# LangGraph 入门指南

## 概述

LangGraph 是一个用于构建有状态、多智能体应用程序的库，它基于状态机的概念，让你能够创建复杂的AI工作流。本文档将从最基础的概念开始，逐步引导你理解LangGraph的核心原理，最终帮助你理解transcript_graph项目中的复杂工作流实现。

## 什么是LangGraph？

### 核心理念

想象一下，你要组织一个 团队完成一个复杂项目：

- 每个团队成员负责特定的任务（**节点**）
- 任务之间有明确的先后顺序（**边**）
- 整个项目有共享的信息和进度（**状态**）
- 根据不同情况选择不同的执行路径（**条件边**）

LangGraph就是这样一个"项目管理系统"，但管理的是AI智能体的协作。

### 与传统编程的区别

**传统编程**：

```python
def process_data():
    step1_result = step1()
    step2_result = step2(step1_result)
    step3_result = step3(step2_result)
    return step3_result
```

**LangGraph方式**：

```python
# 定义状态
class State(TypedDict):
    input_data: str
    step1_result: str
    step2_result: str
    final_result: str

# 定义工作流
workflow = StateGraph(State)
workflow.add_node("step1", step1_function)
workflow.add_node("step2", step2_function)
workflow.add_node("step3", step3_function)

# 定义执行顺序
workflow.add_edge("step1", "step2")
workflow.add_edge("step2", "step3")
```

## 核心概念详解

### 1. 状态（State）

状态是整个工作流的"记忆"，存储所有节点共享的数据。

#### 简单状态示例

```python
from typing import TypedDict

class SimpleState(TypedDict):
    user_input: str      # 用户输入
    processed_data: str  # 处理后的数据
    result: str         # 最终结果
```

#### 状态的特点

- **共享性**：所有节点都能访问和修改状态
- **持久性**：状态在整个工作流执行过程中保持
- **类型安全**：使用TypedDict确保数据类型正确

### 2. 节点（Node）

节点是工作流中的处理单元，每个节点执行特定的任务。

#### 节点函数的基本结构

```python
def my_node(state: SimpleState) -> dict:
    """
    节点函数的标准格式：
    - 输入：当前状态
    - 输出：状态更新字典
    """
    # 从状态中获取数据
    user_input = state["user_input"]
  
    # 执行处理逻辑
    processed = f"处理后的：{user_input}"
  
    # 返回状态更新
    return {"processed_data": processed}
```

#### 节点的类型

**1. 普通节点**：执行固定的处理逻辑

```python
def greeting_node(state: SimpleState) -> dict:
    return {"result": f"你好，{state['user_input']}！"}
```

**2. AI节点**：调用AI模型进行处理

```python
def ai_analysis_node(state: SimpleState) -> dict:
    # 调用AI模型分析用户输入
    analysis = ai_model.analyze(state["user_input"])
    return {"processed_data": analysis}
```

### 3. 边（Edge）

边定义了节点之间的连接关系，决定工作流的执行顺序。

#### 简单边（Simple Edge）

```python
# 固定的执行顺序：A → B → C
workflow.add_edge("node_a", "node_b")
workflow.add_edge("node_b", "node_c")
```

#### 条件边（Conditional Edge）

```python
def route_function(state: SimpleState) -> str:
    """根据状态决定下一个节点"""
    if len(state["user_input"]) > 10:
        return "long_text_processor"
    else:
        return "short_text_processor"

# 添加条件边
workflow.add_conditional_edges(
    "input_analyzer",           # 源节点
    route_function,            # 路由函数
    {
        "long_text_processor": "long_text_processor",
        "short_text_processor": "short_text_processor"
    }
)
```

## 从简单到复杂：逐步学习

### 第一步：最简单的工作流

让我们创建一个最基础的工作流：

```python
from langgraph.graph import StateGraph, END, START
from typing import TypedDict

# 1. 定义状态
class BasicState(TypedDict):
    message: str
    count: int

# 2. 定义节点函数
def add_greeting(state: BasicState) -> dict:
    return {"message": f"你好！{state['message']}"}

def add_count(state: BasicState) -> dict:
    current_count = state.get("count", 0)
    return {"count": current_count + 1}

# 3. 创建工作流
workflow = StateGraph(BasicState)

# 4. 添加节点
workflow.add_node("greeting", add_greeting)
workflow.add_node("counter", add_count)

# 5. 定义执行路径
workflow.add_edge(START, "greeting")
workflow.add_edge("greeting", "counter")
workflow.add_edge("counter", END)

# 6. 编译工作流
app = workflow.compile()

# 7. 运行工作流
result = app.invoke({"message": "世界", "count": 0})
print(result)  # {'message': '你好！世界', 'count': 1}
```

### 第二步：添加条件分支

```python
class ConditionalState(TypedDict):
    user_input: str
    category: str
    response: str

def categorize_input(state: ConditionalState) -> dict:
    """分类用户输入"""
    text = state["user_input"].lower()
    if "问题" in text or "?" in text:
        category = "question"
    elif "帮助" in text or "help" in text:
        category = "help"
    else:
        category = "general"
  
    return {"category": category}

def handle_question(state: ConditionalState) -> dict:
    return {"response": "我来回答你的问题..."}

def handle_help(state: ConditionalState) -> dict:
    return {"response": "这里是帮助信息..."}

def handle_general(state: ConditionalState) -> dict:
    return {"response": "感谢你的消息！"}

def route_by_category(state: ConditionalState) -> str:
    """根据分类路由到不同处理节点"""
    category = state["category"]
    return f"handle_{category}"

# 创建工作流
workflow = StateGraph(ConditionalState)

# 添加节点
workflow.add_node("categorize", categorize_input)
workflow.add_node("handle_question", handle_question)
workflow.add_node("handle_help", handle_help)
workflow.add_node("handle_general", handle_general)

# 添加边
workflow.add_edge(START, "categorize")

# 添加条件边
workflow.add_conditional_edges(
    "categorize",
    route_by_category,
    {
        "handle_question": "handle_question",
        "handle_help": "handle_help", 
        "handle_general": "handle_general"
    }
)

# 所有处理节点都指向结束
workflow.add_edge("handle_question", END)
workflow.add_edge("handle_help", END)
workflow.add_edge("handle_general", END)

app = workflow.compile()
```

### 第三步：并行处理

LangGraph支持并行处理多个任务，这在transcript_graph项目中被大量使用。

```python
from langgraph.constants import Send

class ParallelState(TypedDict):
    tasks: list[str]
    results: dict[str, str]

def create_parallel_tasks(state: ParallelState) -> list[Send]:
    """创建并行任务"""
    tasks = []
    for i, task in enumerate(state["tasks"]):
        # 为每个任务创建一个Send对象
        task_state = {"task_id": f"task_{i}", "task_content": task}
        tasks.append(Send("process_task", task_state))
  
    return tasks

def process_task(state: dict) -> dict:
    """处理单个任务"""
    task_id = state["task_id"]
    content = state["task_content"]
  
    # 模拟处理
    result = f"处理结果：{content}"
  
    return {"results": {task_id: result}}

def collect_results(state: ParallelState) -> dict:
    """收集所有结果"""
    print(f"收集到 {len(state['results'])} 个结果")
    return {}

# 创建工作流
workflow = StateGraph(ParallelState)

workflow.add_node("create_tasks", create_parallel_tasks)
workflow.add_node("process_task", process_task)
workflow.add_node("collect", collect_results)

workflow.add_edge(START, "create_tasks")

# 条件边用于分发并行任务
workflow.add_conditional_edges(
    "create_tasks",
    lambda state: [Send("process_task", task) for task in state.get("parallel_data", [])],
    ["process_task"]
)

workflow.add_edge("process_task", "collect")
workflow.add_edge("collect", END)
```

## transcript_graph中的LangGraph应用

现在让我们看看transcript_graph项目是如何使用这些概念的：

### 1. 复杂状态管理

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/state.py" mode="EXCERPT">

````python
class TranscriptState(TypedDict):
    """教学逐字稿状态 - 比我们之前的例子复杂得多"""
    messages: List[HumanMessage]                    # 用户输入消息
    router: Annotated[Router, operator.or_]        # 路由状态（带合并逻辑）
    course_info: Annotated[Optional[CourseInfo], operator.or_]  # 课程信息
    teaching_process: Annotated[TeachingProcess, operator.or_]  # 教学流程
    teaching_processes: Annotated[Dict[str, TeachingProcessInfo], operator.or_]  # 教学环节
    current_section: Optional[TeachingProcessInfo] # 当前处理的环节
    final_transcript: Optional[str]                # 最终逐字稿
````

</augment_code_snippet>

**关键特性**：

- **Annotated类型**：使用`operator.or_`实现状态合并
- **复杂数据结构**：包含多层嵌套的数据
- **可选字段**：某些字段在特定阶段才有值

### 2. 智能路由决策

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">

````python
def send_expand_task(state: TranscriptState):
    """根据状态决定下一步任务，一次性分发所有环节的生成任务"""
  
    # 获取教学流程内容
    teaching_process_content = state["teaching_process"].content
  
    # 解析为多个环节
    sections = teaching_process_txt_generate_agent.parse_teaching_process(teaching_process_content)
  
    # 为每个环节创建并行任务
    tasks = []
    for section in sections:
        section_state = {
            "course_info": state["course_info"],
            "teaching_process": state["teaching_process"],
            "current_section": TeachingProcessInfo(
                process_id=section["id"],
                process_title=section["title"],
                content=section["content"]
            )
        }
        tasks.append(Send("STR_00_generate_transcript_section", section_state))
  
    return tasks
````

</augment_code_snippet>

**关键特性**：

- **动态任务创建**：根据解析结果创建不同数量的并行任务
- **状态传递**：每个并行任务都携带必要的状态信息
- **Send机制**：使用LangGraph的Send实现并行分发

### 3. 错误处理和状态管理

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">

````python
@contextmanager
def manage_state_update(stage: str, initial_status: str):
    """管理状态更新的上下文 - 这是一个高级模式"""
    state_update = {}
    try:
        yield state_update
        # 成功时设置成功状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 失败时设置错误状态
        state_update["router"] = Router(
            stage=stage,
            status=f"{initial_status}失败",
            error=str(e)
        )
        raise
````

</augment_code_snippet>

**关键特性**：

- **上下文管理器**：确保状态更新的原子性
- **错误捕获**：自动处理异常并更新状态
- **状态追踪**：记录每个阶段的执行状态

## 理解transcript_graph的工作流

现在你已经掌握了基础概念，让我们理解transcript_graph的完整工作流：

### 执行流程图

```mermaid
graph TD
    A[START] --> B[process_info<br/>解析用户输入]
    B --> C{是否有PDF?}
    C -->|有PDF| D[generate_vision_teaching_process<br/>视觉处理]
    C -->|无PDF| E[generate_txt_teaching_process<br/>文本处理]
    D --> F[send_expand_task<br/>任务分发]
    E --> F
    F --> G[STR_00_generate_transcript_section<br/>并行生成逐字稿]
    G --> H[merge_transcript_sections<br/>合并结果]
    H --> I[END]
```

### 关键理解点

1. **状态驱动**：整个流程由状态变化驱动，而不是函数调用
2. **条件分支**：根据是否有PDF选择不同的处理路径
3. **并行处理**：多个教学环节同时生成逐字稿
4. **状态合并**：使用`operator.or_`自动合并状态更新
5. **错误处理**：每个节点都有统一的错误处理机制

## 下一步学习

现在你已经理解了LangGraph的基础概念，建议按以下顺序继续学习：

1. **重新阅读** [工作流图详解](04-工作流图详解.md) - 现在你应该能理解其中的技术细节
2. **深入理解** [状态管理详解](02-状态管理详解.md) - 了解复杂状态的设计原理
3. **学习实现** [处理器组件详解](03-处理器组件详解.md) - 看看节点函数是如何实现的

## 总结

LangGraph的核心思想是：

- **状态机模式**：用状态管理复杂的数据流
- **节点化处理**：将复杂逻辑分解为独立的处理单元
- **灵活路由**：根据状态动态决定执行路径
- **并行能力**：支持高效的并行处理

掌握了这些概念，你就能理解transcript_graph项目中看似复杂的工作流实现，实际上都是这些基础概念的组合和扩展。

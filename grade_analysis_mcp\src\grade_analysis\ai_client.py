"""Grade Analysis AI Client Module

基于 OpenAI 兼容接口调用 DashScope deepseek-v3 模型实现双 AI 客户端系统（Analyst + Reporter）。
参考 transcript_generator_mcp 和 picturebook_generator_mcp 的实现模式。
"""
import json
import time
from typing import Optional, Tu<PERSON>, AsyncGenerator
from openai import AsyncOpenAI

from .config import GradeAnalysisConfig
from .logger import get_logger
from .models import ModelUsageInfo


class BaseOpenAIClient:
    """封装与 OpenAI 兼容接口（DashScope）交互的通用逻辑"""

    def __init__(self, config: GradeAnalysisConfig):
        """
        初始化 OpenAI 兼容客户端。

        Args:
            config (GradeAnalysisConfig): 应用配置实例。
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.client = self.init_openai_client()

    def init_openai_client(self) -> AsyncOpenAI:
        """
        根据配置初始化 OpenAI 兼容的 API 客户端。
        """
        try:
            client = AsyncOpenAI(
                api_key=self.config.openai_api_key,
                base_url=self.config.openai_base_url
            )
            self.logger.info("OpenAI 兼容客户端初始化成功")
            return client
        except Exception as e:
            self.logger.error(f"初始化 OpenAI 兼容客户端失败: {str(e)}")
            raise
    
    def _calculate_usage_info(
        self,
        model_name: str,
        input_tokens: int,
        output_tokens: int
    ) -> ModelUsageInfo:
        """
        计算模型用量信息和成本估算。

        Args:
            model_name (str): 模型名称
            input_tokens (int): 输入 Token 数量
            output_tokens (int): 输出 Token 数量

        Returns:
            ModelUsageInfo: 用量信息对象
        """
        # DashScope deepseek-v3 定价（元/千token）
        input_price_per_1k = 0.002
        output_price_per_1k = 0.008

        cost_estimate = (
            (input_tokens / 1000) * input_price_per_1k +
            (output_tokens / 1000) * output_price_per_1k
        )

        return ModelUsageInfo(
            vendor="dashscope",
            model_name=model_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            cost_estimate=cost_estimate
        )


class AnalystClient(BaseOpenAIClient):
    """负责生成分析代码的 AI 客户端"""

    async def generate_analysis_code(
        self,
        user_request: str,
        data_string: str
    ) -> Tuple[str, ModelUsageInfo]:
        """
        调用 deepseek-v3 模型生成分析代码。

        Args:
            user_request (str): 用户的分析要求
            data_string (str): 成绩数据字符串

        Returns:
            Tuple[str, ModelUsageInfo]: 生成的代码和用量信息
        """
        self.logger.info("开始生成分析代码")
        
        system_prompt = """你是一个专业的数据分析师，擅长使用 Python 和 Pandas 进行数据分析。

你的任务是：
1. 理解用户的分析需求
2. 解析提供的数据格式
3. 生成安全、高效的 Python 代码来完成分析
4. 确保代码能够处理常见的数据问题（如缺失值、格式错误等）

代码要求：
- 使用 pandas 库进行数据处理
- 代码必须是完整的、可执行的
- 包含适当的错误处理
- 输出结果应该是结构化的（字典或 JSON 格式）
- 不要使用任何危险的函数（如 exec, eval, os.system 等）
- 不要访问网络或文件系统

请只返回 Python 代码，不要包含其他解释文字。"""

        user_prompt = f"""请根据以下信息生成数据分析代码：

用户需求：{user_request}

数据内容：
{data_string}

请生成完整的 Python 代码来完成这个分析任务。"""

        try:
            response = await self.client.chat.completions.create(
                model=self.config.analyst_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )

            generated_code = response.choices[0].message.content

            # 计算用量信息
            usage = response.usage
            usage_info = self._calculate_usage_info(
                self.config.analyst_model,
                usage.prompt_tokens,
                usage.completion_tokens
            )

            self.logger.info("分析代码生成完成")
            return generated_code, usage_info
                
        except Exception as e:
            self.logger.error(f"生成分析代码失败: {str(e)}")
            raise ValueError(f"生成分析代码失败: {str(e)}")


class ReporterClient(BaseOpenAIClient):
    """负责生成分析报告的 AI 客户端"""

    async def generate_analysis_report(
        self,
        user_request: str,
        analysis_results: dict
    ) -> Tuple[str, ModelUsageInfo]:
        """
        调用 deepseek-v3 模型生成分析报告。

        Args:
            user_request (str): 用户的原始分析要求
            analysis_results (dict): 代码执行的结果数据

        Returns:
            Tuple[str, ModelUsageInfo]: 生成的报告和用量信息
        """
        self.logger.info("开始生成分析报告")
        
        system_prompt = """你是一个专业的数据分析报告撰写专家，擅长将数据分析结果转化为通俗易懂的报告。

你的任务是：
1. 理解用户的原始分析需求
2. 解读提供的分析结果数据
3. 撰写一份结构清晰、内容准确的分析报告
4. 使用 Markdown 格式输出

报告要求：
- 使用 Markdown 格式
- 结构清晰，包含标题、摘要、详细分析、结论等部分
- 语言通俗易懂，适合非技术人员阅读
- 突出关键发现和洞察
- 如果有异常或需要注意的地方，要明确指出
- 包含具体的数据支撑

请确保报告内容准确、客观、有价值。"""

        user_prompt = f"""请根据以下信息生成数据分析报告：

用户原始需求：{user_request}

分析结果数据：
{json.dumps(analysis_results, ensure_ascii=False, indent=2)}

请生成一份完整的 Markdown 格式分析报告。"""

        try:
            response = await self.client.chat.completions.create(
                model=self.config.reporter_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )

            generated_report = response.choices[0].message.content

            # 计算用量信息
            usage = response.usage
            usage_info = self._calculate_usage_info(
                self.config.reporter_model,
                usage.prompt_tokens,
                usage.completion_tokens
            )

            self.logger.info("分析报告生成完成")
            return generated_report, usage_info
                
        except Exception as e:
            self.logger.error(f"生成分析报告失败: {str(e)}")
            raise ValueError(f"生成分析报告失败: {str(e)}")

# MCP 服务器开发规范

## 1. 概述

本文档旨在为基于 **FastMCP** 框架构建大模型工具服务（MCP Server）提供一套全面的开发规范和最佳实践。遵循此规范能够帮助团队构建出**健壮、高效、可观测且易于维护**的 MCP 服务，并为最终用户提供卓越的交互体验。

[FastMCP 官方文档](https://gofastmcp.com/getting-started/welcome)。

---

## 2. 核心设计原则

### 2.1. 接口协议：MCP 优先

**原则**：所有工具服务必须优先以 **MCP (Model Context Protocol)** 作为标准接口协议对外提供。

**说明**：MCP 提供了比传统 RESTful API 更强大的能力，特别是其对双向流、上下文和元数据的原生支持，是构建复杂、长任务 AI 应用的理想选择。应避免将 MCP 服务降级为简单的 HTTP 请求/响应模式。

### 2.2. 用户体验：实时反馈与流式输出

**原则**：为了保证用户体验，工具在执行长耗时任务时，**必须**保证中间过程状态的告知，并尽可能使用流式输出。

**说明**：AI 应用的执行过程往往耗时不透明，长时间的等待会严重影响用户体验。开发者有责任将任务的每一个关键阶段（如：任务开始、数据处理中、模型调用中、结果生成中、任务完成）通过 MCP 的 `context.info()` 通道实时报告给客户端。对于文本生成等场景，必须以流式（Streaming）方式逐块返回内容。

### 2.3. 可观测性：日志与用量

**原则**：为了便于问题排查和成本控制，服务必须具备完整的日志输出和模型用量报告机制。

**说明**：
-   **日志**：严禁使用 `print()` 函数输出任何日志信息。必须使用 Python 内置的 `logging` 模块，并配置 `FileHandler` 将日志持久化到文件中。日志应包含时间戳、日志级别、模块名和详细信息。
-   **模型用量**：每次调用大模型（LLM、文生图等）后，必须立即将本次调用的详细用量信息（厂商、模型名称、输入/输出Token等）通过 `context.info()` 实时返回给客户端。

### 2.4. 健壮性：统一错误处理

**原则**：服务在遇到可预见的或意外的错误时，绝不能直接崩溃或返回不明确的HTTP 500错误。必须捕获异常，并通过 `context.error()` 向客户端发送结构清晰的错误报告。

**说明**：统一的错误处理是服务健壮性的核心。客户端需要明确知道任务失败的原因，以便进行重试或提示用户。错误报告应包含错误类型、错误信息和相关的上下文数据。

### 2.5. 安全性：密钥管理

**原则**：API Key、数据库密码等所有敏感凭证，严禁以任何形式硬编码在代码中或提交到版本控制系统。

**说明**：必须将密钥存储在环境变量中，并通过 `.env` 文件进行本地开发管理。`.env` 文件本身**必须**被添加到 `.gitignore` 文件中，以防意外泄露。

### 2.6. 代码质量：遵循 PEP 8

**原则**：所有 Python 代码的编写与格式化，原则上必须严格符合 **PEP 8** 编码规范。

**说明**：统一的编码风格是保证代码可读性和可维护性的基石。推荐使用 `black` 进行代码格式化，使用 `ruff` 或 `flake8` 进行代码风格检查。

---

## 3. 标准项目结构

一个规范的 MCP Server 项目应遵循以下目录结构，以 `picturebook_generator_mcp` 为例：

```
project-name/
├── README.md                    # 项目说明
├── pyproject.toml               # 项目依赖与配置 (或 requirements.txt)
├── .env.example                 # 环境变量示例文件
├── main.py                      # MCP服务器入口
├── src/                         # 核心源码目录
│   └── project_name/
│       ├── __init__.py          # 模块初始化
│       ├── config.py            # 应用配置管理
│       ├── models.py            # Pydantic 数据模型
│       ├── services.py          # 核心服务与业务逻辑 (或 generators.py)
│       └── logger.py            # 日志系统配置
├── tests/                       # 测试目录
│   └── test_mcp_server.py       # MCP 客户端测试
└── outputs/                     # (可选) 输出文件存放目录
```

---

## 4. 关键实现规范

### 4.1. 实时通信 (`context.info`)

通过 FastMCP 框架的依赖注入功能，在工具函数签名中声明 `ctx: Context` 即可获得上下文对象，用于实时通信。

**实现**：
1.  **定义标准消息体**：使用 Pydantic 模型定义标准的消息结构，增加可预测性。
2.  **发送通知**：在业务流程的关键节点，调用 `await ctx.info(message.model_dump())`。

**示例 (`services.py`)**:
```python
from fastmcp import Context
from .models import ProgressUpdate # Pydantic Model

class PictureBookGeneratorService:
    async def generate_picture_book(self, ..., ctx: Context):
        # 1. 报告任务开始
        await ctx.info(ProgressUpdate(status="started", message="绘本生成任务已开始").model_dump())

        # 2. 流式返回文本
        async for chunk in story_agent.stream_story(...):
             await ctx.info(ProgressUpdate(status="streaming_text", chunk=chunk).model_dump())
        
        # 3. 报告图片生成状态和结果
        for page in pages:
            await ctx.info(ProgressUpdate(status="image_gen_started", page_id=page.id).model_dump())
            image_urls = await image_agent.generate(...)
            await ctx.info(ProgressUpdate(status="image_gen_completed", urls=image_urls).model_dump())

        # 4. 报告任务结束
        await ctx.info(ProgressUpdate(status="completed", message="任务完成").model_dump())
```

### 4.2. 日志系统 (`logger.py`)

**目标**：配置一个同时向控制台和文件输出日志的记录器。

**示例 (`logger.py`)**:
```python
import logging
import sys
from logging.handlers import RotatingFileHandler

def setup_logger():
    """配置全局日志记录器"""
    logger = logging.getLogger("mcp_server")
    logger.setLevel(logging.INFO)

    # 防止重复添加 handler
    if logger.hasHandlers():
        return logger

    # 格式化
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 控制台 Handler
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    # 文件 Handler (例如，轮转文件)
    file_handler = RotatingFileHandler("logs/server.log", maxBytes=10*1024*1024, backupCount=5)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

# 在应用入口处调用
# from .logger import setup_logger
# logger = setup_logger()
# logger.info("服务启动")
```
在 `main.py` 或服务模块的入口处调用 `setup_logger()`，后续在任何模块中通过 `logging.getLogger("mcp_server")` 即可获取并使用该记录器。

### 4.5. 统一错误处理 (`ctx.error`)

在 `try...except` 块中捕获异常，并使用 `ctx.error()` 进行报告。

**示例 (`services.py`)**:
```python
import httpx
from fastmcp import Context

# ... 在服务方法中 ...
try:
    # ... 可能发生错误的代码 ...
    response = await client.post(...)
    response.raise_for_status() # 如果请求失败则抛出异常
except httpx.HTTPStatusError as e:
    # 捕获预期的HTTP错误
    error_payload = {
        "type": "api_request_failed",
        "message": f"调用上游API失败: {e.response.status_code}",
        "details": str(e)
    }
    await ctx.error(error_payload)
    # 可以选择向上抛出或直接返回
    raise
except Exception as e:
    # 捕获所有其他意外错误
    error_payload = {
        "type": "unexpected_server_error",
        "message": "服务内部发生未知错误",
        "details": str(e)
    }
    await ctx.error(error_payload)
    raise

```

### 4.3. 模型用量报告

**目标**：在模型调用后，立即将成本信息返回给客户端。

**实现**：
1.  在 `models.py` 中定义 `ModelUsageInfo` Pydantic 模型。
2.  让执行模型调用的函数或方法返回 `(result, usage_info)`。
3.  在主服务流程中接收用量信息，并通过 `ctx.info()` 发送。

**示例 (`services.py`)**:
```python
from .models import ModelUsageInfo

# ... 在服务方法中 ...
story_text, story_usage = await self.story_agent.generate(...)
await ctx.info({
    "status": "model_usage",
    "usage": story_usage.model_dump()
})

# ... 循环中 ...
image_paths, image_urls, image_usage = await self.image_agent.generate_images(...)
await ctx.info({
    "status": "model_usage",
    "usage": image_usage.model_dump()
})
```

### 4.4. 配置管理 (`config.py`)

**目标**：将敏感信息（如API Key）和可变配置（如模型名称）与代码分离。

**实现**：使用 `python-dotenv` 库读取 `.env` 文件。

**示例 (`config.py`)**:
```python
import os
from dotenv import load_dotenv

# 在项目根目录寻找 .env 文件
load_dotenv()

class Settings:
    DOUBAO_API_KEY: str = os.getenv("DOUBAO_API_KEY")
    DOUBAO_STORY_MODEL: str = os.getenv("DOUBAO_STORY_MODEL", "default-model-name")
    OUTPUT_DIR: str = os.getenv("OUTPUT_DIR", "outputs")

settings = Settings()
```

---

## 5. 测试规范 (`tests/`)

测试是保证服务质量的关键。测试脚本 (`test_mcp_server.py`) 应至少覆盖以下场景：

1.  **连接与调用**：能成功连接到 MCP 服务器并调用目标工具。
2.  **最终结果验证**：最终返回的 JSON 结果结构正确、内容符合预期。
3.  **实时状态验证**：能够接收并解析 `context.info()` 发送的各类状态消息（如 `started`, `completed`）。
4.  **流式内容验证**：能够正确拼接流式返回的内容，并验证其完整性。
5.  **用量报告验证**：能接收并解析模型用量报告。
6.  **错误处理验证**：能够模拟并触发服务端的错误，并验证客户端是否收到了格式正确的错误信息。

---

## 6. 工程化规范

### 6.1. 依赖管理

**目标**：确保开发、测试和生产环境的依赖一致性，实现可复现的构建。

**规范**：
-   **使用 `pyproject.toml`**: 推荐使用 `poetry` 或 `pdm` 等现代化的包管理工具，它们通过 `pyproject.toml` 文件管理项目元数据和依赖。
-   **避免宽泛版本**：在声明依赖时，应指定明确的版本范围（如 `fastmcp>=2.0,<3.0`），避免使用 `*` 或无限制的版本，防止上游库的不兼容更新破坏项目。

### 6.2. 代码文档 (Docstrings)

**目标**：提升代码的可读性和可维护性，方便团队协作和未来迭代。

**规范**：
-   所有公开的模块（文件顶部）、类、方法和函数都**必须**包含文档字符串。
-   推荐使用 **Google 风格**或 **Numpy/Scipy 风格**的 Docstrings，因为它们结构清晰，易于被各类文档生成工具（如 Sphinx）解析。
-   文档字符串应至少包含：
    -   对功能的简要描述。
    -   对每个参数的说明（`Args:`），包括其类型和含义。
    -   对返回值的说明（`Returns:`），包括其类型和含义。
    -   （可选）如果函数可能抛出特定异常，应进行说明（`Raises:`）。

**示例**:
```python
def calculate_area(width: float, height: float) -> float:
    """计算矩形的面积。

    Args:
        width (float): 矩形的宽度，必须为正数。
        height (float): 矩形的高度，必须为正数。

    Returns:
        float: 计算出的矩形面积。
    
    Raises:
        ValueError: 如果宽度或高度为非正数。
    """
    if width <= 0 or height <= 0:
        raise ValueError("宽度和高度必须为正数")
    return width * height
``` 
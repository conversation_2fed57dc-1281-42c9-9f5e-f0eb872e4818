"""教学逐字稿生成器配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class TranscriptGeneratorConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- AI服务配置 ---
    ai_api_key: str = Field(description="AI服务API密钥")
    ai_provider: str = Field(
        default="zhipuai",
        description="AI服务提供商（zhipuai, siliconflow, dashscope等）"
    )
    ai_base_url: Optional[str] = Field(
        default=None,
        description="AI服务基础URL（可选）"
    )
    ai_model_name: str = Field(
        default="glm-4-flash",
        description="AI模型名称"
    )
    ai_timeout: int = Field(
        default=60,
        description="AI服务请求超时时间（秒）"
    )
    ai_max_retries: int = Field(
        default=3,
        description="AI服务请求最大重试次数"
    )

    
    # --- 生成参数配置 ---
    max_sections: int = Field(default=6, description="最大教学环节数量")
    parallel_limit: int = Field(default=5, description="并行处理限制")
    
    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的逐字稿文件存放的根目录")
    
    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @classmethod
    def from_env(cls) -> "TranscriptGeneratorConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如AI_API_KEY）。

        Returns:
            TranscriptGeneratorConfig: 一个包含所有最终配置值的实例。
        """
        ai_api_key = os.getenv("AI_API_KEY")
        if not ai_api_key:
            raise ValueError("请设置环境变量 AI_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上一级，因为文件现在在src/目录下）
        current_dir = os.path.dirname(os.path.abspath(__file__))  # src目录
        project_root = os.path.dirname(current_dir)  # 项目根目录
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            ai_api_key=ai_api_key,
            ai_provider=os.getenv("AI_PROVIDER", "zhipuai"),
            ai_base_url=os.getenv("AI_BASE_URL"),
            ai_model_name=os.getenv("AI_MODEL_NAME", "glm-4-flash"),
            ai_timeout=int(os.getenv("AI_TIMEOUT", "60")),
            ai_max_retries=int(os.getenv("AI_MAX_RETRIES", "3")),

            max_sections=int(os.getenv("MAX_SECTIONS", "6")),
            parallel_limit=int(os.getenv("PARALLEL_LIMIT", "5")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        )

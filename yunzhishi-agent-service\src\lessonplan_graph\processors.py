"""教案生成的处理器实现。"""
from typing import Dict, List, Optional, TypedDict, Generator, Any, Union, Iterator, AsyncIterator
from zhipuai import <PERSON>hipuA<PERSON>
from openai import OpenAI
import base64
from pathlib import Path
import fitz  # PyMuPDF
import os
import re
import json
from shared.utils import <PERSON><PERSON>, OSSStorageManager, manage_pdf_conversion, manage_pdf_document
from shared.decorators import traceable
from shared.configuration import LessonPlanConfiguration 
from lessonplan_graph.state import CourseInfo, TeachingProcessInfo
from contextlib import contextmanager
from shared.model import CustomChatZhipuAI, CustomChatOpenAI, manage_txt_llm_call, manage_vision_llm_call, create_llm_client
from lessonplan_graph.prompts import (
    OUTLINE_TXT_SYSTEM_PROMPT,OUTLINE_VISION_SYSTEM_PROMPT,OUTLINE_VISION_BATCH_PROMPT,OUTLINE_VISION_SUMMARY_PROMPT,
    PROCESS_EXPAND_SYSTEM_PROMPT,OBJECTIVES_SYSTEM_PROMPT,KEYPOINTS_SYSTEM_PROMPT,METHODS_SYSTEM_PROMPT,
    ACTIVITIES_SYSTEM_PROMPT,STUDENT_ANALYSIS_SYSTEM_PROMPT,BLACKBOARD_DESIGN_SYSTEM_PROMPT,
    CUSTOM_ELEMENT_SYSTEM_PROMPT,MODIFY_ELEMENT_SYSTEM_PROMPT,OUTLINE_MODIFY_SYSTEM_PROMPT,MIND_MAP_SYSTEM_PROMPT,
    TEACHING_METHODOLOGY_PROMPT, STUDENT_ANALYSIS_WITH_METHODOLOGY_PROMPT
)


def format_course_info(course_info: CourseInfo) -> str:
    """格式化课程信息。
    
    Args:
        course_info: CourseInfo对象，包含课程相关信息
        
    Returns:
        str: 格式化后的课程信息字符串
    """
    info_items = []
    
    if course_info.lesson_coursename:
        info_items.append(f"- 课程名称：{course_info.lesson_coursename}")
    if course_info.lesson_unitname:
        info_items.append(f"- 章节课时名称：{course_info.lesson_unitname}")
    if course_info.lesson_count:
        info_items.append(f"- 课时数：{course_info.lesson_count}")
    if course_info.lesson_grade:
        info_items.append(f"- 适用年级：{course_info.lesson_grade}")
    if course_info.lesson_semester:
        info_items.append(f"- 学期：{course_info.lesson_semester}")
        
    return "\n".join(info_items) if info_items else ""

class FileProcessAgent:
    """文件处理智能体，负责处理PDF文件并提取内容"""
    def __init__(self, config: LessonPlanConfiguration):
        # 始终使用zhipuai的API key
        zhipuai_key = os.getenv("ZHIPUAI_API_KEY")
        if not zhipuai_key:
            raise ValueError("请设置环境变量 ZHIPUAI_API_KEY 用于文件处理")
        self.client = ZhipuAI(api_key=zhipuai_key)
        self.config = config
        self.file_io = FileIO(config)
        self.storage = OSSStorageManager(config)
        self.pdf_cache = {}  # 用于缓存PDF临时文件路径
        
    @contextmanager
    def _manage_uploaded_file(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Generator[str, None, None]:
        """管理上传的文件生命周期，确保文件被正确删除"""
        # 如果指定了页面范围，创建临时PDF文件
        temp_pdf_path = pdf_path
        if start_page is not None and end_page is not None:
            with fitz.open(pdf_path) as doc:
                # 验证页码范围
                if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                    raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                
                # 创建新的PDF文档
                new_doc = fitz.open()
                for i in range(start_page - 1, end_page):
                    new_doc.insert_pdf(doc, from_page=i, to_page=i)
                
                # 保存临时文件
                temp_pdf_path = pdf_path.replace('.pdf', f'_temp_{start_page}_{end_page}.pdf')
                new_doc.save(temp_pdf_path)
                new_doc.close()
        
        try:
            file_object = self.client.files.create(
                file=Path(temp_pdf_path),
                purpose="file-extract"
            )
            file_id = file_object.id
            yield file_id
        finally:
            if file_id:
                self.client.files.delete(file_id=file_id)
            # 如果创建了临时文件，删除它
            if temp_pdf_path != pdf_path:
                try:
                    os.remove(temp_pdf_path)
                except Exception as e:
                    print(f"清理临时文件失败: {str(e)}")
    
    @traceable
    def process_pdf(self, object_name: str, start_page: Optional[int] = None, end_page: Optional[int] = None, lesson_pdf_already: Optional[int] = None) -> Dict[str, str]:
        """处理PDF文件并提取内容
        
        Args:
            object_name: PDF文件名
            start_page: 起始页码（从1开始）
            end_page: 结束页码（包含）
            lesson_pdf_already: PDF来源（1=系统教材，2=用户自传）
            
        Returns:
            Dict[str, str]: 包含处理后的内容和类型
        """
        # 生成缓存键
        cache_key = f"{object_name}_{start_page or ''}_{end_page or ''}"
        
        # 检查是否已有缓存的临时文件
        if cache_key in self.pdf_cache and os.path.exists(self.pdf_cache[cache_key]):
            temp_path = self.pdf_cache[cache_key]
            # 使用缓存的临时文件
            with self._manage_uploaded_file(temp_path, start_page, end_page) as file_id:
                pdf_content = json.loads(
                    self.client.files.content(file_id=file_id).content
                )["content"]
                
                result = {
                    "lesson_pdf_content": pdf_content.strip(),
                    "lesson_pdf_type": "text" if pdf_content.strip() else "vision",
                }
                
                # 如果是视觉类型的PDF且是用户自传的，将临时文件路径添加到结果中
                if result["lesson_pdf_type"] == "vision" and lesson_pdf_already == 2:
                    result["temp_pdf_path"] = temp_path
                
                return result
        
        # 没有缓存，从OSS下载
        with self.storage.get_temp_file(object_name) as temp_path:
            with self._manage_uploaded_file(temp_path, start_page, end_page) as file_id:
                pdf_content = json.loads(
                    self.client.files.content(file_id=file_id).content
                )["content"]
                
                result = {
                    "lesson_pdf_content": pdf_content.strip(),
                    "lesson_pdf_type": "text" if pdf_content.strip() else "vision",
                }
                
                # 如果是视觉类型的PDF且是用户自传的，保留临时文件
                if result["lesson_pdf_type"] == "vision" and lesson_pdf_already == 2:
                    # 复制临时文件到可持久保存的位置
                    persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                    import shutil
                    shutil.copy2(temp_path, persistent_temp_path)
                    # 缓存临时文件路径
                    self.pdf_cache[cache_key] = persistent_temp_path
                    result["temp_pdf_path"] = persistent_temp_path
                
                return result

class TeachingProcessOutlineTxtAgent:
    """教学流程大纲生成智能体，负责基于文本内容生成教学流程大纲"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_outline(self, course_info: CourseInfo, user_habits: str = "", user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学流程大纲。

        Args:
            course_info: 课程信息
            user_habits: 用户习惯和偏好
            user_id: 用户ID
            session_id: 会话ID

        Returns:
            str: 生成的教学流程大纲
        """
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        
        # 准备提示词
        prompt = OUTLINE_TXT_SYSTEM_PROMPT.format(
            course_info_formatted=course_info_formatted
        )
        
        if course_info.lesson_pdf_content:
            prompt += f"\n\n参考资料：\n{course_info.lesson_pdf_content}"
            
        # 如果有用户习惯/教学法，添加到提示词中
        if user_habits:
            prompt += f"\n\n用户习惯和偏好：\n{user_habits}"
                
        prompt += "\n请根据以上要求设计教学流程大纲。仅输出大纲内容，不要包含任何其他说明。"
        
        self.llm.push_trace_info("", "教学流程大纲文本生成")
        
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as outline:
            self.file_io.write_file("teaching_process_outline.md", outline)
            return outline

class TeachingProcessOutlineVisionAgent:
    """教学流程大纲视觉生成智能体，负责基于PDF图像内容生成教学流程大纲"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.temp_dir = Path(config.temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        self.file_io = FileIO(config)
        self.storage = OSSStorageManager(config)
        
        # 根据不同的视觉模型提供商初始化客户端
        if config.vision_model_provider == "zhipuai":
            zhipuai_key = config.vision_api_key
            self.client = ZhipuAI(api_key=zhipuai_key)
            self.use_openai_compatible = False
        elif config.vision_model_provider in ["siliconflow", "dashscope"]:
            # 使用OpenAI兼容接口的提供商（SiliconFlow和DashScope）
            self.client=OpenAI(
                api_key=config.vision_api_key,
                base_url=config.vision_model_base_url
            )
            self.use_openai_compatible = True
        else:
            raise ValueError(f"不支持的视觉模型提供商: {config.vision_model_provider}")

    @traceable
    def _convert_pdf_to_images(self, object_name: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> List[str]:
        """将PDF文件转换为图片并返回base64编码列表"""
        with manage_pdf_conversion(self.temp_dir) as img_bases:
            with self.storage.get_temp_file(object_name) as temp_path:
                with manage_pdf_document(temp_path) as doc:
                    # 确定要处理的页面范围
                    if start_page is None:
                        start_page = 1
                    if end_page is None:
                        end_page = doc.page_count
                        
                    # 验证页码范围
                    if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                        raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                    
                    # 只处理指定范围内的页面
                    for page_num in range(start_page - 1, end_page):
                        page = doc.load_page(page_num)
                        pix = page.get_pixmap()
                        img_path = self.temp_dir / f"page_{page_num}.png"
                        pix.save(str(img_path))
                        
                        with open(img_path, 'rb') as img_file:
                            img_base = base64.b64encode(img_file.read()).decode('utf-8')
                            img_bases.append(img_base)
                            
                return img_bases

    def _call_vision_model(self, img_bases: List[str], prompt: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """调用视觉模型
        
        Args:
            img_bases: base64编码的图片列表
            prompt: 提示词
            
        Returns:
            str: 模型生成的内容
        """
        # 构建消息内容
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt}
                ] + [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{img_base}"}
                    }
                    for img_base in img_bases
                ]
            }
        ]
        
        # 使用视觉模型管理器调用模型
        with manage_vision_llm_call(self.client, messages, self.config, trace_desc="教学流程大纲视觉生成", user_id=user_id, session_id=session_id) as content:
            return content
            
    @traceable
    def generate_outline(self, course_info: CourseInfo, user_habits: str = "", user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成基于图片的教学流程大纲。

        Args:
            course_info: 课程信息
            user_habits: 用户习惯和偏好
            user_id: 用户ID
            session_id: 会话ID

        Returns:
            str: 生成的教学流程大纲
        """
        img_bases = []
        cached_pdf_path = None  # 记录缓存的PDF路径，用于后续清理
        pdf_cache_filename = None  # 记录缓存文件的名称
        
        # 判断是用户自传教材还是系统教材
        if course_info.lesson_pdf_already == 2:  # 用户自传教材
            if course_info.lesson_pdf_temp_path and os.path.exists(course_info.lesson_pdf_temp_path):
                # 使用已缓存的临时文件
                pdf_path = course_info.lesson_pdf_temp_path
                cached_pdf_path = pdf_path  # 记录临时文件路径，用于后续删除
                pdf_cache_filename = os.path.basename(cached_pdf_path)
                print(f"使用缓存的PDF文件: {cached_pdf_path}")
                
                # 直接使用临时文件
                with manage_pdf_conversion(self.temp_dir) as img_bases:
                    with manage_pdf_document(pdf_path) as doc:
                        # 确定要处理的页面范围
                        start_page = course_info.lesson_pdf_start_page or 1
                        end_page = course_info.lesson_pdf_end_page or doc.page_count
                        
                        # 验证页码范围
                        if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                            raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                        
                        # 只处理指定范围内的页面
                        for page_num in range(start_page - 1, end_page):
                            page = doc.load_page(page_num)
                            pix = page.get_pixmap()
                            img_path = self.temp_dir / f"page_{page_num}.png"
                            pix.save(str(img_path))
                            
                            with open(img_path, 'rb') as img_file:
                                img_base = base64.b64encode(img_file.read()).decode('utf-8')
                                img_bases.append(img_base)
        elif course_info.lesson_pdf_path:
            # 如果没有缓存的临时文件，使用常规方法从OSS下载并转换PDF页面为图片
            img_bases = self._convert_pdf_to_images(
                course_info.lesson_pdf_path,
                start_page=course_info.lesson_pdf_start_page,
                end_page=course_info.lesson_pdf_end_page
            )
        elif course_info.lesson_pdf_already == 1:  # 系统教材
            # 构建缩略图路径
            stage = course_info.lesson_stage
            coursename = course_info.lesson_coursename
            version = course_info.lesson_version or "tongbian"  # 默认使用通编版本
            grade = course_info.lesson_grade
            semester = course_info.lesson_semester
            
            # 构建缩略图基础路径
            thumbnail_base_path = f"{stage}/{coursename}/{version}/{grade}/{semester}/thumbnail/{stage}_{coursename}_{version}_{grade}_{semester}_thumbnail"
            
            # 获取页面范围
            start_page = course_info.lesson_pdf_start_page or 1
            end_page = course_info.lesson_pdf_end_page
            
            # 读取缩略图
            page_no = start_page
            while True:
                formatted_page_no = f"{page_no:03d}"
                thumbnail_object_name = f"{thumbnail_base_path}_{formatted_page_no}.jpg"
                
                # 检查缩略图是否存在
                if not self.storage.check_file_exists(thumbnail_object_name):
                    if page_no == start_page:
                        print(f"警告：系统中不存在教材缩略图 {thumbnail_object_name}")
                    break
                
                try:
                    with self.storage.get_temp_file(thumbnail_object_name, suffix='.jpg') as temp_path:
                        with open(temp_path, 'rb') as img_file:
                            img_base = base64.b64encode(img_file.read()).decode('utf-8')
                            img_bases.append(img_base)
                except Exception as e:
                    print(f"警告：读取教材缩略图 {thumbnail_object_name} 失败: {str(e)}")
                    break
                
                # 尝试下一个页面
                page_no += 1
                if end_page is not None and page_no > end_page:
                    break
        else:
            raise ValueError("未提供PDF文件或系统教材信息")
        
        # 如果没有获取到任何图片，抛出错误
        if not img_bases:
            raise ValueError("未能获取任何教材图片")
        
        try:
            # 如果图片数量不超过5张，使用原有逻辑
            if len(img_bases) <= 5:
                # 格式化课程信息
                course_info_formatted = format_course_info(course_info)
                
                # 准备提示词
                prompt = OUTLINE_VISION_SYSTEM_PROMPT.format(
                    course_info_formatted=course_info_formatted
                )
                
                # 如果有用户习惯，添加到提示词中
                if user_habits:
                    prompt += f"\n\n用户习惯：\n{user_habits}"
                    
                prompt += "\n请根据教材图片内容，按照以上要求设计教学流程的大纲。仅输出大纲内容，不要包含任何其他说明。"
                
                outline = self._call_vision_model(img_bases, prompt, user_id, session_id)
            else:
                # 处理超过5张图片的情况
                batch_results = []
                total_pages = len(img_bases)
                
                # 将图片分成最多5张一组进行处理
                for i in range(0, total_pages, 5):
                    batch = img_bases[i:i+5]
                    batch_num = i // 5 + 1
                    
                    # 格式化课程信息
                    course_info_formatted = format_course_info(course_info)
                    
                    # 处理当前批次
                    prompt = OUTLINE_VISION_BATCH_PROMPT.format(
                        course_info_formatted=course_info_formatted,
                        batch_num=batch_num,
                        total_pages=total_pages,
                        batch_size=len(batch)
                    )
                    
                    batch_outline = self._call_vision_model(batch, prompt, user_id, session_id)
                    batch_results.append(batch_outline)
                
                # 汇总所有批次的结果
                separator = '=' * 50
                batch_contents = f'\n{separator}\n'.join(batch_results)
                
                # 构建汇总提示词
                prompt = OUTLINE_VISION_SUMMARY_PROMPT.format(
                    course_info_formatted=course_info_formatted,
                    batch_count=len(batch_results),
                    separator=separator,
                    batch_contents=batch_contents
                )
                
                # 使用文本模型汇总
                llm = create_llm_client(self.config)
                
                with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as outline:
                    pass
            
            # 保存大纲
            self.file_io.write_file("outline.md", outline)
            return outline
        finally:
            # 清理缓存的临时PDF文件
            if cached_pdf_path and os.path.exists(cached_pdf_path):
                try:
                    # 先关闭所有可能打开的文件句柄
                    import gc
                    gc.collect()  # 强制垃圾回收
                    
                    # 尝试直接删除文件
                    os.remove(cached_pdf_path)
                    print(f"已清理缓存的临时PDF文件: {cached_pdf_path}")
                    
                    # 清理课程信息中的临时路径
                    if hasattr(course_info, 'lesson_pdf_temp_path') and course_info.lesson_pdf_temp_path == cached_pdf_path:
                        course_info.lesson_pdf_temp_path = None
                    
                    # 尝试直接在临时目录中查找并删除所有匹配的缓存文件
                    if pdf_cache_filename:
                        cache_pattern = os.path.join(self.config.temp_dir, f"pdf_cache_*{pdf_cache_filename}*")
                        import glob
                        for cache_file in glob.glob(cache_pattern):
                            try:
                                if os.path.exists(cache_file):
                                    os.remove(cache_file)
                                    print(f"已清理额外的缓存文件: {cache_file}")
                            except Exception as e:
                                print(f"清理额外的缓存文件失败: {cache_file}, 错误: {str(e)}")
                except Exception as e:
                    print(f"清理缓存的临时PDF文件失败: {cached_pdf_path}, 错误: {str(e)}")

class TeachingElementsResult(TypedDict):
    """教学要素结果类型定义"""
    teaching_objectives: str  # 教学目标
    teaching_keypoints: str   # 重点难点
    teaching_studentanalysis: str     # 学情分析
    teaching_methods: str     # 教学方法
    teaching_activities: str  # 课堂活动
    teaching_boarddesign: str         # 教学板书设计

class TeachingelementsGenerateAgent:
    """教学要素生成智能体，负责生成教学目标、重点难点等要素"""
    
    # 定义跟踪信息映射
    TRACE_INFO = {
        'objectives': {'id': 'STR_00', 'desc': '教学目标生成', 'title': '# 一、教学目标\n\n'},
        'keypoints': {'id': 'STR_01', 'desc': '教学重难点生成', 'title': '# 二、教学重点难点\n\n'},
        'studentanalysis': {'id': 'STR_02', 'desc': '教学学情分析生成', 'title': '# 三、教学学情分析\n\n'},
        'methods': {'id': 'STR_03', 'desc': '教学方法生成', 'title': '# 四、教学方法\n\n'},
        'activities': {'id': 'STR_04', 'desc': '教学活动生成', 'title': '# 五、教学活动\n\n'},
        'boarddesign': {'id': 'STR_06', 'desc': '教学板书设计生成', 'title': '# 七、教学板书设计\n\n'}
    }
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_objectives(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学目标"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('objectives')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = OBJECTIVES_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                lesson_plan=lesson_plan
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("objectives.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""
                
    @traceable
    def generate_keypoints(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学重点难点"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('keypoints')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = KEYPOINTS_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                lesson_plan=lesson_plan
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("keypoints.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""
                
    @traceable
    def generate_methods(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学方法"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('methods')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = METHODS_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                lesson_plan=lesson_plan
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("methods.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""
                
    @traceable
    def generate_activities(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学活动"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('activities')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = ACTIVITIES_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                lesson_plan=lesson_plan
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("activities.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""

    @traceable
    def generate_studentanalysis(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成学情分析"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('studentanalysis')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 根据是否有教学法选择不同的提示词
            if course_info.lesson_methodology and course_info.lesson_methodology.lower() not in ['auto', 'none', '']:
                # 使用带教学法的提示词
                prompt = STUDENT_ANALYSIS_WITH_METHODOLOGY_PROMPT.format(
                    course_info_formatted=course_info_formatted,
                    lesson_plan=lesson_plan,
                    methodology=course_info.lesson_methodology
                )
            else:
                # 使用标准提示词
                prompt = STUDENT_ANALYSIS_SYSTEM_PROMPT.format(
                    course_info_formatted=course_info_formatted,
                    lesson_plan=lesson_plan
                )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("studentanalysis.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""
    
    @traceable
    def generate_boarddesign(self, course_info: CourseInfo, lesson_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教学板书设计"""
        # 获取跟踪信息
        trace_info = self.TRACE_INFO.get('boarddesign')
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(trace_info['id'], trace_info['desc'])
        llm.set_title_message(trace_info['title'])
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = BLACKBOARD_DESIGN_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                lesson_plan=lesson_plan
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                self.file_io.write_file("boarddesign.md", content, subdir="elements")
                return content
        except Exception as e:
            # 内容生成失败
            return ""

class TeachingProcessExpandAgent:
    """教学流程展开智能体，负责将教学流程大纲中的每个章节展开为详细内容"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def parse_sections(self, outline: str) -> List[Dict[str, str]]:
        """从大纲中提取各个部分
        
        Args:
            outline: 教学流程大纲内容
            
        Returns:
            List[Dict[str, str]]: 包含id和title的部分列表
        """
        sections = []
        import re
        
        # 匹配 "## xxx" 格式的标题，不管是否有序号
        pattern = r'##\s*([^\n]+)'
        matches = re.finditer(pattern, outline)
        
        for i, match in enumerate(matches):
            # 获取标题内容，并清理可能存在的重复序号
            process_title = match.group(1).strip()
            process_id = f"process_{i:02d}"  # 使用02d格式化，从00开始
            sections.append({"id": process_id, "title": process_title})
            
        return sections
    
    @traceable
    def expand_section(self, process_id: str, process_title: str, course_info: CourseInfo, outline_content: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """展开单个教学流程章节的内容"""
        # 从process_id中提取数字部分
        process_number = int(''.join(filter(str.isdigit, process_id)))
        
        # 将数字转换为中文括号序号（注意：process_number从0开始）
        chinese_numbers = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
        section_number = chinese_numbers[process_number] if 0 <= process_number < 10 else str(process_number + 1)
        
        # 清理process_title中的序号
        cleaned_title = re.sub(r'^[一二三四五六七八九十\d]+[、.．]\s*', '', process_title)
        
        # 构建标题
        if process_number == 0:  # 第一个部分
            title = f"# 六、教学流程\n\n## （{section_number}）{cleaned_title}\n\n"
        else:
            title = f"## （{section_number}）{cleaned_title}\n\n"
        
        # 创建一个新的LLM实例，避免共享跟踪信息
        llm = create_llm_client(self.config, streaming=True)
        
        # 设置跟踪信息和标题
        llm.push_trace_info(
            f"STR_05_{process_number:02d}",
            f"教学流程{process_number:02d}生成"
        )
        llm.set_title_message(title)
        
        try:
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备提示词
            prompt = PROCESS_EXPAND_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted,
                process_title=process_title,
                teaching_process_outline=outline_content
            )
            
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as content:
                processed_content = self._process_content(content, process_id)
                self.file_io.write_file(f"{process_id}.md", processed_content, subdir="sections")
                return processed_content
        except Exception as e:
            # 内容生成失败
            return ""
    
    def _process_content(self, content: str, process_id: str) -> str:
        """处理模型返回的内容"""
        # 检查是否有重复内容
        content_parts = content.split('```markdown')
        if len(content_parts) > 1:
            # 如果有多个部分，只取第一部分
            content = content_parts[0].strip()
        
        # 移除可能的 markdown 代码块标记
        cleaned_content = re.sub(r'```\s*$', '', content)
        return cleaned_content

class TeachingProcessOutlineModifyAgent:
    """教学流程大纲修改智能体，负责根据用户习惯修改教学流程大纲"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def modify_outline(self, course_info: CourseInfo, original_outline: str, user_habits: str = "", user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """根据用户习惯修改教学流程大纲 """
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        
        # 构建提示词
        prompt = OUTLINE_MODIFY_SYSTEM_PROMPT.format(
            course_info_formatted=course_info_formatted,
            user_habits=user_habits,
            original_outline=original_outline
        )

        self.llm.push_trace_info("", "教学流程大纲修改")

        # 调用语言模型
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as output:
            # 保存修改后的大纲
            self.file_io.write_file("teaching_process_outline_modified.md", output)
            return output

class LessonplanSectionsMergeAgent:
    """合并章节内容的智能体"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.file_io = FileIO(config)
        self.config = config
    
    @traceable
    def merge_sections(
        self, 
        teaching_sections: Dict[str, TeachingProcessInfo], 
        teaching_objectives: str, 
        teaching_keypoints: str, 
        teaching_studentanalysis: str,
        teaching_methods: str, 
        teaching_activities: str,
        teaching_boarddesign: str
    ) -> str:
        """合并所有内容，包括章节内容和总结"""
        # 构建教案内容
        sections = [
            teaching_objectives.strip(),
            teaching_keypoints.strip(),
            teaching_studentanalysis.strip(),
            teaching_methods.strip(),
            teaching_activities.strip()
        ]
        
        # 按顺序添加教学流程章节
        for i, (section_name, section_info) in enumerate(teaching_sections.items(), 1):
            sections.append(section_info.content.strip())
        
        # 添加教学板书设计（在教学流程之后）
        sections.append(teaching_boarddesign.strip())
            
        final_plan = "\n\n".join(sections)
        
        # 保存合并后的内容
        self.file_io.write_file("lesson_plan.md", final_plan)
        return final_plan

class ElementCustomizeAgent:
    """自定义教案要素生成器"""
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_element(self, course_info: CourseInfo, element_name: str, final_plan: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成自定义教案要素内容"""
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        
        # 准备提示词
        prompt = CUSTOM_ELEMENT_SYSTEM_PROMPT.format(
            course_info_formatted=course_info_formatted,
            element_name=element_name,
            final_plan=final_plan
        )
        
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as content:
            # 保存自定义要素内容
            self.file_io.write_file(f"{element_name}.md", content, subdir="elements/custom")
            return content

class ElementModifyGenerateAgent:
    """修改已生成要素的智能体"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
            
    @traceable
    def modify_element(self, course_info: CourseInfo, element_type: str, element_name: str, original_content: str, modify_requirements: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """修改教案要素
        
        Args:
            course_info: 课程信息
            element_type: 要素类型（如teaching_objectives、teaching_keypoints等）
            element_name: 要素名称（如果是process则是process_id，如果是custom则是具体名称）
            original_content: 原始内容
            modify_requirements: 修改要求
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            str: 修改后的内容
        """
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        
        # 构建系统提示词
        prompt = MODIFY_ELEMENT_SYSTEM_PROMPT.format(
            course_info_formatted=course_info_formatted,
            element_type=element_type,
            element_name=element_name,
            original_content=original_content,
            modify_requirements=modify_requirements
        )

        # 使用上下文管理器调用模型
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as output:
            return output

class MindmapGenerateAgent:
    """思维导图生成智能体，负责生成教学内容的思维导图"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
            
    @traceable
    def generate_mindmap(self, teaching_processes: Dict[str, TeachingProcessInfo], user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成基于教学流程的思维导图"""
        # 按照流程ID排序处理流程
        sorted_processes = sorted(
            teaching_processes.items(),
            key=lambda x: int(''.join(filter(str.isdigit, x[0])))
        )
        
        # 准备流程内容字符串
        processes_content = "\n\n".join([
            f"\n{process.content}"
            for _, process in sorted_processes
        ])
        
        # 使用配置中的思维导图提示词
        prompt = MIND_MAP_SYSTEM_PROMPT.format(
            processes_content=processes_content
        )
        
        self.llm.push_trace_info("STR_00", "思维导图生成")
        
        # 调用语言模型生成思维导图
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as mindmap_content:
            # 保存思维导图内容
            self.file_io.write_file("lesson_mindmap.md", mindmap_content)
            return mindmap_content

class TranscriptGenerateAgent:
    """教学逐字稿生成智能体，负责基于教学流程生成教学逐字稿"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_process_transcript(self, course_info: CourseInfo, process_id: str, process_title: str, process_content: str, custom_llm=None, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成单个教学环节的逐字稿
        
        Args:
            course_info: 课程信息
            process_id: 流程ID
            process_title: 流程标题
            process_content: 流程内容
            custom_llm: 可选的自定义LLM实例，用于流式输出标题
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            str: 生成的逐字稿内容
        """
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        
        # 从process_id中提取数字部分
        process_number = int(''.join(filter(str.isdigit, process_id)))
        
        # 将数字转换为中文括号序号（注意：process_number从0开始）
        chinese_numbers = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
        section_number = chinese_numbers[process_number] if 0 <= process_number < 10 else str(process_number + 1)
        
        # 清理process_title中的序号
        cleaned_title = re.sub(r'^[一二三四五六七八九十\d]+[、.．]\s*', '', process_title)
        
        # 构建标题
        title = f"## （{section_number}）{cleaned_title}"
        
        # 使用配置中的逐字稿提示词
        prompt = self.config.transcript_system_prompt.format(
            course_info_formatted=course_info_formatted,
            process_title=process_title,
            process_content=process_content
        )
        
        # 使用自定义LLM实例或创建新的实例
        llm = custom_llm or self.llm
        
        # 如果没有自定义LLM，则添加跟踪信息
        if not custom_llm:
            llm.push_trace_info(f"STR_00", f"教学逐字稿{process_number:02d}生成")
        
        # 调用语言模型生成逐字稿
        with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as transcript_content:
            return f"{transcript_content}"
    
    @traceable
    def generate_full_transcript(self, course_info: CourseInfo, teaching_processes: Dict[str, TeachingProcessInfo], user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成完整的教学逐字稿
        
        Args:
            course_info: 课程信息
            teaching_processes: 教学流程信息字典
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            str: 生成的完整逐字稿内容
        """
        # 按照流程ID排序处理流程
        sorted_processes = sorted(
            teaching_processes.items(),
            key=lambda x: int(''.join(filter(str.isdigit, x[0])))
        )
        
        # 准备标题
        if course_info.lesson_unitname:
            full_title = f"# {course_info.lesson_unitname} 教学逐字稿\n\n"
        else:
            full_title = "# 教学逐字稿\n\n"
        
        # 生成每个环节的逐字稿
        transcript_parts = []
        
        # 对于第一个环节，创建一个新的LLM实例并设置标题消息
        first_process_id, first_process_info = sorted_processes[0]
        llm = create_llm_client(self.config, streaming=True)
        llm.push_trace_info(f"STR_00", "教学逐字稿开始生成")
        llm.set_title_message(full_title)
        
        # 生成第一个环节的逐字稿（包含标题）
        first_transcript = self.generate_process_transcript(
            course_info=course_info,
            process_id=first_process_id,
            process_title=first_process_info.process_title,
            process_content=first_process_info.content,
            custom_llm=llm,
            user_id=user_id,
            session_id=session_id
        )
        transcript_parts.append(first_transcript)
        
        # 生成其余环节的逐字稿
        for process_id, process_info in sorted_processes[1:]:
            transcript_content = self.generate_process_transcript(
                course_info=course_info,
                process_id=process_id,
                process_title=process_info.process_title,
                process_content=process_info.content,
                user_id=user_id,
                session_id=session_id
            )
            transcript_parts.append(transcript_content)
        
        # 合并所有部分
        full_transcript = "\n\n".join(transcript_parts)
        
        # 保存完整逐字稿
        self.file_io.write_file("lesson_transcript.md", full_transcript)
        
        return full_transcript

class PlananalysisGenerateAgent:
    """教案分析生成智能体，负责基于完整教案内容生成分析和评价"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_plananalysis(self, course_info: CourseInfo, teaching_processes: str, user_habits: str = "", user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成教案分析
        
        Args:
            course_info: 课程信息
            lesson_plan: 完整教案内容
            user_habits: 用户习惯和偏好
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            str: 生成的教案分析内容
        """
        # 创建一个新的LLM实例，开启流式输出
        llm = create_llm_client(self.config, streaming=True)
        
        # 格式化课程信息
        course_info_formatted = format_course_info(course_info)
        sorted_processes = sorted(
            teaching_processes.items(),
            key=lambda x: int(''.join(filter(str.isdigit, x[0])))
        )
        
        # 准备流程内容字符串
        processes_content = "\n\n".join([
            f"\n{process.content}"
            for _, process in sorted_processes
        ])
        # 准备标题
        if course_info.lesson_coursename and course_info.lesson_unitname:
            full_title = f"# {course_info.lesson_unitname} 教案分析\n\n"
        else:
            full_title = "# 教案分析\n\n"
        
        # 设置跟踪信息和标题
        llm.push_trace_info("STR_00", "教案分析生成")
        llm.set_title_message(full_title)
        
        # 使用配置中的教案分析提示词
        prompt = self.config.plananalysis_system_prompt.format(
            course_info_formatted=course_info_formatted,
            lesson_plan=processes_content,
            user_habits=user_habits or "用户未提供特定的教学风格和偏好。"
        )
        
        # 调用语言模型生成教案分析
        with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as analysis_content:
            # 保存教案分析
            self.file_io.write_file("lesson_analysis.md", full_title + analysis_content)
            return full_title + analysis_content

class TeachingMethodologyAgent:
    """教学法匹配智能体，负责根据课程信息自动匹配合适的教学法"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def match_methodology(self, course_info: CourseInfo, user_habits: str = "", user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """根据课程信息自动匹配适合的教学法
        
        Args:
            course_info: 课程信息
            user_habits: 用户习惯和偏好
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            str: 匹配的教学法名称，如果出错则返回默认教学法
        """
        try:
            # 创建一个新的LLM实例
            llm = create_llm_client(self.config)
            
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 使用提示词模板
            prompt = TEACHING_METHODOLOGY_PROMPT.format(
                course_info_formatted=course_info_formatted
            )
            
            # 如果有用户习惯，添加到提示词中
            if user_habits:
                prompt += f"\n\n用户教学风格和偏好：\n{user_habits}"
            
            # 设置跟踪信息
            llm.push_trace_info("", "教学法匹配")
            
            # 调用语言模型获取匹配结果
            with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as result:
                # 清理结果
                result = result.strip()
                if not result:
                    return "直接教学法"  # 如果结果为空，返回默认值
                
                # 如果结果中包含多个教学法（用逗号分隔），最多取两个
                methodologies = [m.strip() for m in result.split(',')]
                if len(methodologies) > 2:
                    methodologies = methodologies[:2]
                
                # 返回结果，用顿号连接
                return "、".join(methodologies)
                
        except Exception as e:
            print(f"匹配教学法失败：{str(e)}")
            # 出错时返回默认教学法
            return "直接教学法"

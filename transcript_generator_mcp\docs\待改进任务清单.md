# 待改进任务清单

## 概述

本文档列出了 `transcript_generator_mcp` 项目需要改进的具体任务，以确保与参考项目 `picturebook_generator_mcp` 保持一致性。

## 任务优先级说明

- **P0 (高优先级)**: 影响用户体验或功能的关键问题
- **P1 (中优先级)**: 代码规范和一致性问题
- **P2 (低优先级)**: 优化和完善性问题

---

## P0 高优先级任务

### 任务 1: 移除 metadata.json 中不需要的字段

**问题描述:**
当前 metadata.json 包含用户不需要的字段：
- `estimated_duration_minutes`: 22
- `generation_time_seconds`: 43.847906827926636

**当前状态:**
```json
"generation_info": {
  "timestamp": "2025-07-20T17:40:57.045212",
  "sections_count": 6,
  "estimated_duration_minutes": 22,        // ❌ 需要移除
  "generation_time_seconds": 43.847906827926636,  // ❌ 需要移除
  "transcript_length": 5093
}
```

**目标状态:**
```json
"generation_info": {
  "timestamp": "2025-07-20T17:40:57.045212",
  "sections_count": 6,
  "transcript_length": 5093
}
```

**具体步骤:**
1. 修改 `src/transcript_generator/services.py` 中的 `_save_metadata()` 方法
2. 移除 `estimated_duration_minutes` 和 `generation_time_seconds` 字段的生成逻辑
3. 更新相关的数据模型（如果有）
4. 测试验证输出格式正确

**涉及文件:**
- `src/transcript_generator/services.py`
- `src/transcript_generator/models.py` (如果有相关模型)

**验收标准:**
- [x] metadata.json 不再包含这两个字段
- [x] 功能测试正常
- [x] 输出格式与参考项目一致

---

### 任务 2: 完善日志系统的进度显示

**问题描述:**
当前日志缺少参考项目那样的详细进度显示，如：
`[进度] image_generation_started: 正在为第 4 页生成图片`

**当前状态:**
```
2025-07-20 16:59:17 - transcript_generator.services - INFO - services.py:115 - generate_teaching_flow() - 开始生成教学流程 - 课程: 健康检查测试课程
```

**目标状态:**
```
2025-07-20 16:59:17 - TranscriptGeneratorService - INFO - services.py:115 - generate_teaching_flow() - [进度] flow_generation_started: 开始生成教学流程 - 课程: 健康检查测试课程
```

**具体步骤:**
1. 在 `services.py` 中添加进度日志标识 `[进度]`
2. 为每个关键步骤添加详细的进度日志：
   - `[进度] flow_generation_started: 开始生成教学流程`
   - `[进度] section_generation_started: 开始生成第 X 个环节`
   - `[进度] section_generation_completed: 第 X 个环节生成完毕`
   - `[进度] transcript_generation_completed: 逐字稿生成完成`
3. 统一日志记录器命名为项目特定名称
4. 确保进度日志与 `ctx.info()` 的实时反馈保持一致

**涉及文件:**
- `src/transcript_generator/services.py`
- `src/transcript_generator/logger.py`

**验收标准:**
- [x] 日志包含 `[进度]` 标识
- [x] 每个关键步骤都有对应的进度日志
- [x] 日志格式与参考项目一致
- [x] 进度信息足够详细和清晰

---

### 任务 2.1: 删除非核心工具

**问题描述:**
当前项目包含了`health_check`和`get_service_info`工具，但我们只需要保留用于生成逐字稿的核心工具。

**当前状态:**
```python
@mcp.tool()
async def health_check(ctx: Context) -> str:
    # 健康检查逻辑

@mcp.tool()
async def get_service_info(ctx: Context) -> str:
    # 服务信息获取逻辑
```

**目标状态:**
只保留 `generate_transcript` 工具。

**具体步骤:**
1. 从 `main.py` 中删除 `health_check` 和 `get_service_info` 工具定义
2. 从 `services.py` 中删除 `health_check` 方法
3. 清理相关的导入和引用

**涉及文件:**
- `main.py`
- `src/transcript_generator/services.py`

**验收标准:**
- [x] main.py 中只保留 generate_transcript 工具
- [x] services.py 中不再包含 health_check 方法
- [x] 功能测试正常
- [x] 只保留核心的逐字稿生成功能

---

### 任务 2.1: 删除health_check工具

**问题描述:**
当前项目包含了health_check工具，但我们只需要保留用于生成逐字稿的核心工具。

**当前状态:**
```python
@mcp.tool()
async def health_check(ctx: Context) -> str:
    # 健康检查逻辑
```

**目标状态:**
只保留 `generate_transcript` 和 `get_service_info` 工具。

**具体步骤:**
1. 从 `main.py` 中删除 `health_check` 工具定义
2. 从 `services.py` 中删除 `health_check` 方法
3. 清理相关的导入和引用

**涉及文件:**
- `main.py`
- `src/transcript_generator/services.py`

**验收标准:**
- [x] main.py 中不再包含 health_check 工具
- [x] services.py 中不再包含 health_check 方法
- [x] 功能测试正常
- [x] 只保留核心的逐字稿生成功能

---

## P1 中优先级任务

### 任务 3: 统一目录命名规范

**问题描述:**
当前项目使用 `docs/` 目录，而参考项目使用 `doc/` 目录。

**当前状态:**
```
transcript_generator_mcp/
├── docs/           # ❌ 应为 doc/
```

**目标状态:**
```
transcript_generator_mcp/
├── doc/            # ✅ 与参考项目一致
```

**具体步骤:**
1. 将 `docs/` 目录重命名为 `doc/`
2. 更新所有引用该目录的文档和配置
3. 检查是否有硬编码的路径需要更新

**涉及文件:**
- 整个 `docs/` 目录
- README.md (如果有引用)
- 其他可能引用该目录的文件

**验收标准:**
- [ ] 目录名称与参考项目一致
- [ ] 所有文档链接正常
- [ ] 项目结构清晰

---

### 任务 4: 考虑核心业务文件命名统一

**问题描述:**
当前项目使用 `services.py`，参考项目使用 `generators.py`。

**当前状态:**
```
src/transcript_generator/
├── services.py     # 核心业务逻辑
```

**目标状态 (可选):**
```
src/transcript_generator/
├── generators.py   # 与参考项目一致
```

**具体步骤:**
1. 评估重命名的必要性和影响
2. 如果决定重命名：
   - 将 `services.py` 重命名为 `generators.py`
   - 更新所有导入语句
   - 更新类名以保持一致性
3. 更新相关文档和注释

**涉及文件:**
- `src/transcript_generator/services.py`
- `main.py`
- 所有导入该模块的文件

**验收标准:**
- [ ] 文件命名与参考项目一致
- [ ] 所有导入正常工作
- [ ] 功能测试通过

---

### 任务 5: 优化 pyproject.toml 结构

**问题描述:**
当前项目的 `pyproject.toml` 结构与参考项目存在差异。

**具体步骤:**
1. 对比两个项目的 `pyproject.toml` 文件
2. 统一项目元数据格式
3. 对齐依赖版本和配置
4. 添加缺失的工具配置（如 black, isort 等）

**涉及文件:**
- `pyproject.toml`

**验收标准:**
- [ ] 项目配置结构一致
- [ ] 依赖版本合理
- [ ] 包含必要的开发工具配置

---

## P2 低优先级任务

### 任务 6: 统一日志记录器命名规范

**问题描述:**
日志记录器命名需要与参考项目保持一致的风格。

**具体步骤:**
1. 检查所有模块中的日志记录器命名
2. 统一命名规范
3. 确保日志输出格式一致

**涉及文件:**
- `src/transcript_generator/logger.py`
- 所有使用日志的模块

**验收标准:**
- [ ] 日志记录器命名一致
- [ ] 日志格式统一

---

### 任务 7: 完善实时进度反馈机制

**问题描述:**
需要确保 `ctx.info()` 的实时反馈与日志系统保持一致。

**具体步骤:**
1. 检查所有 `ctx.info()` 调用
2. 确保进度消息格式一致
3. 添加缺失的进度反馈点

**涉及文件:**
- `src/transcript_generator/services.py`
- `main.py`

**验收标准:**
- [ ] 实时反馈信息完整
- [ ] 消息格式一致
- [ ] 用户体验良好

---

### 任务 8: 代码注释和文档完善

**问题描述:**
确保代码注释和文档字符串与参考项目的质量和风格保持一致。

**具体步骤:**
1. 检查所有类和方法的 docstring
2. 统一注释风格
3. 完善缺失的文档

**涉及文件:**
- 所有源码文件

**验收标准:**
- [ ] 文档字符串完整
- [ ] 注释风格一致
- [ ] 代码可读性良好

---

## 任务执行计划

### 第一阶段 (P0 任务)
1. **任务 1**: 移除 metadata.json 不需要的字段
2. **任务 2**: 完善日志系统的进度显示

### 第二阶段 (P1 任务)
3. **任务 3**: 统一目录命名规范
4. **任务 4**: 考虑核心业务文件命名统一
5. **任务 5**: 优化 pyproject.toml 结构

### 第三阶段 (P2 任务)
6. **任务 6**: 统一日志记录器命名规范
7. **任务 7**: 完善实时进度反馈机制
8. **任务 8**: 代码注释和文档完善

## 验收标准

### 整体验收标准
- [ ] 所有 P0 任务完成
- [ ] 至少 80% 的 P1 任务完成
- [ ] 项目与参考项目高度一致
- [ ] 功能测试全部通过
- [ ] 用户体验良好

### 测试验证
1. **功能测试**: 确保所有核心功能正常工作
2. **一致性测试**: 对比输出格式和行为
3. **性能测试**: 确保改进不影响性能
4. **用户体验测试**: 验证日志和反馈的改进效果

---

## P2 低优先级任务（续）

### 任务 9: 数据模型层优化参考

**问题描述:**
当前项目的数据模型可以参考原始项目的TranscriptState设计和参考项目的模型结构进行优化。

**参考对比分析:**

**原始项目 TranscriptState (LangGraph状态):**
```python
class TranscriptState(TypedDict):
    messages: List[HumanMessage]                    # 消息历史
    router: Router                                  # 路由状态
    course_info: CourseInfo                         # 课程信息
    teaching_process: TeachingProcess               # 教学流程大纲
    teaching_processes: Dict[str, TeachingProcessInfo]  # 各环节详细信息
    current_section: Optional[TeachingProcessInfo]  # 当前处理的环节
    final_transcript: Optional[str]                 # 最终逐字稿
```

**参考项目模型特点:**
- 简洁明了的字段定义
- 清晰的类型注解
- 合理的可选字段设计

**当前项目模型特点:**
- 详细的字段验证
- 完整的文档字符串
- 丰富的示例数据

**改进建议:**
1. **借鉴原始项目的状态管理思路**:
   - 考虑添加Router状态类用于追踪处理进度
   - 优化TeachingSection模型，参考TeachingProcessInfo设计
   - 考虑添加状态合并机制

2. **参考参考项目的简洁性**:
   - 保持字段定义的简洁性
   - 避免过度复杂的验证逻辑
   - 统一可选字段的默认值处理

3. **保持当前项目的优势**:
   - 保留详细的字段验证
   - 保持完整的文档字符串
   - 保留有用的示例数据

**具体步骤:**
1. 分析三个项目的模型设计差异
2. 提取最佳实践和设计模式
3. 优化当前模型结构
4. 更新相关的业务逻辑代码
5. 测试验证模型改进效果

**涉及文件:**
- `src/transcript_generator/models.py`
- `src/transcript_generator/services.py`

**验收标准:**
- [ ] 模型设计更加合理和一致
- [ ] 保持向后兼容性
- [ ] 功能测试正常
- [ ] 代码可读性提升

---

## 注意事项

1. **备份**: 在进行任何修改前，请确保代码已备份
2. **测试**: 每完成一个任务后，立即进行测试验证
3. **文档**: 及时更新相关文档
4. **版本控制**: 合理使用 Git 提交，便于回滚
5. **渐进式改进**: 优先完成高优先级任务，确保项目稳定性
6. **参考学习**: 充分利用原始项目和参考项目的设计经验

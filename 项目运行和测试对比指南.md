# 教学逐字稿生成项目运行和测试对比指南

## 📋 项目概述

### 原始项目 (yunzhishi-agent-service/src/transcript_graph/)
- **架构**: LangGraph + LangChain 状态机工作流
- **启动方式**: `langgraph dev --host 0.0.0.0 --port 2024`
- **服务类型**: HTTP API服务 (端口2024)
- **API端点**: `http://localhost:2024/transcript`
- **特殊功能**: 支持PDF文件处理、并行生成、状态机工作流

### MCP化项目 (transcript_generator_mcp/)
- **架构**: FastMCP框架
- **启动方式**: `python main.py`
- **服务类型**: MCP工具服务 (streamable-http)
- **工具**: generate_transcript, health_check, get_service_info
- **特殊功能**: 实时进度反馈、流式输出、MCP协议

## 🚀 原始项目测试步骤

### 1. 环境准备
```bash
cd yunzhishi-agent-service

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -e ".[dev]"
```

### 2. 配置环境变量
创建 `.env` 文件：
```bash
# AI模型配置
ZHIPUAI_API_KEY=your_api_key_here
SILICONFLOW_API_KEY=your_api_key_here

# OSS存储配置（可选）
OSS_ACCESS_KEY_ID=your_oss_id
OSS_ACCESS_KEY_SECRET=your_oss_secret

# LangSmith追踪（可选）
LANGSMITH_API_KEY=your_langsmith_key

# 服务配置
LANGCHAIN_HOST=0.0.0.0
LANGCHAIN_BACKEND_HOST=0.0.0.0
LANGCHAIN_PORT=2024
```

### 3. 启动服务
```bash
# 启动LangGraph开发服务器
langgraph dev --host 0.0.0.0 --port 2024
```

### 4. 测试API调用
创建测试脚本 `test_original.py`：
```python
import requests
import json
import time

def test_original_api():
    url = "http://localhost:2024/transcript"
    
    # 测试数据
    test_data = {
        "messages": [
            {
                "type": "human",
                "content": {
                    "course_basic_info": "小学三年级数学，面积单元",
                    "teaching_info": "让学生理解面积概念，学会计算长方形面积",
                    "personal_requirements": "注重动手操作，多用生活实例"
                }
            }
        ]
    }
    
    print("🚀 测试原始项目API...")
    start_time = time.time()
    
    try:
        response = requests.post(url, json=test_data, timeout=300)
        response.raise_for_status()
        
        result = response.json()
        end_time = time.time()
        
        print(f"✅ 请求成功，耗时: {end_time - start_time:.2f}秒")
        print(f"📊 结果统计:")
        print(f"   - 最终逐字稿长度: {len(result.get('final_transcript', ''))}")
        print(f"   - 教学环节数量: {len(result.get('teaching_processes', {}))}")
        
        # 保存结果
        with open('original_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return result
        
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None

if __name__ == "__main__":
    test_original_api()
```

## 🔧 MCP项目测试步骤

### 1. 环境准备
```bash
cd transcript_generator_mcp

# 创建虚拟环境（如果需要）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
创建 `.env` 文件：
```bash
# AI模型配置
AI_PROVIDER=zhipuai
AI_API_KEY=your_api_key_here
AI_MODEL_NAME=glm-4-flash
AI_BASE_URL=https://open.bigmodel.cn/api/paas/v4/

# 输出配置
OUTPUT_DIR=outputs
LOG_LEVEL=INFO
LOG_CONSOLE=true

# 性能配置
PARALLEL_LIMIT=3
MAX_SECTIONS=6
TEST_MODE=false
```

### 3. 快速验证
```bash
# 运行快速测试
python quick_test.py
```

### 4. 启动MCP服务
```bash
# 启动MCP服务器
python main.py
```

### 5. 测试MCP工具
创建测试脚本 `test_mcp.py`：
```python
import asyncio
import json
import time
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mcp_tools():
    """测试MCP工具"""
    print("🚀 测试MCP项目工具...")
    
    try:
        # 导入MCP工具
        from main import generate_transcript, initialize_service
        
        # 初始化服务
        initialize_service()
        
        # 模拟MCP上下文
        class MockContext:
            def __init__(self):
                self.messages = []
            
            async def info(self, message):
                self.messages.append(message)
                if isinstance(message, dict):
                    status = message.get("status", "")
                    if status == "started":
                        print("   🎬 开始生成...")
                    elif status == "completed":
                        print("   🎉 生成完成")
        
        ctx = MockContext()
        
        # 测试数据（与原始项目相同）
        start_time = time.time()
        result_json = await generate_transcript(
            course_basic_info="小学三年级数学，面积单元",
            teaching_info="让学生理解面积概念，学会计算长方形面积",
            ctx=ctx,
            personal_requirements="注重动手操作，多用生活实例"
        )
        end_time = time.time()
        
        result = json.loads(result_json)
        
        if result["success"]:
            data = result["data"]
            print(f"✅ 生成成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📊 结果统计:")
            print(f"   - 最终逐字稿长度: {len(data['final_transcript'])}")
            print(f"   - 教学环节数量: {data['total_sections']}")
            print(f"   - 预计时长: {data['estimated_duration']}分钟")
            print(f"   - 实时消息数: {len(ctx.messages)}")
            
            # 保存结果
            with open('mcp_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            return result
        else:
            print(f"❌ 生成失败: {result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_mcp_tools())
```

## 📊 结果对比分析

### 1. 输入格式对比

**原始项目输入**:
```json
{
  "messages": [
    {
      "type": "human",
      "content": {
        "course_basic_info": "...",
        "teaching_info": "...",
        "personal_requirements": "...",
        "pdf_path": "...",  // 支持PDF
        "pdf_start_page": 1,
        "pdf_end_page": 5
      }
    }
  ]
}
```

**MCP项目输入**:
```python
generate_transcript(
    course_basic_info="...",
    teaching_info="...",
    personal_requirements="..."  // 暂不支持PDF
)
```

### 2. 输出格式对比

**原始项目输出**:
```json
{
  "final_transcript": "...",
  "teaching_processes": {
    "process_01": {
      "process_id": "process_01",
      "process_title": "课程导入",
      "content": "...",
      "transcript": "..."
    }
  }
}
```

**MCP项目输出**:
```json
{
  "success": true,
  "message": "教学逐字稿生成成功",
  "data": {
    "final_transcript": "...",
    "teaching_sections": [
      {
        "section_id": "...",
        "title": "...",
        "content": "...",
        "transcript": "...",
        "duration_minutes": 10
      }
    ],
    "total_sections": 4,
    "estimated_duration": 40,
    "generation_time": 15.2,
    "created_at": "2024-01-01T10:00:00"
  }
}
```

## 🔍 一致性检查要点

### 1. 核心内容对比
- **final_transcript**: 最终逐字稿内容是否相似
- **教学环节**: 数量、标题、内容结构是否一致
- **逐字稿质量**: 语言风格、教学流程是否符合预期

### 2. 性能对比
- **生成时间**: 两个版本的处理速度
- **资源使用**: 内存、CPU使用情况
- **并发能力**: 处理多个请求的能力

### 3. 功能差异
- **PDF支持**: 原始版本支持，MCP版本暂不支持
- **实时反馈**: MCP版本提供更好的进度反馈
- **错误处理**: 两个版本的错误处理机制

## 🛠️ 问题排查

### 常见问题
1. **API密钥配置**: 确保两个项目使用相同的AI模型和配置
2. **依赖版本**: 检查关键依赖包的版本是否一致
3. **提示词差异**: 对比两个项目的提示词是否相同
4. **模型参数**: 确保温度、最大token等参数一致

### 调试建议
1. **开启详细日志**: 设置LOG_LEVEL=DEBUG
2. **对比中间结果**: 检查教学流程生成阶段的输出
3. **单步调试**: 逐个环节对比生成结果
4. **性能监控**: 记录各阶段的耗时和资源使用

## 📝 测试报告模板

```markdown
# 测试报告

## 测试环境
- 测试时间: 
- 测试用例: 
- AI模型: 

## 结果对比
| 指标 | 原始项目 | MCP项目 | 差异 |
|------|----------|---------|------|
| 生成时间 | | | |
| 逐字稿长度 | | | |
| 教学环节数 | | | |
| 内容质量 | | | |

## 问题记录
- [ ] 问题1: 描述
- [ ] 问题2: 描述

## 结论
- 一致性评估: 
- 建议改进: 
```

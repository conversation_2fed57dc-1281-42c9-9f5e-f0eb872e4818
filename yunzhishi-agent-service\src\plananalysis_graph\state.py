"""教案分析生成图的状态管理。

本模块定义了教案分析生成图中使用的状态结构。
包括输入状态、处理状态、输出状态和路由分类模式的定义。
"""
from typing import Dict, List, Optional, Any, TypedDict, Annotated
import operator
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

class PlanInfo(BaseModel):
    """教案信息类型定义"""
    lesson_plan: Optional[str] = Field(default="", description="教案内容")
    personal_requirements: Optional[str] = Field(None, description="个性化要求")
    pdf_path: Optional[str] = Field(None, description="PDF文件路径")
    pdf_start_page: Optional[int] = Field(None, description="PDF起始页码")
    pdf_end_page: Optional[int] = Field(None, description="PDF结束页码")
    pdf_content: Optional[str] = Field(None, description="PDF内容")
    pdf_type: str = Field(default="text", description="PDF处理类型，text或vision")
    pdf_temp_path: Optional[str] = Field(None, description="PDF临时文件路径（用于缓存）")
    
    def __or__(self, other: 'PlanInfo') -> 'PlanInfo':
        """实现教案信息的合并操作
        
        合并规则：保留最新的非空值
        """
        return PlanInfo(
            lesson_plan=other.lesson_plan or self.lesson_plan,
            personal_requirements=other.personal_requirements or self.personal_requirements,
            pdf_path=other.pdf_path or self.pdf_path,
            pdf_start_page=other.pdf_start_page or self.pdf_start_page,
            pdf_end_page=other.pdf_end_page or self.pdf_end_page,
            pdf_content=other.pdf_content or self.pdf_content,
            pdf_type=other.pdf_type or self.pdf_type,
            pdf_temp_path=other.pdf_temp_path or self.pdf_temp_path
        )

class Router(BaseModel):
    """路由状态模型"""
    stage: str = Field(description="当前阶段")
    status: str = Field(description="状态描述")
    error: Optional[str] = Field(None, description="错误信息")
    
    def __or__(self, other: 'Router') -> 'Router':
        """实现路由状态的合并操作
        
        合并规则：
        1. 如果任一状态有错误，保留错误状态
        2. 否则保留最新的状态
        """
        if self.error:
            return self
        if other.error:
            return other
        return other

class PlanAnalysis(BaseModel):
    """教案分析模型"""
    content: str = Field(description="教案分析内容")
    
    def __or__(self, other: 'PlanAnalysis') -> 'PlanAnalysis':
        """实现教案分析的合并操作"""
        return other if other.content else self

class PlanAnalysisInput(TypedDict):
    """教案分析输入状态"""
    messages: List[HumanMessage]

class PlanAnalysisOutput(TypedDict):
    """教案分析输出状态"""
    final_analysis: str

class PlanAnalysisState(TypedDict):
    """教案分析状态"""
    messages: List[HumanMessage]
    router: Annotated[Router, operator.or_]
    plan_info: Annotated[Optional[PlanInfo], operator.or_]
    plan_analysis: Annotated[Optional[PlanAnalysis], operator.or_]
    final_analysis: Optional[str]

def create_initial_state() -> PlanAnalysisState:
    """创建初始状态
    
    Returns:
        PlanAnalysisState: 包含所有必要字段的初始状态字典
    """
    return {
        "messages": [],
        "router": Router(
            stage="process_info",
            status="开始处理信息"
        ),
        "plan_info": PlanInfo(
            lesson_plan="",
            personal_requirements=None
        ),
        "plan_analysis": None,
        "final_analysis": None
    } 
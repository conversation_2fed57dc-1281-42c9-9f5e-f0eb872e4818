"""教学逐字稿生成图的状态管理。

本模块定义了教学逐字稿生成图中使用的状态结构。
包括输入状态、处理状态、输出状态和路由分类模式的定义。
"""
from typing import Dict, List, Optional, Any, TypedDict, Annotated
import operator
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

class TeachingProcessInfo(BaseModel):
    """教学流程信息"""
    process_id: str = Field(description="流程ID")
    process_title: str = Field(description="流程标题")
    content: Optional[str] = Field(default=None, description="流程内容")
    transcript: Optional[str] = Field(default=None, description="逐字稿内容")
    
    def __or__(self, other: 'TeachingProcessInfo') -> 'TeachingProcessInfo':
        """实现教学流程信息的合并操作
        
        合并规则：
        1. 保持原有的process_id和process_title
        2. 如果有新的content或transcript则更新
        """
        return TeachingProcessInfo(
            process_id=self.process_id,
            process_title=self.process_title,
            content=other.content if other.content is not None else self.content,
            transcript=other.transcript if other.transcript is not None else self.transcript
        )

class CourseInfo(BaseModel):
    """课程信息类型定义"""
    course_basic_info: str = Field(description="课程基本信息")
    teaching_info: str = Field(description="教学信息")
    personal_requirements: Optional[str] = Field(None, description="个性化要求")
    pdf_path: Optional[str] = Field(None, description="PDF文件路径")
    pdf_start_page: Optional[int] = Field(None, description="PDF起始页码")
    pdf_end_page: Optional[int] = Field(None, description="PDF结束页码")
    pdf_content: Optional[str] = Field(None, description="PDF内容")
    pdf_type: str = Field(default="text", description="PDF处理类型，text或vision")
    pdf_temp_path: Optional[str] = Field(None, description="PDF临时文件路径（用于缓存）")
    
    def __or__(self, other: 'CourseInfo') -> 'CourseInfo':
        """实现课程信息的合并操作
        
        合并规则：保留最新的非空值
        """
        return CourseInfo(
            course_basic_info=other.course_basic_info or self.course_basic_info,
            teaching_info=other.teaching_info or self.teaching_info,
            personal_requirements=other.personal_requirements or self.personal_requirements,
            pdf_path=other.pdf_path or self.pdf_path,
            pdf_start_page=other.pdf_start_page or self.pdf_start_page,
            pdf_end_page=other.pdf_end_page or self.pdf_end_page,
            pdf_content=other.pdf_content or self.pdf_content,
            pdf_type=other.pdf_type or self.pdf_type,
            pdf_temp_path=other.pdf_temp_path or self.pdf_temp_path
        )

class Router(BaseModel):
    """路由状态模型"""
    stage: str = Field(description="当前阶段")
    status: str = Field(description="状态描述")
    error: Optional[str] = Field(None, description="错误信息")
    
    def __or__(self, other: 'Router') -> 'Router':
        """实现路由状态的合并操作
        
        合并规则：
        1. 如果任一状态有错误，保留错误状态
        2. 否则保留最新的状态
        """
        if self.error:
            return self
        if other.error:
            return other
        return other

class TeachingProcess(BaseModel):
    """教学流程模型"""
    content: str = Field(description="教学流程内容")
    
    def __or__(self, other: 'TeachingProcess') -> 'TeachingProcess':
        """实现教学流程的合并操作"""
        return other if other.content else self

class TranscriptInput(TypedDict):
    """教学逐字稿输入状态"""
    messages: List[HumanMessage]

class TranscriptOutput(TypedDict):
    """教学逐字稿输出状态"""
    final_transcript: str
    teaching_processes: Dict[str, TeachingProcessInfo]

class TranscriptState(TypedDict):
    """教学逐字稿状态"""
    messages: List[HumanMessage]
    router: Annotated[Router, operator.or_]
    course_info: Annotated[Optional[CourseInfo], operator.or_]
    teaching_process: Annotated[TeachingProcess, operator.or_]
    teaching_processes: Annotated[Dict[str, TeachingProcessInfo], operator.or_]
    current_section: Optional[TeachingProcessInfo]
    final_transcript: Optional[str]

def create_initial_state() -> TranscriptState:
    """创建初始状态
    
    Returns:
        TranscriptState: 包含所有必要字段的初始状态字典
    """
    return {
        "messages": [],
        "router": Router(
            stage="process_info",
            status="开始处理信息"
        ),
        "course_info": CourseInfo(
            course_basic_info="",
            teaching_info="",
            personal_requirements=None
        ),
        "teaching_process": TeachingProcess(content=""),
        "teaching_processes": {},
        "current_section": None,
        "final_transcript": None
    } 
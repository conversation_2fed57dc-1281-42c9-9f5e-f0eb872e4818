"""PPT生成器日志配置模块

提供统一的日志配置和管理功能，支持文件输出、控制台输出和不同日志级别。
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional


class PPTLogger:
    """PPT生成器日志管理器"""
    
    _loggers = {}
    _initialized = False
    
    @classmethod
    def setup_logging(
        cls,
        log_dir: str = "logs",
        log_level: str = "INFO",
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        console_output: bool = True
    ) -> None:
        """
        设置全局日志配置
        
        Args:
            log_dir: 日志文件目录
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            max_file_size: 单个日志文件最大大小（字节）
            backup_count: 日志文件备份数量
            console_output: 是否输出到控制台
        """
        if cls._initialized:
            return
            
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置根日志级别
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - '
                '%(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加文件处理器 - 详细日志
        log_filename = os.path.join(
            log_dir, 
            f"ppt_generator_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # 添加错误日志文件处理器
        error_log_filename = os.path.join(
            log_dir,
            f"ppt_generator_error_{datetime.now().strftime('%Y%m%d')}.log"
        )
        error_file_handler = logging.handlers.RotatingFileHandler(
            filename=error_log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_file_handler)
        
        # 添加控制台处理器（如果启用）
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            console_handler.setFormatter(simple_formatter)
            root_logger.addHandler(console_handler)
        
        cls._initialized = True
        
        # 记录初始化信息
        logger = cls.get_logger("PPTLogger")
        logger.info("日志系统初始化完成")
        logger.info(f"日志目录: {log_dir}")
        logger.info(f"日志级别: {log_level}")
        logger.info(f"控制台输出: {console_output}")
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        if name not in cls._loggers:
            logger = logging.getLogger(name)
            cls._loggers[name] = logger
        
        return cls._loggers[name]
    
    @classmethod
    def set_level(cls, name: str, level: str) -> None:
        """
        设置指定日志器的级别
        
        Args:
            name: 日志器名称
            level: 日志级别
        """
        if name in cls._loggers:
            numeric_level = getattr(logging, level.upper(), logging.INFO)
            cls._loggers[name].setLevel(numeric_level)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志器的便捷函数
    
    Args:
        name: 日志器名称，如果为None则使用调用者的模块名
        
    Returns:
        logging.Logger: 日志器实例
    """
    if name is None:
        # 自动获取调用者的模块名
        import inspect
        frame = inspect.currentframe()
        if frame and frame.f_back:
            name = frame.f_back.f_globals.get('__name__', 'unknown')
        else:
            name = 'unknown'
    
    return PPTLogger.get_logger(name)


# 初始化默认日志配置
def init_default_logging() -> None:
    """初始化默认日志配置"""
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    log_dir = os.path.join(project_root, "logs")
    
    # 从环境变量获取日志级别
    log_level = os.getenv("LOG_LEVEL", "INFO")
    console_output = os.getenv("LOG_CONSOLE", "true").lower() == "true"
    
    PPTLogger.setup_logging(
        log_dir=log_dir,
        log_level=log_level,
        console_output=console_output
    )


# 在模块加载时初始化日志
if not PPTLogger._initialized:
    init_default_logging() 
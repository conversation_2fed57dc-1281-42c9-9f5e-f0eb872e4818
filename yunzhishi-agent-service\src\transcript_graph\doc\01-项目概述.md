# Transcript Graph 项目概述

## 项目简介

Transcript Graph 是一个基于 AI 的教学逐字稿自动生成系统，能够根据课程信息和教学资料，自动生成完整、详细的教学逐字稿。该系统采用分治策略，先生成教学流程大纲，再为每个教学环节生成具体的逐字稿内容，最后合并为完整的教学逐字稿。

## 核心功能

### 1. 多模态输入支持
- **文本输入**：基于课程基本信息、教学信息等文本内容生成逐字稿
- **视觉输入**：支持 PDF 文档解析，将 PDF 内容转换为图像后进行 AI 分析
- **混合输入**：同时支持文本和 PDF 资料的组合输入

### 2. 智能教学流程设计
- 根据课程内容自动设计 4-5 个教学环节
- 确保教学环节的逻辑性和递进性
- 包含课程导入、主体内容、课程总结等完整结构
- 每个环节包含 3-5 个具体步骤

### 3. 详细逐字稿生成
- 为每个教学环节生成 800-1200 字的详细逐字稿
- 包含教师讲话、学生互动、教学动作等完整内容
- 语言自然、符合中国课堂教学风格
- 支持并行生成，提高处理效率

### 4. 智能内容合并
- 自动合并各环节逐字稿为完整文档
- 保持内容的连贯性和逻辑性
- 生成最终的教学逐字稿文件

## 技术架构

### 核心技术栈
- **LangGraph**：构建状态机工作流，管理复杂的处理流程
- **LangChain**：AI 模型集成和管理框架
- **PyMuPDF (fitz)**：PDF 文档处理和图像转换
- **阿里云 OSS**：文件存储和管理
- **多 AI 模型支持**：智谱 AI、SiliconFlow、DashScope 等

### 架构设计模式
- **状态机模式**：使用 LangGraph 管理复杂的工作流状态
- **策略模式**：支持多种 AI 模型提供商的动态切换
- **装饰器模式**：使用 @traceable 进行全流程监控和追踪
- **工厂模式**：动态创建和管理 AI 客户端实例

## 目录结构

```
transcript_graph/
├── __init__.py              # 模块入口，导出主要类和函数
├── doc/                     # 项目文档目录
│   ├── 00-LangGraph入门指南.md # LangGraph基础概念和入门教程
│   ├── 01-项目概述.md       # 本文档
│   ├── 02-状态管理详解.md   # 状态管理系统详解
│   ├── 03-处理器组件详解.md # 各处理器组件详解
│   ├── 04-工作流图详解.md   # 工作流程图详解
│   ├── 04-1-工作流执行示例详解.md # 具体示例演示工作流执行过程
│   ├── 05-提示词系统详解.md # 提示词系统详解
│   ├── 06-配置和环境.md     # 配置管理和环境设置
│   ├── 07-API使用指南.md    # API 接口使用指南
│   ├── 08-开发扩展指南.md   # 开发和扩展指南
│   └── 10-调试式用例-代码执行追踪.md # 调试视角的代码执行追踪
├── graph.py                 # 工作流图定义
├── processors.py            # 处理器组件实现
├── prompts.py              # 提示词系统
└── state.py                # 状态管理
```

## 核心组件

### 1. 状态管理 (state.py)
- **TranscriptState**：主状态类，管理整个工作流的状态
- **TranscriptInput/Output**：输入输出状态定义
- **CourseInfo**：课程信息模型
- **TeachingProcessInfo**：教学环节信息模型
- **Router**：路由状态，跟踪当前处理阶段

### 2. 处理器组件 (processors.py)
- **TeachingProcessTxtGenerateAgent**：文本教学流程生成器
- **TeachingProcessVisionGenerateAgent**：视觉教学流程生成器
- **TranscriptSectionGenerateAgent**：逐字稿片段生成器
- **TranscriptSectionsMergeAgent**：逐字稿合并器
- **FileProcessAgent**：文件处理器

### 3. 工作流图 (graph.py)
- 定义完整的工作流程图
- 管理节点间的条件跳转
- 实现并行任务分发机制
- 处理错误和异常情况

### 4. 提示词系统 (prompts.py)
- **TEACHING_PROCESS_TXT_SYSTEM_PROMPT**：文本教学流程生成提示词
- **TEACHING_PROCESS_VISION_SYSTEM_PROMPT**：视觉教学流程生成提示词
- **TRANSCRIPT_SECTION_SYSTEM_PROMPT**：逐字稿片段生成提示词

## 工作流程概览

```mermaid
graph TD
    A[开始] --> B[处理输入信息]
    B --> C{输入类型判断}
    C -->|文本| D[生成文本教学流程]
    C -->|PDF| E[生成视觉教学流程]
    D --> F[并行生成逐字稿片段]
    E --> F
    F --> G[合并逐字稿片段]
    G --> H[输出最终逐字稿]
    H --> I[结束]
```

## 特色功能

### 1. 并行处理能力
- 支持多个教学环节的并行逐字稿生成
- 使用 LangGraph 的 Send 机制实现任务分发
- 显著提高处理效率和响应速度

### 2. 多模态支持
- 同时支持文本和图像输入
- PDF 文档自动转换为图像进行 AI 分析
- 智能融合多种输入源的信息

### 3. 可追踪性
- 全流程使用 @traceable 装饰器
- 详细记录每个处理步骤
- 便于调试和性能优化

### 4. 高度可配置
- 支持多种 AI 模型提供商
- 灵活的参数配置
- 环境变量驱动的配置管理

## 应用场景

- **教育机构**：快速生成标准化教学逐字稿
- **在线教育平台**：批量制作课程逐字稿
- **教师个人**：辅助备课和教学设计
- **教学研究**：分析和改进教学方法

## 下一步阅读

### 对于LangGraph初学者
如果你对LangGraph不熟悉，强烈建议先阅读：
- [LangGraph入门指南](00-LangGraph入门指南.md) - 从零开始学习LangGraph的核心概念

### 完整学习路径
建议按以下顺序阅读详细文档：
1. **[LangGraph入门指南](00-LangGraph入门指南.md)** - 理解LangGraph基础概念（初学者必读）
2. **[状态管理详解](02-状态管理详解.md)** - 了解系统的状态管理机制
3. **[处理器组件详解](03-处理器组件详解.md)** - 深入了解各个处理器的功能
4. **[工作流图详解](04-工作流图详解.md)** - 理解完整的工作流程
5. **[工作流执行示例详解](04-1-工作流执行示例详解.md)** - 通过具体示例看见工作流执行过程
6. **[调试式用例-代码执行追踪](10-调试式用例-代码执行追踪.md)** - 调试视角理解代码执行过程
7. **[API使用指南](07-API使用指南.md)** - 学习如何使用系统
8. **[配置和环境](06-配置和环境.md)** - 了解部署和配置
9. **[提示词系统详解](05-提示词系统详解.md)** - 深入AI交互机制
10. **[开发扩展指南](08-开发扩展指南.md)** - 进行定制开发

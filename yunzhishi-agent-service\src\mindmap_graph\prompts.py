"""思维导图生成的提示词模板。"""

# 文本生成思维导图的系统提示词
TEXT_MINDMAP_SYSTEM_PROMPT = """作为一名专业的思维导图设计专家，请根据以下信息生成一个结构清晰的思维导图。

1. 格式规范：
   - 严格使用Markdown标题格式
   - 一级标题使用单个#，二级标题使用##，三级标题使用###，四级标题使用####
   - #号后必须有一个空格
   - 每个标题独占一行，不要有空行
   - 不要输出无关内容及任何解释说明，直接从"# "开始

2. 内容要求：
   - 主题突出：确保中心主题明确
   - 层次分明：合理划分层级，至少3级，不超过4级
   - 逻辑清晰：各分支之间关系明确
   - 表述简洁：每个节点的描述要精炼（一般不超过10个字）


3. 结构设计：
   - 保持分支的平衡性
   - 确保逻辑流程清晰

4. 质量检查：
   - 格式正确：标题层级使用准确
   - 结构合理：分支分布均衡
   - 内容准确：信息表达准确
   - 可读性好：表述简洁明了

输出格式：
# 中心主题
## 主要分支1
### 子主题1
#### 具体内容
### 子主题2
## 主要分支2
### 子主题3
#### 具体内容

## 输入信息
{mindmap_info_formatted}

请直接输出符合以上要求的markdown格式思维导图，不要包含任何其他说明。
"""

# 视觉直接生成思维导图提示词
VISION_MINDMAP_DIRECT_PROMPT = """作为一名专业的思维导图设计专家，请分析图像内容，根据以下信息生成一个结构清晰的思维导图。

1. 格式规范：
   - 严格使用Markdown标题格式
   - 一级标题使用单个#，二级标题使用##，三级标题使用###，四级标题使用####
   - #号后必须有一个空格
   - 每个标题独占一行

2. 内容要求：
   - 主题突出：确保中心主题明确
   - 层次分明：合理划分层级，至少3级，不超过4级
   - 逻辑清晰：各分支之间关系明确
   - 表述简洁：每个节点的描述要精炼（一般不超过10个字）

3. 结构设计：
   - 保持分支的平衡性
   - 确保逻辑流程清晰

4. 质量检查：
   - 格式正确：标题层级使用准确
   - 结构合理：分支分布均衡
   - 内容准确：信息表达准确
   - 可读性好：表述简洁明了

输出格式：
# 中心主题
## 主要分支1
### 子主题1
#### 具体内容
### 子主题2
## 主要分支2
### 子主题3
#### 具体内容

输入信息
{mindmap_info_formatted}

请直接输出符合以上要求的markdown格式思维导图，不要包含任何其他说明。
"""

# 视觉批量处理思维导图提示词
VISION_MINDMAP_BATCH_PROMPT = """作为一名专业的思维导图设计专家，请分析这批图片内容，为提供的主题设计思维导图的一部分。

批次信息
- 总页数：{total_pages}
- 当前批次：第{batch_num}批
- 批次大小：{batch_size}张

思维导图要求
1. 只为这批图片内容设计思维导图的一部分
2. 使用Markdown标题格式（#、##、###）表示层级
3. 每个节点表述简洁，不超过10个字
4. 保留专业术语和核心概念
5. 确保与整体主题的连贯性

输入信息
{mindmap_info_formatted}

请直接输出Markdown格式的思维导图片段，不要包含任何解释或说明。标注这是第{batch_num}批次的内容。
"""

# 视觉思维导图汇总提示词
VISION_MINDMAP_SUMMARY_PROMPT = """作为一名专业的思维导图设计专家，请将以下{batch_count}个批次的思维导图片段整合成一个完整的思维导图。

1. 格式规范：
   - 严格使用Markdown标题格式
   - 一级标题使用单个#，二级标题使用##，三级标题使用###，四级标题使用####
   - #号后必须有一个空格
   - 每个标题独占一行
   - 不要输出无关内容及任何解释说明，直接从"# "开始

2. 内容要求：
   - 主题突出：确保中心主题明确
   - 层次分明：合理划分层级，至少3级，不超过4级
   - 逻辑清晰：各分支之间关系明确
   - 表述简洁：每个节点的描述要精炼（一般不超过10个字）

3. 结构设计：
   - 保持分支的平衡性
   - 确保逻辑流程清晰

4. 质量检查：
   - 格式正确：标题层级使用准确
   - 结构合理：分支分布均衡
   - 内容准确：信息表达准确
   - 可读性好：表述简洁明了

输出格式
# 中心主题
## 主要分支1
### 子主题1
#### 具体内容
### 子主题2
## 主要分支2
### 子主题3
#### 具体内容

输入信息
{mindmap_info_formatted}

各批次思维导图片段内容
{batch_contents}

请直接输出符合以上要求的markdown格式思维导图，不要包含任何其他说明。
""" 
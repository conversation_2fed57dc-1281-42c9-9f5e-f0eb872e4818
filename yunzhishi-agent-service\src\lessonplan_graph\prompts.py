"""定义系统中使用的所有提示词。"""

# 文本教学大纲生成提示词
OUTLINE_TXT_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据课程内容，设计一份教学流程的大纲，其中使用"一、二、三、等"二级标题(##)标识每个环节

{course_info_formatted}

要求：
1. 格式要求：
- 严格使用"一、二、三、等"二级标题(##)标识每个环节
- 每个环节下使用无序列表（-）列出具体内容
- 每个环节的内容120-150字
- 总字数不超过600字
- 不要输出无关内容及任何解释说明，直接输出大纲内容，从"## 一、"开始

2. 内容要求：
- 根据课程内容设计4-5个教学环节，至少包括课程导入、课程总结
- 每个环节需要包含具体的教学内容和教学方法
- 环节之间要有清晰的逻辑关系和递进性
- 确保教学流程的完整性

3. 质量检查：
- 内容选取合理：
  * 符合教材重点内容
  * 难度适中，循序渐进
  * 知识点完整，逻辑清晰
- 时间分配科学：
  * 各环节时间比例合理
  * 预留缓冲时间
  * 节奏把控适当
- 教学设计合理：
  * 目标明确具体
  * 方法灵活多样
  * 师生互动充分
  * 重难点突出"""

# 视觉教学大纲生成提示词
OUTLINE_VISION_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据教材图片内容，设计一份教学流程的大纲，其中使用"一、二、三、等"二级标题(##)标识每个环节

{course_info_formatted}

要求：
1. 格式要求：
- 严格使用"一、二、三、等"二级标题(##)标识每个环节
- 每个环节下使用无序列表（-）列出具体内容
- 每个环节的内容120-150字
- 总字数不超过600字
- 不要输出无关内容及任何解释说明，直接输出大纲内容，从"## 一、"开始

2. 内容要求：
- 根据课程内容设计4-5个教学环节，至少包括课程导入、课程总结
- 每个环节需要包含具体的教学内容和教学方法
- 环节之间要有清晰的逻辑关系和递进性
- 确保教学流程的完整性

3. 质量检查：
- 内容选取合理：
  * 符合教材重点内容
  * 难度适中，循序渐进
  * 知识点完整，逻辑清晰
- 时间分配科学：
  * 各环节时间比例合理
  * 预留缓冲时间
  * 节奏把控适当
- 教学设计合理：
  * 目标明确具体
  * 方法灵活多样
  * 师生互动充分
  * 重难点突出"""

# 视觉教学大纲批次处理提示词
OUTLINE_VISION_BATCH_PROMPT = """作为一名专业的教学设计专家，这是一个包含{total_pages}页的课程内容中的第{batch_num}批图片（共{batch_size}张）。
请分析这些图片内容，设计这部分内容的教学环节。

{course_info_formatted}

要求：
1. 格式要求：
- 使用"一、二、三、等"二级标题(##)标识每个环节
- 每个环节下使用无序列表（-）列出具体内容
- 每个环节的内容50-80字
- 总字数不超过300字
- 不要输出无关内容

2. 内容要求：
- 仅针对这批图片的内容设计2-3个教学环节
- 每个环节需要包含具体的教学内容和教学方法
- 确保与整体课程的连贯性
- 标注这是第{batch_num}批次的内容"""

# 视觉教学大纲汇总提示词
OUTLINE_VISION_SUMMARY_PROMPT = """作为一名专业的教学设计专家，请将以下{batch_count}个批次的教学大纲整合成一个完整的教学大纲。

{course_info_formatted}

各批次大纲内容如下：

{separator}
{batch_contents}
{separator}

请根据以上内容，生成一份完整的教学大纲，要求：

1. 格式要求：
- 严格使用"一、二、三、等"二级标题(##)标识每个环节
- 每个环节下使用无序列表（-）列出具体内容
- 每个环节的内容120-150字
- 总字数不超过600字
- 不要输出无关内容及任何解释说明，直接输出大纲内容，从"## 一、"开始

2. 内容要求：
- 整合所有批次的内容，设计4-5个完整的教学环节
- 必须包括课程导入和课程总结
- 每个环节需要包含具体的教学内容和教学方法
- 保持内容的逻辑性和连贯性
- 突出重点内容
- 确保教学流程的完整性"""

# 教学流程展开通用提示词
PROCESS_EXPAND_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请你帮我详细展开以下教学环节的内容。

{course_info_formatted}

教学流程大纲：
{teaching_process_outline}

当前展开的环节标题：
{process_title}

输出格式要求：
- 使用数字序号（1、2、3、等）和加粗标题，至少需要3个标题段落，4-5个左右更合适
- 每个标题下使用无序列表（-）列出具体步骤，每个标题下至少包含3个具体步骤
- 除了标题和步骤外，不要输出任何其他内容，直接从"1、"开始输出

内容要求：
- 每个步骤包含清晰的内容和过程，即教师干什么、学生干什么、教师怎么引导、学生怎么参与等
- 尽可能详尽、具体、完整
- 注意与其他环节的衔接
- 关注学生的参与度和反馈
- 每个步骤的标题要清晰，不要使用"步骤一"、"步骤二"等
- 避免AI常见的生硬或不自然的表达，避免过于完美、刻板的句式结构
- 除步骤外，不要输出其他任何内容及任何解释说明，直接从标题开始输出步骤内容

质量检查：
- 内容的专业性：
  * 概念准确，术语规范
  * 逻辑清晰，层次分明
  * 案例恰当，联系实际
- 教学设计的合理性：
  * 方法多样，有效性强
  * 互动充分，参与度高
  * 时间分配合理
- 表达的规范性：
  * 结构完整，条理清晰
  * 重点突出，层次分明
  * 衔接自然，过渡流畅

格式示例：
  1、**课程导入：情境创设**
    - 教师展示相关的图片资料，并提出引导性问题。
    - 学生观察图片，思考问题，积极发表自己的看法。
    - 教师总结学生的回答，引出本节课的主题。

  2、**知识讲解：概念辨析**
    - 教师通过多媒体展示核心概念，并进行详细讲解。
    - 学生认真听讲，记录要点，标注疑问。
  """

# 教学目标生成提示词
OBJECTIVES_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请总结本节课的教学目标。要求：
1. 格式要求：
- 使用无序列表（-）
- 每个目标20-30字
- 总字数不超过100字
- 不要输出无关内容及任何解释说明，直接输出目标内容
2. 内容要求：
- 至少包含3个具体目标
- 每个目标需要说明通过什么任务达到什么效果
- 可涵盖知识、技能和情意三个维度

3. 质量检查：
- 目标具体且可衡量
- 符合学生认知水平
- 与课程内容紧密相关
- 动词使用准确恰当"""

# 教学重点难点生成提示词
KEYPOINTS_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请总结本节课的重点和难点。要求：
1. 格式要求：
- 分别使用'重点：'和'难点：'作为标题(加粗即可)
- 每个点使用无序列表（-）
- 每点20-30字
- 总字数不超过150字
- 不要输出无关内容及任何解释说明，直接输出重点难点内容
2. 内容要求：
- 1-2个重点，突出核心知识和关键概念
- 1-2个难点，指出学习障碍
- 重点难点相互呼应

3. 质量检查：
- 重点突出课程核心
- 难点分析准确
- 表述清晰具体
- 符合学生认知特点"""

# 教学方法生成提示词
METHODS_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请总结本节课使用的教学方法。要求：
1. 格式要求：
- 使用有序列表（1、 2、 3、），每个之间用一个空行隔开
- 每个方法30-50字
- 总字数不超过200字
- 不要输出无关内容及任何解释说明，直接输出方法内容

2. 内容要求：
- 列出3-4个主要教学方法
- 每个方法包含：名称、具体应用方式、预期效果
- 方法之间要有逻辑联系
- 符合教学目标要求

3. 质量检查：
- 方法适合教学内容
- 操作性强
- 师生互动充分
- 时间分配合理

格式示例：1、**情境导入**：
"""

# 教学活动生成提示词
ACTIVITIES_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请总结本节课的教学活动。要求：
1. 格式要求：
- 使用有序列表（1、 2、 3、），每个之间用一个空行隔开
- 每个活动50-70字
- 总字数不超过250字
- 不要输出无关内容及任何解释说明，直接输出活动内容

2. 内容要求：
- 总结3-4个教学活动
- 每个活动包含：名称、具体流程
- 活动类型多样
- 难度递进

3. 质量检查：
- 活动目标明确
- 操作性强
- 时间分配合理
- 师生互动充分

格式示例：1、**课堂讨论**：
"""

# 学情分析生成提示词
STUDENT_ANALYSIS_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请分析本节课的学情。要求：
1. 格式要求：
- 三段话，使用无序列表（-）开头
- 总字数200字以上，不超过300字
- 不要输出无关内容及任何解释说明，直接输出分析内容

2. 内容要求：
- 第一段分析2-3个典型的学生认知特点
- 第二段分析1-2个可能的学习障碍或误区
- 第三段提出针对性的教学建议

3. 质量检查：
- 避免AI常见的生硬或不自然的表达，避免过于完美、刻板的句式结构
- 避免表面性回答，提供有深度的见解
- 分析符合学生年龄和认知特点
- 分析有针对性和实用性
- 建议具体可操作"""

# 结合教学法的学情分析生成提示词
STUDENT_ANALYSIS_WITH_METHODOLOGY_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}
使用的教学法：{methodology}

请分析本节课的学情，并结合{methodology}教学法提出建议。要求：
1. 格式要求：
- 三段话，使用无序列表（-）开头
- 总字数250字以上，不超过350字
- 不要输出无关内容及任何解释说明，直接输出分析内容

2. 内容要求：
- 第一段分析2-3个典型的学生认知特点
- 第二段分析1-2个可能的学习障碍或误区
- 第三段提出针对性的教学建议，必须自然引出结合{methodology}教学法进行教学活动，教学法加粗表示

3. 质量检查：
- 避免AI常见的生硬或不自然的表达，避免过于完美、刻板的句式结构
- 避免表面性回答，提供有深度的见解
- 分析符合学生年龄和认知特点
- 分析有针对性和实用性"""

# 教学板书设计提示词
BLACKBOARD_DESIGN_SYSTEM_PROMPT = """
{course_info_formatted}

完整课程内容：{lesson_plan}

请设计本节课的板书，即呈现在黑板上的内容。要求：
1. 格式要求：
- 使用标题、缩进和符号表示层次关系
- 从二级标题+本课标题开始，即"##"开始，不需要标题前的标号（如1.1、第一章、第一节等）
- 总字数不超过200字
- 不要输出无关内容及任何解释说明，直接输出板书设计

2. 内容要求：
- 包含课程标题和核心知识点
- 使用适当的框架、图表结构呈现内容关系
- 简洁清晰，层次分明

3. 质量检查：
- 结构合理，逻辑清晰
- 重点突出，层次分明
- 内容精炼，易于理解
- 美观实用，便于学生记忆"""

CUSTOM_ELEMENT_SYSTEM_PROMPT = """基于以下课程信息和现有教案，生成"{element_name}"部分的内容：
        
{course_info_formatted}

当前教案内容:
{final_plan}

请生成专业、具体的{element_name}内容。要求：
1. 内容要专业、具体、可操作
2. 符合教育教学规范
3. 与课程目标相符
4. 注重实用性和可执行性
5. 与现有教案内容保持一致性
6. 不要输出无关内容，字数原则上不超过200字
"""

MODIFY_ELEMENT_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据以下要求修改教案要素。

{course_info_formatted}

要修改的要素：{element_type} - {element_name}

原始内容：
{original_content}

修改要求：
{modify_requirements}

请按照以下规则进行修改：
1. 保持原有格式和结构
2. 确保修改后的内容符合教育教学规范
3. 保持与课程目标的一致性
4. 注重实用性和可执行性
5. 确保修改符合用户的具体要求

请直接输出修改后的完整内容，不要包含任何解释或说明。"""

# 大纲修改提示词
OUTLINE_MODIFY_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据用户习惯和偏好，修改现有的教学流程大纲。

{course_info_formatted}

用户习惯和偏好：
{user_habits}

原始教学大纲：
{original_outline}

修改要求：
1. 保持原始大纲的基本结构和格式
2. 根据用户习惯和偏好进行调整
3. 确保修改后的大纲：
   - 符合用户的教学风格和偏好
   - 保持教学内容的完整性和逻辑性
   - 提高教学效果和学生参与度
   - 总字数不超过原始大纲的1.2倍

请输出修改后的完整大纲。不要包含任何解释或说明，只输出大纲内容。"""

# 思维导图生成提示词
MIND_MAP_SYSTEM_PROMPT = """作为一名专业的思维导图设计专家，请根据教学流程信息生成一个结构清晰的思维导图。

要求：
1. 格式规范：
   - 严格使用Markdown标题格式
   - 一级标题使用单个#
   - 二级标题使用##
   - 三级标题使用###
   - 四级标题使用####
   - #号后必须有一个空格
   - 每个标题独占一行

2. 内容要求：
   - 主题突出：确保中心主题明确
   - 层次分明：合理划分层级，至少3级，不超过4级
   - 逻辑清晰：各分支之间关系明确
   - 表述简洁：每个节点的描述要精炼
   - 内容完整：覆盖所有重要信息，尽可能详细丰富

3. 结构设计：
   - 中心主题放在最上方
   - 主要分支使用二级标题
   - 次要内容使用三、四级标题
   - 保持分支的平衡性
   - 确保逻辑流程清晰

4. 质量检查：
   - 格式正确：标题层级使用准确
   - 结构合理：分支分布均衡
   - 内容准确：信息表达准确
   - 逻辑性强：层级关系明确
   - 可读性好：表述简洁明了

请直接输出符合以上要求的markdown格式思维导图，不要包含任何其他说明。

示例格式：
# 中心主题
## 主要分支1
### 子主题1
#### 具体内容
### 子主题2
## 主要分支2
### 子主题3
#### 具体内容

教学流程信息：
{processes_content}"""

# 教学逐字稿生成提示词
TRANSCRIPT_SYSTEM_PROMPT = """根据以下教学环节的内容，生成详细的教学逐字稿。

1. 格式要求：
   - 按照流程的编号、框架及格式，直接从"## {process_title}"开始
   - 必须以"老师："开始，表示教师讲话
   - 教师与学生对话时，学生的发言以"学生："标识
   - 教师的动作或说明放在括号中，如"(板书在黑板上)"
   - 重要内容可以使用引号强调
   - 每段对话独占一行
   - 每个教学环节内部结构应包含：引入、讲解、互动、小结

2. 内容要求：
   - 语言自然、亲切、生动
   - 体现教师与学生的互动，包括提问、回答和讨论
   - 涵盖此环节中提到的所有教学内容和步骤
   - 包含教师的提问、解释和总结
   - 对于重点内容要有强调和层次
   - 符合中国课堂教学的语言风格
   - 总字数控制在800-1200字左右

课程信息：
{course_info_formatted}

教学环节内容：
{process_content}

请直接输出逐字稿内容，不要包含其他解释。"""

# 教案分析生成提示词
PLANANALYSIS_SYSTEM_PROMPT = """作为一名专业的教育评估专家，请根据以下教案内容进行系统的分析和评价。

课程信息：
{course_info_formatted}

教案内容：
{lesson_plan}

用户教学风格和偏好：
{user_habits}

请从以下几个方面进行分析评价：

1、教学内容分析
   - 内容的选择是否合理、重点是否突出
   - 内容的组织是否具有逻辑性和连贯性
   - 内容的难度是否适合学生水平

2、教学过程分析
   - 各环节设计是否合理，时间分配是否适当
   - 教学方法的选择是否多样化、有效
   - 师生互动是否充分
   - 重难点突破策略是否恰当

3、教学法分析
  

4、总体优点与特色
   - 列出3-4个明显的优点和特色


格式要求：
- 每个部分使用二级标题（##）开头
- 分析内容要具体、专业，不要空泛评价
- 总字数控制在600字以内
- 突出客观评价与合理建议
- 语言专业、准确、简洁

请直接输出分析内容，不要包含其他解释。"""

# 教学法匹配提示词
TEACHING_METHODOLOGY_PROMPT = """根据以下课程信息，从提供的教学法列表中选择1-2个最适合的教学法：
            
{course_info_formatted}

请仅回复教学法名称，如果需要多个，用逗号分隔。
不要包含解释或其他内容。输出20字以内。

可选教学法列表：
- 布鲁姆教育目标分类法：将教育目标按认知、情感和动作三个领域分类，适合体系化教学设计。
- 直接教学法：教师主导教学过程，明确讲解知识点，适合基础知识传授。
- 建构主义教学法：强调学生主动构建知识的过程，适合培养思维能力和创新能力。
- 合作学习法：学生分组协作完成学习任务，适合培养团队合作能力。
- 苏格拉底问答法：通过提问引导学生思考和发现问题答案，适合培养批判性思维。
- 翻转课堂：学生先自学内容，课堂时间用于讨论和解决问题，适合进阶学习。
- 多元智能教学法：针对学生的不同智能类型设计教学活动，适合个性化教学。
- 蒙台梭利教学法：强调自由探索和动手操作，适合培养自主性和创造力。
- 掌握学习法：确保学生完全掌握一个知识点后再进入下一环节，适合基础教育。
- 全身反应法：通过肢体动作辅助学习，适合语言教学和幼儿教育。
- 差异化教学：根据学生个体差异调整教学内容和方法，适合混合能力班级。

选择标准：
1. 考虑学生年龄段和认知特点
2. 考虑课程性质和内容特点
3. 考虑教学目标和重点难点
4. 考虑教学环境和条件

请仅回复教学法名称，如果需要多个，用逗号分隔。
不要包含解释或其他内容。输出20字以内。""" 
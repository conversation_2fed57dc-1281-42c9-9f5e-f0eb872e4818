"""AI演示文稿生成服务器数据模型"""
from typing import List, Optional
from pydantic import BaseModel, Field


class PPTInfo(BaseModel):
    """PPT信息模型"""
    topic: str = Field(default="演示文稿", description="演示文稿主题")
    reference_content: str = Field(description="参考资料内容（字符串格式）")


class PPTSlide(BaseModel):
    """PPT幻灯片模型"""
    slide_number: int = Field(description="幻灯片编号")
    slide_type: str = Field(
        description="幻灯片类型（如：cover, content, data等）"
    )
    title: str = Field(description="幻灯片标题")
    content: List[str] = Field(description="幻灯片内容要点")
    notes: Optional[str] = Field(default="", description="设计备注")
    html_content: Optional[str] = Field(default="", description="生成的HTML内容")


class PPTOutline(BaseModel):
    """PPT大纲模型"""
    title: str = Field(description="演示文稿标题")
    slides: List[PPTSlide] = Field(description="幻灯片列表")


class PPTGenerationState(BaseModel):
    """PPT生成状态模型"""
    reference_content: str = Field(default="", description="参考资料内容")
    html_specification: str = Field(default="", description="HTML规范")
    # editable_guidelines: str = Field(default="", description="可编辑指导")  # 暂时不需要可编辑功能
    outline: Optional[PPTOutline] = Field(default=None, description="PPT大纲")
    generated_slides: List[str] = Field(
        default_factory=list, 
        description="生成的HTML文件路径列表"
    )
    index_page_path: Optional[str] = Field(
        default=None, 
        description="索引页面路径"
    ) 
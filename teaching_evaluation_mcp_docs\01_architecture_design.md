# Teaching Evaluation MCP 项目架构设计

## 1. 项目概述

### 1.1 项目目标
基于 `picturebook_generator_mcp` 的架构模式，构建一个专业的课堂教学分析 MCP 服务，实现：
- 接收课堂教学转录文本输入
- 使用 DashScope DeepSeek-V3 模型进行智能分析
- 生成结构化的教学分析报告
- 提供完整的元数据和用量统计

### 1.2 核心特性
- **MCP 协议优先**：完全基于 MCP 协议的实时通信
- **流式反馈**：实时进度报告和状态更新
- **模块化架构**：高度解耦的组件设计
- **完整日志**：详细的操作日志和错误追踪
- **用量统计**：精确的模型调用成本计算

## 2. 技术架构

### 2.1 整体架构图
```
teaching_evaluation_mcp/
├── main.py                     # MCP服务器入口
├── pyproject.toml              # 项目依赖配置
├── .env.example                # 环境变量示例
├── src/
│   └── teaching_evaluation/
│       ├── __init__.py         # 模块初始化
│       ├── config.py           # 配置管理
│       ├── models.py           # 数据模型
│       ├── logger.py           # 日志系统
│       ├── ai_client.py        # AI客户端
│       ├── prompt_manager.py   # 提示词管理
│       ├── chart_generator.py  # 雷达图生成
│       └── analyzer.py         # 教学分析服务
├── tests/
│   └── test_mcp_server.py      # MCP客户端测试
├── docs/                       # 规划和设计文档
├── logs/                       # 日志文件目录
└── outputs/                    # 分析报告输出目录
```

### 2.2 核心组件设计

#### 2.2.1 配置管理 (config.py)
```python
class TeachingEvaluationConfig(BaseModel):
    # AI服务配置
    ai_api_key: str
    ai_provider: str = "dashscope"
    ai_model_name: str = "deepseek-v3"
    ai_timeout: int = 120
    ai_max_retries: int = 3
    
    # 分析参数
    max_tokens: int = 8000
    temperature: float = 0.7
    
    # 输出配置
    output_dir: str = "outputs"
    
    # 日志配置
    log_dir: str = "logs"
    log_level: str = "INFO"
    log_console: bool = True
```

#### 2.2.2 数据模型 (models.py)
```python
class TeachingTranscript(BaseModel):
    """课堂教学转录文本"""
    content: str
    metadata: Optional[Dict[str, Any]] = None

class AnalysisRequest(BaseModel):
    """分析请求"""
    transcript: str
    analysis_id: Optional[str] = None
    custom_requirements: Optional[str] = None

class AnalysisReport(BaseModel):
    """分析报告"""
    analysis_id: str
    subject: str
    overall_evaluation: str
    five_dimensions: Dict[str, Any]
    summary_and_suggestions: str
    metadata: Dict[str, Any]

class ModelUsageInfo(BaseModel):
    """模型用量信息（基于参考项目设计）"""
    vendor: str = Field(description="提供模型的厂商，例如：dashscope, doubao")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")
```

#### 2.2.3 AI客户端 (ai_client.py)
基于 `transcript_generator_mcp` 的 DashScope 客户端，适配教学分析场景：
- 支持 DeepSeek-V3 模型调用
- 实现重试机制和错误处理
- 提供用量统计和成本计算
- 支持流式和非流式调用

#### 2.2.4 提示词管理 (prompt_manager.py)
```python
class PromptManager:
    """简化的提示词管理器"""

    @staticmethod
    def build_analysis_prompt(transcript: str, custom_requirements: str = "") -> str:
        """构建统一的教学分析提示词"""
        
```

#### 2.2.5 雷达图生成 (chart_generator.py)
```python
class RadarChartGenerator:
    """基于Plotly的雷达图生成器"""

    def __init__(self, output_dir: str = "outputs"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def extract_scores_from_analysis(self, analysis_text: str) -> Dict[str, float]:
        """从AI分析结果中提取五维度评分"""

    def generate_radar_chart(self, scores: Dict[str, float],
                           title: str = "教学评价雷达图") -> Tuple[str, str]:
        """生成雷达图，返回HTML和PNG文件路径"""
```

#### 2.2.6 教学分析服务 (analyzer.py)
```python
class TeachingAnalyzer:
    """教学分析服务"""
    
    async def analyze_teaching(
        self, 
        request: AnalysisRequest, 
        ctx: Context
    ) -> Tuple[AnalysisReport, ModelUsageInfo]:
        """执行教学分析"""
```

### 2.3 数据流设计

```
输入转录文本 → 提示词构建 → AI模型调用 → 结果解析 → 雷达图生成 → 报告生成 → 文件输出
     ↓              ↓           ↓          ↓         ↓         ↓         ↓
  验证格式      统一模板    实时进度    结构化解析   图表生成   元数据生成  用量统计
```

## 3. 关键技术决策

### 3.1 架构继承策略
- **完全继承** `picturebook_generator_mcp` 的项目结构
- **复用** 日志系统设计模式
- **适配** 配置管理和数据模型
- **集成** DashScope AI 客户端

### 3.2 命名规范
- 项目名：`teaching_evaluation_mcp`
- 模块名：`teaching_evaluation`
- 主要类：`TeachingEvaluationConfig`, `TeachingAnalyzer`
- 日志器：`TeachingEvaluationLogger`

### 3.3 文件命名
- 配置文件：遵循 `picturebook_generator_mcp` 模式
- 日志文件：`teaching_evaluation_YYYYMMDD.log`
- 输出文件：`analysis_report_{timestamp}.md` + `metadata_{timestamp}.json`
- 雷达图文件：
  - HTML格式：`radar_chart_{timestamp}.html`
  - PNG格式：`radar_chart_{timestamp}.png`

## 4. 接口设计

### 4.1 MCP 工具接口
```python
@mcp.tool()
async def analyze_teaching_transcript(
    transcript: str,
    ctx: Context,
    custom_requirements: str = ""
) -> str:
    """分析课堂教学转录文本
    
    Args:
        transcript (str): 课堂教学转录文本
        ctx (Context): MCP上下文，由框架自动注入
        custom_requirements (str, optional): 用户自定义分析要求
    
    Returns:
        str: JSON格式的分析结果
    """
```

### 4.2 实时通信协议
```python
# 进度状态消息
class ProgressUpdate(BaseModel):
    status: str  # "started", "analyzing", "generating", "completed"
    message: str
    progress: Optional[float] = None
    details: Optional[Dict[str, Any]] = None

# 用量报告消息
class UsageReport(BaseModel):
    status: str = "model_usage"
    usage: ModelUsageInfo
```

## 5. 错误处理策略

### 5.1 错误分类
- **输入验证错误**：转录文本格式不正确
- **AI服务错误**：API调用失败、超时、限流
- **解析错误**：AI返回结果格式异常
- **文件系统错误**：输出目录不可写

### 5.2 错误处理机制
```python
try:
    # 业务逻辑
    result = await analyzer.analyze_teaching(request, ctx)
except ValidationError as e:
    await ctx.error({
        "type": "input_validation_error",
        "message": "输入数据格式错误",
        "details": str(e)
    })
except AIServiceError as e:
    await ctx.error({
        "type": "ai_service_error", 
        "message": "AI服务调用失败",
        "details": str(e)
    })
```

## 6. 性能考虑

### 6.1 响应时间优化
- 异步处理：全程使用 async/await
- 流式输出：实时返回分析进度
- 超时控制：合理设置AI调用超时

### 6.2 资源管理
- 内存控制：及时释放大文本对象
- 文件管理：自动清理过期日志
- 连接池：复用HTTP连接

## 7. 安全考虑

### 7.1 敏感信息保护
- API密钥通过环境变量管理
- 日志中不记录敏感信息
- 输出文件权限控制

### 7.2 输入验证
- 转录文本长度限制
- 特殊字符过滤
- 注入攻击防护

## 8. 可扩展性设计

### 8.1 模型支持扩展
- 抽象AI客户端接口
- 支持多种AI服务提供商
- 可配置的模型参数

### 8.2 分析维度扩展
- 可插拔的分析模块
- 自定义评价框架
- 多语言支持

## 9. 部署考虑

### 9.1 环境要求
- Python 3.8+
- FastMCP 框架
- DashScope SDK

### 9.2 配置管理
- 环境变量配置
- 配置文件验证
- 默认值设置

## 10. 监控和运维

### 10.1 日志策略
- 结构化日志输出
- 多级别日志记录
- 日志轮转和归档

### 10.2 指标监控
- 请求处理时间
- AI调用成功率
- 资源使用情况
- 成本统计

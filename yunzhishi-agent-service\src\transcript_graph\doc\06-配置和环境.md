# 配置和环境管理

## 概述

Transcript Graph 项目采用多层次的配置管理策略，通过环境变量、配置文件和代码配置相结合的方式，实现了灵活、安全、可扩展的配置管理。本文档详细介绍项目的配置体系、环境设置和部署要求。

## 配置架构

### 配置层次结构

```
环境变量 (.env)
    ↓
配置类 (LessonPlanConfiguration)
    ↓
工作流配置 (ConfigSchema)
    ↓
运行时配置 (RunnableConfig)
```

### 配置优先级

1. **环境变量**：最高优先级，用于敏感信息和部署配置
2. **配置文件**：中等优先级，用于业务逻辑配置
3. **默认值**：最低优先级，确保系统基本可用

## 环境变量配置

### .env 文件结构

<augment_code_snippet path="yunzhishi-agent-service/.env" mode="EXCERPT">
````bash
# AI模型API密钥
ZHIPUAI_API_KEY="c846c078f9de313b9c719ee45616acbc.3bR6DRbrKBogRleJ"
SILICONFLOW_API_KEY="sk-cwndcilrhkoqjzkajmngunhysxezogotvxrfzuajrlsvxybo"
DASHSCOPE_API_KEY="sk-aff1dca53ba24d2eb419d9f2bdf2e2a6"

# 阿里云OSS配置
OSS_ACCESS_KEY_ID="LTAI5tSQSyCfA5FnpDeEo8pF"
OSS_ACCESS_KEY_SECRET="******************************"

# LangSmith追踪配置
LANGSMITH_API_KEY="***************************************************"

# LangChain服务配置
LANGCHAIN_HOST=0.0.0.0
LANGCHAIN_BACKEND_HOST=0.0.0.0
LANGCHAIN_PORT=2024
LANGCHAIN_SERVE_ALLOW_CREDENTIALS=true
LANGCHAIN_SERVE_CORS_HEADERS=*
LANGCHAIN_SERVE_CORS_ORIGINS=*

# Token统计API配置
TOKEN_API_DOMAIN=https://test-api.zzz4ai.com
````
</augment_code_snippet>

### 环境变量分类

#### 1. AI模型配置
- **ZHIPUAI_API_KEY**: 智谱AI模型API密钥
- **SILICONFLOW_API_KEY**: SiliconFlow模型API密钥
- **DASHSCOPE_API_KEY**: 阿里云百炼模型API密钥

#### 2. 存储配置
- **OSS_ACCESS_KEY_ID**: 阿里云OSS访问密钥ID
- **OSS_ACCESS_KEY_SECRET**: 阿里云OSS访问密钥

#### 3. 监控配置
- **LANGSMITH_API_KEY**: LangSmith追踪服务API密钥

#### 4. 服务配置
- **LANGCHAIN_HOST**: LangChain服务主机地址
- **LANGCHAIN_PORT**: LangChain服务端口
- **TOKEN_API_DOMAIN**: Token统计API域名

### 环境变量安全管理

#### 1. 敏感信息保护
```bash
# 使用.env文件存储敏感信息
# 确保.env文件不被提交到版本控制
echo ".env" >> .gitignore
```

#### 2. 环境变量验证
<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
@property
def txt_api_key(self) -> str:
    """获取文本模型API密钥"""
    if self.txt_model_provider == "zhipuai":
        key = os.getenv("ZHIPUAI_API_KEY")
        if not key:
            raise ValueError("请设置环境变量 ZHIPUAI_API_KEY")
        return key.strip()
    # ... 其他提供商的验证
````
</augment_code_snippet>

## 配置类设计

### LessonPlanConfiguration 核心配置类

<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
@dataclass
class LessonPlanConfiguration:
    """应用配置类"""
    
    # 模型提供商配置
    txt_model_provider: str = field(default="zhipuai")  # zhipuai/siliconflow/dashscope
    vision_model_provider: str = field(default="zhipuai")  # zhipuai/siliconflow/dashscope
    text_model: str = field(default="zhipuai/glm-4-air-0111")
    vision_model: str = field(default="zhipuai/glm-4v-plus-0111")
    
    # 文件处理配置
    temp_dir: str = field(default="temp_files")
    output_dir: str = field(default="outputs")
    
    # OSS配置
    oss_endpoint: str = field(default="oss-cn-shanghai.aliyuncs.com")
    oss_bucket: str = field(default="textbook-pdf2025")
````
</augment_code_snippet>

### 配置字段详解

#### 1. 模型配置
- **txt_model_provider**: 文本模型提供商（zhipuai/siliconflow/dashscope）
- **vision_model_provider**: 视觉模型提供商
- **text_model**: 具体的文本模型名称
- **vision_model**: 具体的视觉模型名称

#### 2. 文件系统配置
- **temp_dir**: 临时文件目录，用于存储处理过程中的临时文件
- **output_dir**: 输出目录，用于存储最终生成的文件

#### 3. 云服务配置
- **oss_endpoint**: 阿里云OSS服务端点
- **oss_bucket**: OSS存储桶名称

### 动态配置加载

<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
@classmethod
def from_runnable_config(cls, config: RunnableConfig) -> "LessonPlanConfiguration":
    """从RunnableConfig创建配置对象"""
    configurable = config.get("configurable", {})
    
    # 确保provider是小写的
    txt_model_provider = str(configurable.get("txt_model_provider", "zhipuai")).lower()
    vision_model_provider = str(configurable.get("vision_model_provider", "zhipuai")).lower()
    
    # 根据txt_model_provider设置默认的text_model
    default_text_model = (
        "THUDM/glm-4-9b-chat" if txt_model_provider == "siliconflow"
        else "deepseek-v3" if txt_model_provider == "dashscope"
        else "zhipuai/glm-4-air-0111"
    )
    
    config_obj = cls(
        txt_model_provider=txt_model_provider,
        vision_model_provider=vision_model_provider,
        text_model=str(configurable.get("text_model", default_text_model)),
        vision_model=str(configurable.get("vision_model", default_vision_model)),
        # ... 其他配置项
    )
    return config_obj
````
</augment_code_snippet>

## 工作流配置模式

### ConfigSchema 定义

<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]
    vision_model_provider: Optional[str]
    text_model: Optional[str]
    vision_model: Optional[str]
    temp_dir: Optional[str]
    output_dir: Optional[str]
    oss_endpoint: Optional[str]
    oss_bucket: Optional[str]
````
</augment_code_snippet>

### 配置传递机制

```python
# 在graph.py中使用配置
workflow = StateGraph(TranscriptState, ConfigSchema, input=TranscriptInput, output=TranscriptOutput)

# 配置传递到各个节点
def create_graph(config: Optional[Dict[str, Any]] = None):
    lesson_plan_config = LessonPlanConfiguration.from_runnable_config(config or {})
    # 使用配置初始化各个组件
```

## 模型配置管理

### 支持的模型提供商

#### 1. 智谱AI (zhipuai)
```python
# 默认模型配置
text_model: "zhipuai/glm-4-air-0111"
vision_model: "zhipuai/glm-4v-plus-0111"

# API密钥获取
api_key = os.getenv("ZHIPUAI_API_KEY")
```

#### 2. SiliconFlow (siliconflow)
```python
# 默认模型配置
text_model: "THUDM/glm-4-9b-chat"
vision_model: "deepseek-vl2"

# API密钥获取
api_key = os.getenv("SILICONFLOW_API_KEY")
```

#### 3. 阿里云百炼 (dashscope)
```python
# 默认模型配置
text_model: "deepseek-v3"
vision_model: "qwen-omni-turbo"

# API密钥获取
api_key = os.getenv("DASHSCOPE_API_KEY")
```

### 模型配置属性

<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
@property
def model_base_url(self) -> str:
    """获取模型基础URL"""
    if self.txt_model_provider == "siliconflow":
        return "https://api.siliconflow.cn/v1"
    elif self.txt_model_provider == "dashscope":
        return "https://dashscope.aliyuncs.com/compatible-mode/v1"
    else:
        return ""  # 智谱AI使用默认URL
````
</augment_code_snippet>

## 文件和存储配置

### 本地文件系统

#### 1. 目录结构
```
project_root/
├── temp_files/          # 临时文件目录
│   ├── pdf_images_*/    # PDF转图像临时目录
│   └── cached_pdfs/     # PDF缓存目录
├── outputs/             # 输出目录
│   ├── transcript_*/    # 逐字稿输出目录
│   └── lesson_plans/    # 教案输出目录
```

#### 2. 目录管理
```python
# 自动创建目录
os.makedirs(config.temp_dir, exist_ok=True)
os.makedirs(config.output_dir, exist_ok=True)

# 临时文件清理
def cleanup_temp_files():
    # 清理过期的临时文件
    pass
```

### 阿里云OSS配置

#### 1. OSS连接配置
<augment_code_snippet path="yunzhishi-agent-service/src/shared/configuration.py" mode="EXCERPT">
````python
@property
def oss_access_key_id(self) -> str:
    """获取OSS访问密钥ID"""
    oss_access_key_id = os.getenv("OSS_ACCESS_KEY_ID")
    if not oss_access_key_id:
        raise ValueError("未设置OSS_ACCESS_KEY_ID环境变量")
    return oss_access_key_id

@property
def oss_access_key_secret(self) -> str:
    """获取OSS访问密钥"""
    oss_access_key_secret = os.getenv("OSS_ACCESS_KEY_SECRET")
    if not oss_access_key_secret:
        raise ValueError("未设置OSS_ACCESS_KEY_SECRET环境变量")
    return oss_access_key_secret
````
</augment_code_snippet>

#### 2. OSS使用示例
```python
from shared.utils import OSSStorageManager

# 初始化OSS管理器
oss_manager = OSSStorageManager(config)

# 上传文件
oss_url = oss_manager.upload_file(local_path, remote_path)

# 下载文件
local_path = oss_manager.download_file(remote_path, local_path)
```

## 项目依赖管理

### pyproject.toml 配置

<augment_code_snippet path="yunzhishi-agent-service/pyproject.toml" mode="EXCERPT">
````toml
[project]
name = "TeacherAssistantAgent"
version = "3.9.2"
requires-python = ">=3.9"
dependencies = [
    "langgraph>=0.0.15",
    "langchain>=0.0.350",
    "langchain-community>=0.0.10",
    "zhipuai>=1.0.7",
    "python-dotenv>=1.0.0",
    "PyMuPDF>=1.23.8",
    "typing-extensions>=4.8.0",
    "openai>=1.0.0",
    "boto3>=1.34.0",
    "md2xmind>=1.0.0",
    "oss2>=2.18.1",
    "langchain_openai>=0.0.1"
]
````
</augment_code_snippet>

### 核心依赖说明

#### 1. AI框架依赖
- **langgraph**: 状态机工作流框架
- **langchain**: AI应用开发框架
- **langchain-community**: LangChain社区组件

#### 2. AI模型依赖
- **zhipuai**: 智谱AI Python SDK
- **openai**: OpenAI兼容API客户端

#### 3. 文档处理依赖
- **PyMuPDF**: PDF文档处理库
- **md2xmind**: Markdown转思维导图

#### 4. 云服务依赖
- **oss2**: 阿里云OSS Python SDK
- **boto3**: AWS SDK（用于S3兼容存储）

#### 5. 工具依赖
- **python-dotenv**: 环境变量管理
- **typing-extensions**: 类型注解扩展

## 部署配置

### Docker配置

#### 1. Dockerfile
<augment_code_snippet path="yunzhishi-agent-service/Dockerfile" mode="EXCERPT">
````dockerfile
FROM registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service-base:latest

WORKDIR /app

COPY pyproject.toml *.json .
COPY src/ src/

# 安装依赖
RUN pip install --no-cache-dir -e ".[inmem]" \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

ENV PYTHONPATH=/app
ENV PATH=/usr/local/bin:$PATH

EXPOSE 2024

CMD ["langgraph", "dev", "--host", "0.0.0.0", "--port", "2024"]
````
</augment_code_snippet>

#### 2. 环境变量传递
```bash
docker run --name agent-service -p 2024:2024 \
  -e ZHIPUAI_API_KEY="your_key" \
  -e OSS_ACCESS_KEY_ID="your_id" \
  -e OSS_ACCESS_KEY_SECRET="your_secret" \
  agent-service:latest
```

### 生产环境配置

#### 1. 环境变量管理
```bash
# 使用环境变量文件
docker run --env-file .env.prod agent-service:latest

# 使用Kubernetes ConfigMap和Secret
kubectl create configmap app-config --from-env-file=.env
kubectl create secret generic app-secrets --from-env-file=.env.secrets
```

#### 2. 配置验证
```python
def validate_config():
    """验证配置的完整性和正确性"""
    required_env_vars = [
        "ZHIPUAI_API_KEY",
        "OSS_ACCESS_KEY_ID", 
        "OSS_ACCESS_KEY_SECRET"
    ]
    
    for var in required_env_vars:
        if not os.getenv(var):
            raise ValueError(f"缺少必需的环境变量: {var}")
```

## 配置最佳实践

### 1. 安全性
- 敏感信息使用环境变量
- 不在代码中硬编码密钥
- 使用.env文件管理本地开发环境

### 2. 可维护性
- 使用类型注解确保配置正确性
- 提供合理的默认值
- 配置验证和错误处理

### 3. 灵活性
- 支持多种模型提供商
- 运行时动态配置
- 环境特定的配置覆盖

### 4. 可观测性
- 配置加载日志
- 配置验证结果记录
- 运行时配置状态监控

这个配置系统为Transcript Graph提供了强大而灵活的配置管理能力，确保了系统在不同环境下的稳定运行和易于维护。

"""工具函数模块

该模块提供了文件IO操作和消息处理的工具函数。
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Union, TypeVar, cast, TextIO, Generator, Optional, List
from contextlib import contextmanager
from langchain_core.messages import HumanMessage, AIMessage
import oss2
import tempfile
import re
import fitz  # PyMuPDF

from shared.configuration import LessonPlanConfiguration

T = TypeVar('T')

class FileIO:
    """文件IO工具类"""
    
    # 类级别的静态变量，用于存储当前任务目录
    _current_task_dir = None
    
    def __init__(self, config: 'LessonPlanConfiguration'):
        self.config = config
        
    @classmethod
    def _get_task_dir(cls, config: 'LessonPlanConfiguration') -> str:
        """获取当前任务的目录"""
        if cls._current_task_dir is None:
            # 生成任务目录名
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # 检查是否是思维导图任务
            if hasattr(config, 'current_mindmap_title') and config.current_mindmap_title is not None:
                # 思维导图任务
                title = config.current_mindmap_title or "unknown"
                cls._current_task_dir = f"mindmap_{timestamp}_{title}"
            else:
                # 教案任务 - 保持原有逻辑
                course_name = config.current_course_name or "unknown"
                lesson_unitname = config.current_lesson_unitname or "unknown"
                cls._current_task_dir = f"lesson_{timestamp}_{course_name}_{lesson_unitname}"
                
            # 创建任务目录
            task_path = os.path.join(config.output_dir, cls._current_task_dir)
            os.makedirs(task_path, exist_ok=True)
        return cls._current_task_dir
        
    @classmethod
    def reset_task_dir(cls):
        """重置任务目录，用于开始新任务时调用"""
        cls._current_task_dir = None
        
    def write_file(self, filename: str, content: str, subdir: Optional[str] = None) -> None:
        """写入文件
        
        Args:
            filename: 文件名
            content: 文件内容
            subdir: 子目录（可选）
        """
        # 获取任务目录
        task_dir = self._get_task_dir(self.config)
        
        # 构建完整路径
        if subdir:
            # 如果指定了子目录，路径为: output_dir/task_dir/subdir/filename
            dir_path = os.path.join(self.config.output_dir, task_dir, subdir)
            os.makedirs(dir_path, exist_ok=True)
            file_path = os.path.join(dir_path, filename)
        else:
            # 否则路径为: output_dir/task_dir/filename
            file_path = os.path.join(self.config.output_dir, task_dir, filename)
            
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
    def read_file(self, filename: str, subdir: Optional[str] = None, default: str = "") -> str:
        """读取文件内容
        
        Args:
            filename: 文件名（包含扩展名）
            subdir: 可选的子目录名，用于组织文件结构
            default: 文件不存在时返回的默认值
            
        Returns:
            str: 文件内容或默认值
            
        Raises:
            ValueError: 当文件名无效时
        """
        if not filename:
            raise ValueError("文件名不能为空")
            
        # 构建文件路径
        filepath = self.config.output_dir
        if subdir:
            filepath = filepath / subdir
        filepath = filepath / filename
        
        # 读取文件
        if not filepath.exists():
            return default
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()

def get_message_content(message: Union[Dict[str, Any], HumanMessage, AIMessage, str]) -> Dict[str, Any]:
    """从不同类型的消息对象中提取内容信息
    
    Args:
        message: 消息对象，支持以下类型：
            - Dict[str, Any]: 字典类型消息
            - HumanMessage: 人类消息对象
            - AIMessage: 智能体消息对象
            - str: 字符串类型消息
    
    Returns:
        Dict[str, Any]: 提取的消息内容字典
        
    Raises:
        json.JSONDecodeError: JSON解析失败时抛出
    """
    # 提取原始内容
    if isinstance(message, (HumanMessage, AIMessage)):
        content = message.content
    elif isinstance(message, dict):
        content = message.get("content", "")
    else:
        content = str(message)

    # 处理内容格式
    if isinstance(content, str):
        try:
            # 尝试解析JSON格式
            parsed_content = json.loads(content)
            if isinstance(parsed_content, dict):
                return cast(Dict[str, Any], parsed_content)
            elif isinstance(parsed_content, list) and parsed_content:
                # 处理列表类型，获取第一个元素
                first_item = parsed_content[0]
                return cast(Dict[str, Any], first_item if isinstance(first_item, dict) else {"content": str(first_item)})
        except json.JSONDecodeError:
            # JSON解析失败时返回原始内容
            return {"content": content}
    elif isinstance(content, dict):
        return cast(Dict[str, Any], content)
    elif isinstance(content, list) and content:
        # 处理列表类型，获取第一个元素
        first_item = content[0]
        return cast(Dict[str, Any], first_item if isinstance(first_item, dict) else {"content": str(first_item)})
    
    # 默认返回包装后的内容
    return {"content": str(content)}

class OSSStorageManager:
    """阿里云OSS存储管理器，负责云存储的操作"""
    
    def __init__(self, config: LessonPlanConfiguration):
        """初始化OSS存储管理器
        
        Args:
            config: 应用配置对象
        """
        self.config = config
        
    @contextmanager
    def get_client(self) -> Generator[oss2.Bucket, None, None]:
        """获取OSS客户端的上下文管理器
        
        Yields:
            oss2.Bucket: OSS Bucket实例
            
        Raises:
            ValueError: 当客户端创建失败时
        """
        try:
            # 创建Auth对象
            auth = oss2.Auth(
                self.config.oss_access_key_id,
                self.config.oss_access_key_secret
            )
            # 创建Bucket对象
            bucket = oss2.Bucket(
                auth,
                self.config.oss_endpoint,
                self.config.oss_bucket
            )
            yield bucket
        except Exception as e:
            raise ValueError(f"OSS客户端创建失败: {str(e)}") from e
            
    @contextmanager
    def get_temp_file(self, object_name: str, suffix: str = '.pdf') -> Generator[str, None, None]:
        """从OSS下载文件到临时目录
        
        Args:
            object_name: OSS存储桶中的对象名称
            suffix: 临时文件后缀
            
        Yields:
            str: 临时文件路径
            
        Raises:
            ValueError: 当文件不存在或下载失败时
        """
        with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as temp_file:
            temp_path = temp_file.name
            try:
                with self.get_client() as bucket:
                    # 检查文件是否存在
                    if not bucket.object_exists(object_name):
                        raise ValueError(f"文件 {object_name} 在存储桶中不存在")
                    # 下载文件
                    bucket.get_object_to_file(object_name, temp_path)
                yield temp_path
            finally:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
    def upload_file(self, local_path: str, object_name: str) -> None:
        """上传文件到OSS存储
        
        Args:
            local_path: 本地文件路径
            object_name: OSS存储桶中的目标对象名称
            
        Raises:
            ValueError: 当上传失败时
        """
        try:
            with self.get_client() as bucket:
                bucket.put_object_from_file(object_name, local_path)
        except Exception as e:
            raise ValueError(f"文件上传失败: {str(e)}") from e
            
    def delete_file(self, object_name: str) -> None:
        """从OSS存储中删除文件
        
        Args:
            object_name: 要删除的对象名称
            
        Raises:
            ValueError: 当删除失败时
        """
        try:
            with self.get_client() as bucket:
                bucket.delete_object(object_name)
        except Exception as e:
            raise ValueError(f"文件删除失败: {str(e)}") from e
            
    def check_file_exists(self, object_name: str) -> bool:
        """检查文件是否存在于OSS存储中
        
        Args:
            object_name: 要检查的对象名称
            
        Returns:
            bool: 文件是否存在
        """
        try:
            with self.get_client() as bucket:
                return bucket.object_exists(object_name)
        except Exception:
            return False

def sanitize_filename(filename: str) -> str:
    """过滤文件名中的特殊字符"""
    # 移除或替换不能用于文件名的英文字符
    filename = re.sub(r'[\s\\/:"*<>|?]+', '', filename)
    # 确保结果不为空
    if not filename:
        filename = "unnamed"
    # print(f"Original filename: {filename} -> Sanitized filename: {filename}")
    return filename

@contextmanager
def manage_pdf_conversion(temp_dir: Path) -> Generator[List[str], None, None]:
    """管理PDF转换过程的上下文，自动清理临时图片文件
    
    Args:
        temp_dir: 临时目录路径
        
    Yields:
        List[str]: 用于存储图片base64字符串的列表
    """
    img_bases = []
    try:
        yield img_bases
    finally:
        # 清理临时文件
        for img_file in temp_dir.glob("page_*.png"):
            img_file.unlink(missing_ok=True)

@contextmanager
def manage_pdf_document(pdf_path: str) -> Generator[Any, None, None]:
    """管理PDF文档的上下文，确保文档正确关闭
    
    Args:
        pdf_path: PDF文件路径
        
    Yields:
        fitz.Document: 打开的PDF文档对象
    """
    doc = fitz.open(pdf_path)
    try:
        yield doc
    finally:
        doc.close()
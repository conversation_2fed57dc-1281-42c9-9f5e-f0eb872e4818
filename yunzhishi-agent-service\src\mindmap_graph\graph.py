import os
from typing import Dict, Any, Optional, List, Generator, ContextManager, Callable, Literal, cast
from typing_extensions import TypedDict
from contextlib import contextmanager

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, END, START
from langgraph.constants import Send
from langgraph.types import Command, interrupt
from langchain_core.runnables import RunnableConfig

from mindmap_graph.state import (MindMapState, MindMapInput, MindMapOutput,
    MindMapInfo, Router, create_initial_state)
from shared.utils import get_message_content, FileIO, OSSStorageManager, sanitize_filename
from shared.configuration import LessonPlanConfiguration 
from mindmap_graph.processors import (FileProcessAgent, MindmapTextGenerateAgent, MindmapVisionGenerateAgent, XMindConverterAgent)

# 加载环境变量配置
load_dotenv()

class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]   # 文本模型提供商
    vision_model_provider: Optional[str]   # 视觉模型提供商
    text_model: Optional[str]   # 文本模型名称
    vision_model: Optional[str]   # 视觉模型名称
    temp_dir: Optional[str]   # 临时文件目录
    output_dir: Optional[str]   # 输出目录
    oss_endpoint: Optional[str]   # 阿里云 OSS endpoint
    oss_bucket: Optional[str]   # 阿里云 OSS bucket name

# 全局智能体实例
file_process_agent = None
mindmap_text_generate_agent = None
mindmap_vision_generate_agent = None
xmind_converter_agent = None
mindmap_config = None  # 全局配置实例

def create_graph(config: Optional[RunnableConfig] = None) -> StateGraph:
    """创建工作流程图"""
    # 初始化全局代理
    global mindmap_config, file_process_agent, mindmap_text_generate_agent, mindmap_vision_generate_agent, xmind_converter_agent
    
    if config is not None:
        mindmap_config = LessonPlanConfiguration.from_runnable_config(config)
    else:
        mindmap_config = LessonPlanConfiguration()
    
    file_process_agent = FileProcessAgent(mindmap_config)
    mindmap_text_generate_agent = MindmapTextGenerateAgent(mindmap_config)
    mindmap_vision_generate_agent = MindmapVisionGenerateAgent(mindmap_config)
    xmind_converter_agent = XMindConverterAgent(mindmap_config)
        
    # 创建工作流程图，传入配置模式
    workflow = StateGraph(MindMapState, ConfigSchema, input=MindMapInput, output=MindMapOutput)
    
    # 添加工作节点
    workflow.add_node("preprocess_info", preprocess_info)
    workflow.add_node("STR_00_generate_text_mindmap", generate_text_mindmap)
    workflow.add_node("STR_00_generate_vision_mindmap", generate_vision_mindmap)
    workflow.add_node("convert_to_xmind", convert_to_xmind)
    
    # 设置工作流入口点
    workflow.add_edge(START, "preprocess_info")
    
    # 设置节点之间的边
    workflow.add_edge("STR_00_generate_text_mindmap", "convert_to_xmind")
    workflow.add_edge("STR_00_generate_vision_mindmap", "convert_to_xmind")
    workflow.add_edge("convert_to_xmind", END)
    
    return workflow.compile()

class GraphConfig(TypedDict):
    """工作流配置类型定义"""
    model_name: Literal["zhipuai"]  # 使用的模型名称
    mindmap_topic: str  # 思维导图主题
    mindmap_description: Optional[str]  # 详细描述
    mindmap_requirements: Optional[str]  # 生成要求
    pdf_path: Optional[str]  # PDF文件路径
    pdf_start_page: Optional[int]  # PDF起始页码
    pdf_end_page: Optional[int]  # PDF结束页码

@contextmanager
def manage_workflow_state() -> Generator[LessonPlanConfiguration, None, None]:
    """管理工作流状态的上下文管理器"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 创建默认配置实例
        config = LessonPlanConfiguration()
        
        yield config
        
    except Exception as e:
        raise
    finally:
        pass

@contextmanager
def manage_state_update(stage: str, initial_status: str) -> Generator[Dict[str, Any], None, None]:
    """管理状态更新的上下文"""
    state_update = {}
    try:
        yield state_update
        # 只有在没有设置router的情况下才设置默认状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 只有STR_开头的节点才添加ERR前缀
        if stage.startswith("STR_"):
            # 从STR_XX_中提取XX作为错误编号
            error_prefix = f"ERR_{stage[4:6]}"
            error_status = f"{error_prefix}_{initial_status}失败"
        else:
            error_status = f"{initial_status}失败"
            
        state_update["router"] = Router(
            stage=stage,
            status=error_status,
            error=str(e)
        )
        raise

def preprocess_info(state: MindMapState) -> Command[Literal["STR_00_generate_text_mindmap", "STR_00_generate_vision_mindmap"]]:
    """预处理输入信息节点，解析用户输入并处理PDF文件
    
    Args:
        state: 当前工作流状态
        
    Returns:
        Command: 包含状态更新和下一个节点的命令
    """
    with manage_state_update("preprocess_info", "预处理信息") as state_update:
        # 获取最新消息
        latest_message = state["messages"][-1]
        # 直接使用get_message_content提取参数
        params = get_message_content(latest_message)
        
        # 获取用户ID和会话ID
        user_id = params.get("user_id")
        session_id = params.get("session_id")
        
        # 更新思维导图信息
        mindmap_info = MindMapInfo(
            mindmap_topic=params.get("mindmap_topic", ""),
            mindmap_description=params.get("mindmap_description", None),
            mindmap_requirements=params.get("mindmap_requirements", None),
            pdf_path=params.get("pdf_path", None),
            pdf_start_page=params.get("pdf_start_page", None),
            pdf_end_page=params.get("pdf_end_page", None),
            pdf_type="text"  # 默认为文本类型
        )
        
        # 处理PDF文件（如果有）
        if mindmap_info.pdf_path:

            # 处理PDF文件
            pdf_result = file_process_agent.process_pdf(
                mindmap_info.pdf_path,
                mindmap_info.pdf_start_page,
                mindmap_info.pdf_end_page
            )
            
            # 更新思维导图信息
            mindmap_info.pdf_content = pdf_result.get("pdf_content", "")
            mindmap_info.pdf_type = pdf_result.get("pdf_type", "text")
            mindmap_info.pdf_path = pdf_result.get("pdf_path", mindmap_info.pdf_path)
            
            # 如果是视觉类型的PDF且有临时路径，更新临时路径
            if mindmap_info.pdf_type == "vision" and "pdf_temp_path" in pdf_result:
                mindmap_info.pdf_temp_path = pdf_result.get("pdf_temp_path")
                print(f"已更新PDF临时文件路径: {mindmap_info.pdf_temp_path}")
        
        # 创建状态更新
        state_update = {
            "mindmap_info": mindmap_info,
            "user_id": user_id,
            "session_id": session_id,
            "router": Router(
                stage="preprocess_info",
                status="预处理信息成功"
            )
        }
        
        # 根据PDF类型确定下一个节点
        if mindmap_info.pdf_type == "vision":
            goto = "STR_00_generate_vision_mindmap"
        else:
            goto = "STR_00_generate_text_mindmap"
            
        # 返回命令，包含状态更新和下一个节点
        return Command(
            update=state_update,
            goto=goto
        )

def generate_text_mindmap(state: MindMapState) -> Dict[str, Any]:
    """生成思维导图文本节点"""
    with manage_state_update("STR_00_generate_text_mindmap", "生成思维导图文本") as state_update:
        # 检查必要数据
        if not state["mindmap_info"] or not state["mindmap_info"].mindmap_topic:
            raise ValueError("未提供有效的思维导图信息")
        
        # 生成思维导图文本
        mindmap_markdown = mindmap_text_generate_agent.generate_mindmap(
            state["mindmap_info"],
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 更新状态
        state_update["mindmap_markdown"] = mindmap_markdown
        state_update["router"] = Router(
            stage="FIN_ALL_generate_text_mindmap",
            status="生成思维导图文本成功"
        )
        
        return state_update

def generate_vision_mindmap(state: MindMapState) -> Dict[str, Any]:
    """从PDF图像生成思维导图节点"""
    with manage_state_update("STR_00_generate_vision_mindmap", "从PDF图像生成思维导图") as state_update:
        # 检查必要数据
        if not state["mindmap_info"] or not state["mindmap_info"].pdf_path:
            raise ValueError("未提供有效的PDF文件")
        
        # 从PDF图像生成思维导图
        mindmap_markdown = mindmap_vision_generate_agent.generate_mindmap_from_pdf(
            state["mindmap_info"],
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 更新状态
        state_update["mindmap_markdown"] = mindmap_markdown
        state_update["router"] = Router(
            stage="FIN_ALL_generate_vision_mindmap",
            status="生成思维导图文本成功"
        )
        return state_update

def convert_to_xmind(state: MindMapState) -> Dict[str, Any]:
    """转换为XMind格式节点"""
    with manage_state_update("convert_to_xmind", "转换为XMind格式") as state_update:
        # 检查必要数据
        if not state["mindmap_markdown"]:
            raise ValueError("未生成思维导图文本")
        
        # 转换为XMind格式
        xmind_path = xmind_converter_agent.convert_to_xmind(state["mindmap_markdown"])
        
        # 更新状态（即使转换失败也更新，确保流程可以继续）
        state_update["mindmap_xmind"] = xmind_path if xmind_path else "转换失败"
        
        return state_update
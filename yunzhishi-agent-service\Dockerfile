FROM registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service-base:latest

WORKDIR /app

COPY pyproject.toml *.json .
COPY src/ src/

# 安装依赖（补充开发依赖）
RUN pip install --no-cache-dir -e ".[inmem]" \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com \
    && pip install --no-cache-dir --upgrade "langgraph-cli[inmem]"

ENV PYTHONPATH=/app
ENV PATH=/usr/local/bin:$PATH

EXPOSE 2024

# 启动命令
CMD ["langgraph", "dev", "--host", "0.0.0.0", "--port", "2024"]
# API 使用指南

## 概述

Transcript Graph 提供了基于 LangGraph 的 API 接口，支持通过 HTTP 请求调用教学逐字稿生成服务。本文档详细介绍 API 的使用方法、输入输出格式、调用示例和错误处理。

## API 架构

### 服务端点

- **基础URL**: `http://localhost:2024` (默认)
- **协议**: HTTP/HTTPS
- **格式**: JSON
- **框架**: LangGraph + LangChain

### 认证方式

目前系统使用环境变量进行认证，无需在 API 调用中传递认证信息。

## 输入格式

### TranscriptInput 结构

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/state.py" mode="EXCERPT">
````python
class TranscriptInput(TypedDict):
    """教学逐字稿输入状态"""
    messages: List[HumanMessage]
````
</augment_code_snippet>

### 输入参数详解

#### 1. 基础输入格式

```json
{
  "messages": [
    {
      "type": "human",
      "content": {
        "course_basic_info": "课程基本信息",
        "teaching_info": "教学内容概述",
        "personal_requirements": "个性化要求（可选）",
        "pdf_path": "PDF文件路径（可选）",
        "pdf_start_page": 1,
        "pdf_end_page": 5
      }
    }
  ]
}
```

#### 2. 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `course_basic_info` | string | 是 | 课程基本信息，包括学段、学科、年级和课题名称 |
| `teaching_info` | string | 否 | 教学内容概述，可直接传入教案内容 |
| `personal_requirements` | string | 否 | 个性化要求，可指定教学风格、方法或特殊关注点 |
| `pdf_path` | string | 否 | PDF教材文件路径，用于参考教材内容 |
| `pdf_start_page` | integer | 否 | PDF起始页码，从1开始计数 |
| `pdf_end_page` | integer | 否 | PDF结束页码，包含该页 |

## 输出格式

### TranscriptOutput 结构

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/state.py" mode="EXCERPT">
````python
class TranscriptOutput(TypedDict):
    """教学逐字稿输出状态"""
    final_transcript: str
    teaching_processes: Dict[str, TeachingProcessInfo]
````
</augment_code_snippet>

### 输出字段详解

#### 1. 响应格式

```json
{
  "final_transcript": "完整的教学逐字稿内容",
  "teaching_processes": {
    "process_01": {
      "process_id": "process_01",
      "process_title": "课程导入",
      "content": "环节内容描述",
      "transcript": "该环节的详细逐字稿"
    },
    "process_02": {
      "process_id": "process_02", 
      "process_title": "知识讲解",
      "content": "环节内容描述",
      "transcript": "该环节的详细逐字稿"
    }
  }
}
```

#### 2. 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `final_transcript` | string | 最终合并的完整教学逐字稿 |
| `teaching_processes` | object | 各个教学环节的详细信息字典 |
| `process_id` | string | 环节唯一标识符（如 process_01） |
| `process_title` | string | 环节标题（如"课程导入"） |
| `content` | string | 环节内容描述 |
| `transcript` | string | 该环节的详细逐字稿内容 |

## 调用示例

### 示例1：纯文本输入

#### 请求

```json
{
  "messages": [
    {
      "type": "human",
      "content": {
        "course_basic_info": "初中政治八年级上册《坚持国家利益至上》",
        "teaching_info": "一、课程导入：家国情怀的传承与思辨- 展示陆之方一家三代戍边案例，提问"为何两代人坚守边疆？"，引导学生思考个人选择与国家利益的关系。二、案例分析：国家利益的维护路径与思辨- 呈现网络泄密调研数据，开展"军迷论坛发言"情景模拟。三、法律解析与责任担当：思辨与行动- 解析《保密法》《国防法》核心条款。四、课程总结：知行合一的爱国实践与思辨- 构建"国家利益金字塔"思维导图。",
        "personal_requirements": ""
      }
    }
  ]
}
```

#### 响应

```json
{
  "final_transcript": "# 一、课程导入\n\n老师：同学们，今天我们来学习《坚持国家利益至上》这一课...\n\n# 二、案例分析\n\n老师：接下来我们通过具体案例来分析...",
  "teaching_processes": {
    "process_01": {
      "process_id": "process_01",
      "process_title": "课程导入",
      "content": "家国情怀的传承与思辨...",
      "transcript": "# 一、课程导入\n\n老师：同学们，今天我们来学习..."
    }
  }
}
```

### 示例2：PDF文件输入

#### 请求

```json
{
  "messages": [
    {
      "type": "human", 
      "content": {
        "course_basic_info": "初中政治八年级上册《坚持国家利益至上》",
        "teaching_info": "",
        "personal_requirements": "",
        "pdf_path": "user_input/教案0402txt.pdf",
        "pdf_start_page": 1,
        "pdf_end_page": 5
      }
    }
  ]
}
```

#### 响应

```json
{
  "final_transcript": "基于PDF内容生成的完整逐字稿...",
  "teaching_processes": {
    "process_01": {
      "process_id": "process_01",
      "process_title": "课程导入",
      "content": "基于PDF内容提取的环节描述...",
      "transcript": "基于PDF内容生成的逐字稿..."
    }
  }
}
```

## 配置参数

### 运行时配置

可以通过配置参数自定义模型和处理选项：

```json
{
  "configurable": {
    "txt_model_provider": "zhipuai",
    "vision_model_provider": "zhipuai", 
    "text_model": "zhipuai/glm-4-air-0111",
    "vision_model": "zhipuai/glm-4v-plus-0111",
    "temp_dir": "temp_files",
    "output_dir": "outputs"
  }
}
```

### 配置选项说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `txt_model_provider` | string | "zhipuai" | 文本模型提供商 |
| `vision_model_provider` | string | "zhipuai" | 视觉模型提供商 |
| `text_model` | string | "zhipuai/glm-4-air-0111" | 文本模型名称 |
| `vision_model` | string | "zhipuai/glm-4v-plus-0111" | 视觉模型名称 |
| `temp_dir` | string | "temp_files" | 临时文件目录 |
| `output_dir` | string | "outputs" | 输出文件目录 |

## 错误处理

### 常见错误类型

#### 1. 输入验证错误

```json
{
  "error": {
    "type": "ValidationError",
    "message": "缺少必需的字段: course_basic_info",
    "code": 400
  }
}
```

#### 2. 文件处理错误

```json
{
  "error": {
    "type": "FileProcessError", 
    "message": "无法找到指定的PDF文件: user_input/教案.pdf",
    "code": 404
  }
}
```

#### 3. 模型调用错误

```json
{
  "error": {
    "type": "ModelError",
    "message": "AI模型调用失败: API密钥无效",
    "code": 401
  }
}
```

#### 4. 系统错误

```json
{
  "error": {
    "type": "SystemError",
    "message": "内部服务器错误",
    "code": 500
  }
}
```

### 错误处理最佳实践

#### 1. 客户端重试策略

```python
import time
import requests

def call_transcript_api(data, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post(
                "http://localhost:2024/transcript",
                json=data,
                timeout=300
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code >= 500:
                # 服务器错误，可以重试
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
            else:
                # 客户端错误，不重试
                response.raise_for_status()
                
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue
            raise e
    
    raise Exception("API调用失败，已达到最大重试次数")
```

#### 2. 错误日志记录

```python
import logging

logger = logging.getLogger(__name__)

def handle_api_error(error_response):
    error_info = error_response.get("error", {})
    error_type = error_info.get("type", "Unknown")
    error_message = error_info.get("message", "未知错误")
    error_code = error_info.get("code", 0)
    
    logger.error(f"API错误 - 类型: {error_type}, 消息: {error_message}, 代码: {error_code}")
    
    # 根据错误类型进行不同处理
    if error_type == "ValidationError":
        # 输入验证错误，检查输入格式
        pass
    elif error_type == "FileProcessError":
        # 文件处理错误，检查文件路径和权限
        pass
    elif error_type == "ModelError":
        # 模型错误，检查API密钥和配置
        pass
```

## 性能优化

### 1. 请求优化

#### 批量处理

```python
# 避免频繁的单个请求
# 推荐：合并多个相关课程的处理
def batch_process_transcripts(course_list):
    results = []
    for course in course_list:
        result = call_transcript_api(course)
        results.append(result)
    return results
```

#### 异步处理

```python
import asyncio
import aiohttp

async def async_call_transcript_api(session, data):
    async with session.post(
        "http://localhost:2024/transcript",
        json=data
    ) as response:
        return await response.json()

async def process_multiple_transcripts(course_list):
    async with aiohttp.ClientSession() as session:
        tasks = [
            async_call_transcript_api(session, course)
            for course in course_list
        ]
        results = await asyncio.gather(*tasks)
        return results
```

### 2. 缓存策略

```python
import hashlib
import json
from functools import lru_cache

def generate_cache_key(input_data):
    """生成输入数据的缓存键"""
    content_str = json.dumps(input_data, sort_keys=True)
    return hashlib.md5(content_str.encode()).hexdigest()

@lru_cache(maxsize=100)
def cached_transcript_call(cache_key, input_data):
    """带缓存的API调用"""
    return call_transcript_api(input_data)
```

## 监控和调试

### 1. 请求追踪

```python
import uuid

def call_with_tracking(data):
    request_id = str(uuid.uuid4())
    
    # 添加追踪ID到请求头
    headers = {
        "X-Request-ID": request_id,
        "Content-Type": "application/json"
    }
    
    logger.info(f"开始处理请求 {request_id}")
    
    try:
        response = requests.post(
            "http://localhost:2024/transcript",
            json=data,
            headers=headers,
            timeout=300
        )
        
        logger.info(f"请求 {request_id} 处理完成")
        return response.json()
        
    except Exception as e:
        logger.error(f"请求 {request_id} 处理失败: {str(e)}")
        raise
```

### 2. 性能监控

```python
import time
from contextlib import contextmanager

@contextmanager
def performance_monitor(operation_name):
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"{operation_name} 耗时: {duration:.2f}秒")

# 使用示例
with performance_monitor("逐字稿生成"):
    result = call_transcript_api(data)
```

## 最佳实践

### 1. 输入数据准备

- **课程信息完整性**：确保 `course_basic_info` 包含完整的课程标识信息
- **教学内容结构化**：`teaching_info` 建议使用结构化的格式，便于AI理解
- **PDF文件优化**：确保PDF文件清晰可读，页面范围合理

### 2. 错误处理策略

- **优雅降级**：当PDF处理失败时，回退到纯文本模式
- **重试机制**：对临时性错误实施指数退避重试
- **用户反馈**：提供清晰的错误信息和解决建议

### 3. 性能优化

- **合理的超时设置**：根据内容复杂度设置适当的超时时间
- **资源管理**：及时清理临时文件和缓存
- **并发控制**：避免过多并发请求导致系统过载

这个API使用指南为开发者提供了完整的接口使用说明，确保能够正确、高效地使用Transcript Graph服务。

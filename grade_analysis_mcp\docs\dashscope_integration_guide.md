# DashScope 集成指南

## 1. 概述

本文档详细说明如何在 `grade_analysis_mcp` 项目中集成阿里云 DashScope 的 DeepSeek-V3 模型，参考 `transcript_generator_mcp` 项目的成功实现。

## 2. 环境配置

### 2.1 环境变量配置

参考 `transcript_generator_mcp/.env` 文件，创建以下环境变量：

```bash
# 课堂教学分析 MCP 服务配置

# AI 模型配置 - 使用阿里云DashScope的DeepSeek-V3模型
# 获取API Key: https://help.aliyun.com/zh/model-studio/developer-reference/get-api-key
# 支持的模型: deepseek-v3, deepseek-r1, deepseek-r1-0528
AI_PROVIDER=dashscope
AI_API_KEY="your-dashscope-api-key-here"
AI_MODEL_NAME=deepseek-v3

# 分析参数配置
AI_TIMEOUT=120
AI_MAX_RETRIES=3
MAX_TOKENS=8000
TEMPERATURE=0.7

# 输出配置
OUTPUT_DIR=outputs

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_CONSOLE=true
```

### 2.2 .env.example 文件

```bash
# 课堂教学分析 MCP 服务配置示例

# AI 模型配置 - 使用阿里云DashScope的DeepSeek-V3模型
AI_PROVIDER=dashscope
AI_API_KEY="sk-your-dashscope-api-key-here"
AI_MODEL_NAME=deepseek-v3

# 分析参数配置
AI_TIMEOUT=120
AI_MAX_RETRIES=3
MAX_TOKENS=8000
TEMPERATURE=0.7

# 输出配置
OUTPUT_DIR=outputs

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_CONSOLE=true
```

## 3. 配置类实现

### 3.1 GradeAnalysisConfig 类

参考 `transcript_generator_mcp/src/config.py`，实现配置管理：

```python
"""课堂教学分析配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()

class GradeAnalysisConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- AI服务配置 ---
    ai_api_key: str = Field(description="AI服务API密钥")
    ai_provider: str = Field(
        default="dashscope",
        description="AI服务提供商（固定为dashscope）"
    )
    ai_model_name: str = Field(
        default="deepseek-v3",
        description="AI模型名称"
    )
    ai_timeout: int = Field(
        default=120,
        description="AI服务请求超时时间（秒）"
    )
    ai_max_retries: int = Field(
        default=3,
        description="AI服务请求最大重试次数"
    )
    
    # --- 分析参数配置 ---
    max_tokens: int = Field(default=8000, description="最大token数量")
    temperature: float = Field(default=0.7, description="生成温度")
    
    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的分析报告文件存放的根目录")
    
    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @classmethod
    def from_env(cls) -> "GradeAnalysisConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如AI_API_KEY）。

        Returns:
            GradeAnalysisConfig: 一个包含所有最终配置值的实例。
        """
        ai_api_key = os.getenv("AI_API_KEY")
        if not ai_api_key:
            raise ValueError("请设置环境变量 AI_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上一级）
        current_dir = os.path.dirname(os.path.abspath(__file__))  # src目录
        project_root = os.path.dirname(current_dir)  # 项目根目录
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            ai_api_key=ai_api_key,
            ai_provider=os.getenv("AI_PROVIDER", "dashscope"),
            ai_model_name=os.getenv("AI_MODEL_NAME", "deepseek-v3"),
            ai_timeout=int(os.getenv("AI_TIMEOUT", "120")),
            ai_max_retries=int(os.getenv("AI_MAX_RETRIES", "3")),
            max_tokens=int(os.getenv("MAX_TOKENS", "8000")),
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        )
```

## 4. AI 客户端实现

### 4.1 复用 DashScope 客户端

直接复用 `transcript_generator_mcp/src/ai_client.py` 中的 `DashScopeClient` 类：

- 支持 DeepSeek-V3 模型
- 包含完整的错误处理和重试机制
- 实现了用量统计和成本计算
- 支持流式和非流式调用

### 4.2 模型用量信息

复用 `ModelUsageInfo` 和 `AIClientConfig` 数据模型，确保用量报告的一致性。

## 5. 成本估算

### 5.1 DeepSeek-V3 定价

根据 DashScope 官方定价：
- 输入：0.002元/千token
- 输出：0.008元/千token

### 5.2 预估成本

对于典型的课堂分析任务：
- 输入文本：约 5000-10000 字符 ≈ 5000-10000 tokens
- 输出报告：约 3000-5000 字符 ≈ 3000-5000 tokens
- 单次分析成本：约 0.03-0.06 元

## 6. 集成步骤

### 6.1 复制核心文件

从 `transcript_generator_mcp` 复制以下文件并适配：
1. `src/ai_client.py` - AI 客户端实现
2. 相关的数据模型（ModelUsageInfo, AIClientConfig）

### 6.2 适配修改

1. 更新模块导入路径
2. 调整日志记录器名称
3. 根据分析任务调整参数（如 max_tokens）

### 6.3 测试验证

1. 验证 API 连接
2. 测试模型调用
3. 验证用量统计
4. 测试错误处理

## 7. 注意事项

### 7.1 API 限制

- 注意 DashScope 的 QPS 限制
- 合理设置超时时间
- 实现适当的重试策略

### 7.2 成本控制

- 监控 token 使用量
- 设置合理的 max_tokens 限制
- 记录详细的用量日志

### 7.3 错误处理

- 网络连接错误
- API 限流错误
- 模型服务不可用
- Token 超限错误

## 8. 优势

使用 DashScope DeepSeek-V3 模型的优势：
1. **成本效益**：相比其他大模型，价格更优惠
2. **长文本支持**：适合处理完整的课堂转录文本
3. **中文优化**：对中文教学内容理解更准确
4. **稳定性**：阿里云提供的企业级服务保障
5. **一致性**：与现有项目保持技术栈统一

"""课程计划生成图的状态管理。

本模块定义了课程计划生成图中使用的状态结构。
包括输入状态、处理状态、输出状态和路由分类模式的定义。
"""
from typing import Dict, List, Optional, Any, TypedDict, Annotated
import operator
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

class TeachingProcessInfo(BaseModel):
    """教学流程信息"""
    process_id: str = Field(description="流程ID")
    process_title: str = Field(description="流程标题")
    content: Optional[str] = Field(default=None, description="流程内容")
    
    def __or__(self, other: 'TeachingProcessInfo') -> 'TeachingProcessInfo':
        """实现教学流程信息的合并操作
        
        合并规则：
        1. 保持原有的process_id和process_title
        2. 如果有新的content则更新content
        """
        return TeachingProcessInfo(
            process_id=self.process_id,
            process_title=self.process_title,
            content=other.content if other.content is not None else self.content
        )

class CourseInfo(BaseModel):
    """课程信息类型定义"""
    lesson_stage: str = Field(description="学段")
    lesson_coursename: str = Field(description="课程名称")
    lesson_unitname: str = Field(description="章节课时名称")
    lesson_version: Optional[str] = Field(None, description="教材版本")
    lesson_grade: str = Field(description="课程年级")
    lesson_semester: str = Field(description="课程学期")
    lesson_count: int = Field(description="课时数")
    lesson_pdf_already: int = Field(default=3, description="PDF情况，1系统已有，2用户自传，3无教材")
    lesson_pdf_type: str = Field(default="text", description="PDF处理类型，text或vision")
    lesson_pdf_content: Optional[str] = Field(None, description="PDF内容")
    lesson_pdf_path: Optional[str] = Field(None, description="PDF文件路径")
    lesson_pdf_start_page: Optional[int] = Field(None, description="PDF起始页码")
    lesson_pdf_end_page: Optional[int] = Field(None, description="PDF结束页码")
    lesson_pdf_temp_path: Optional[str] = Field(None, description="PDF临时文件路径（用于缓存）")
    lesson_methodology: Optional[str] = Field(None, description="教学法，auto表示自动匹配，空或None表示不使用特定教学法")
    user_personal: Optional[str] = Field(default=None, description="用户个人信息")
    
    def __or__(self, other: 'CourseInfo') -> 'CourseInfo':
        """实现课程信息的合并操作
        
        合并规则：保留最新的非空值
        """
        return CourseInfo(
            lesson_stage=other.lesson_stage or self.lesson_stage,
            lesson_coursename=other.lesson_coursename or self.lesson_coursename,
            lesson_unitname=other.lesson_unitname or self.lesson_unitname,
            lesson_version=other.lesson_version or self.lesson_version,
            lesson_grade=other.lesson_grade or self.lesson_grade,
            lesson_semester=other.lesson_semester or self.lesson_semester,
            lesson_count=other.lesson_count or self.lesson_count,
            lesson_pdf_already=other.lesson_pdf_already or self.lesson_pdf_already,
            lesson_pdf_type=other.lesson_pdf_type or self.lesson_pdf_type,
            lesson_pdf_content=other.lesson_pdf_content or self.lesson_pdf_content,
            lesson_pdf_path=other.lesson_pdf_path or self.lesson_pdf_path,
            lesson_pdf_start_page=other.lesson_pdf_start_page or self.lesson_pdf_start_page,
            lesson_pdf_end_page=other.lesson_pdf_end_page or self.lesson_pdf_end_page,
            lesson_pdf_temp_path=other.lesson_pdf_temp_path or self.lesson_pdf_temp_path,
            lesson_methodology=other.lesson_methodology or self.lesson_methodology,
            user_personal=other.user_personal or self.user_personal
        )

class Router(BaseModel):
    """路由状态模型"""
    stage: str = Field(description="当前阶段")
    status: str = Field(description="状态描述")
    error: Optional[str] = Field(None, description="错误信息")
    
    def __or__(self, other: 'Router') -> 'Router':
        """实现路由状态的合并操作
        
        合并规则：
        1. 如果任一状态有错误，保留错误状态
        2. 否则保留最新的状态
        """
        if self.error:
            return self
        if other.error:
            return other
        return other

class TeachingProcessOutline(BaseModel):
    """教学流程大纲模型"""
    content: str = Field(description="大纲内容")
    def __or__(self, other: 'TeachingProcessOutline') -> 'TeachingProcessOutline':
        """实现大纲内容的合并操作"""
        return other if other.content else self

class TeachingProcessOutlinePreprocess(BaseModel):
    """预处理的教学流程大纲模型"""
    content: str = Field(description="预处理大纲内容")
    def __or__(self, other: 'TeachingProcessOutlinePreprocess') -> 'TeachingProcessOutlinePreprocess':
        """实现预处理大纲内容的合并操作"""
        return other if other.content else self

class LessonPlanInput(TypedDict):
    """课程计划输入状态"""
    messages: List[HumanMessage]

class LessonPlanOutput(TypedDict):
    """课程计划输出状态"""
    final_plan: str
    teaching_processes: Dict[str, TeachingProcessInfo]
    teaching_objectives: str
    teaching_keypoints: str
    teaching_studentanalysis: str
    teaching_methods: str
    teaching_activities: str
    teaching_boarddesign: str
    custom_elements: Dict[str, str]
    mindmap_content: Optional[str]
    transcript_content: Optional[str]
    plananalysis_content: Optional[str]

class LessonPlanState(TypedDict):
    """课程计划状态"""
    messages: List[HumanMessage]
    user_id: Optional[str]
    session_id: Optional[str]
    router: Annotated[Router, operator.or_]
    course_info: Annotated[Optional[CourseInfo], operator.or_]
    teaching_process_outline: Annotated[TeachingProcessOutline, operator.or_]
    teaching_process_outline_preprocess: Annotated[Optional[TeachingProcessOutlinePreprocess], operator.or_]
    teaching_processes: Annotated[Dict[str, TeachingProcessInfo], operator.or_]
    teaching_objectives: Optional[str]
    teaching_keypoints: Optional[str]
    teaching_studentanalysis: Optional[str]
    teaching_methods: Optional[str]
    teaching_activities: Optional[str]
    teaching_boarddesign: Optional[str]
    custom_elements: Annotated[Dict[str, str], operator.or_]
    wants_add_element: bool
    wants_modify_element: bool
    wants_generate_mindmap: bool
    wants_generate_transcript: bool
    wants_generate_plananalysis: bool
    final_plan: Optional[str]
    user_habits: str
    mindmap_content: Optional[str]
    transcript_content: Optional[str]
    plananalysis_content: Optional[str]

def create_initial_state() -> LessonPlanState:
    """创建初始状态
    
    Returns:
        LessonPlanState: 包含所有必要字段的初始状态字典
    """
    return {
        "messages": [],
        "user_id": None,
        "session_id": None,
        "router": Router(
            stage="preprocess_info",
            status="开始预处理信息"
        ),
        "course_info": CourseInfo(
            lesson_stage="",
            lesson_coursename="",
            lesson_unitname="",
            lesson_version=None,
            lesson_grade="",
            lesson_semester="",
            lesson_count=0,
            lesson_pdf_already=3,
            lesson_pdf_path="",
            lesson_pdf_type="text",
            lesson_pdf_content=None,
            lesson_methodology=None,
            user_personal=None
        ),
        "teaching_process_outline": TeachingProcessOutline(content=""),
        "teaching_process_outline_preprocess": TeachingProcessOutlinePreprocess(content=""),
        "teaching_processes": {},
        "teaching_objectives": None,
        "teaching_keypoints": None,
        "teaching_studentanalysis": None,
        "teaching_methods": None,
        "teaching_activities": None,
        "teaching_boarddesign": None,
        "custom_elements": {},
        "wants_add_element": False,
        "wants_modify_element": False,
        "wants_generate_mindmap": False,
        "wants_generate_transcript": False,
        "wants_generate_plananalysis": False,
        "final_plan": None,
        "user_habits": "",
        "mindmap_content": None,
        "transcript_content": None,
        "plananalysis_content": None
    }
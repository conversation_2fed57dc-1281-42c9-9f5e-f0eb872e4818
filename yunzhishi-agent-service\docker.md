[镜像打包]

1. 基础镜像打包

```bash
docker build -f Dockerfile.base -t agent-service-base .
```

2. 业务镜像打包

```bash
docker build -t agent-service .
```

[镜像运行]

```bash
docker run --name agent-service -p 2024:2024 -e ZHIPUAI_API_KEY="c846c078f9de313b9c719ee45616acbc.3bR6DRbrKBogRleJ" -e SILICONFLOW_API_KEY="sk-cwndcilrhkoqjzkajmngunhysxezogotvxrfzuajrlsvxybo" -e OSS_ACCESS_KEY_ID="LTAI5tSQSyCfA5FnpDeEo8pF" -e OSS_ACCESS_KEY_SECRET="******************************" -e LANGSMITH_API_KEY="***************************************************" -e LANGCHAIN_HOST="0.0.0.0" -e LANGCHAIN_BACKEND_HOST="0.0.0.0" -e LANGCHAIN_PORT="2024" -e LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true" -e LANGCHAIN_SERVE_CORS_HEADERS="*" -e LANGCHAIN_SERVE_CORS_ORIGINS="*" agent-service:latest
```

[镜像上传]
1. 上传基础镜像
```bash
#登录到阿里云镜像仓库
docker login --username=hz_yunshi@1729905983737116 registry.cn-shanghai.aliyuncs.com
密码：h&oMlffUaHM#m$DGBkR
# 打标签
docker tag agent-service-base registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service-base:latest

# 上传镜像
docker push registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service-base:latest
```

2. 上传业务镜像

```bash
#登录到阿里云镜像仓库
docker login --username=hz_yunshi@1729905983737116 registry.cn-shanghai.aliyuncs.com
密码：h&oMlffUaHM#m$DGBkR

# 打标签
docker tag agent-service registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service:2025030401

# 上传镜像
docker push registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service:2025030401
```
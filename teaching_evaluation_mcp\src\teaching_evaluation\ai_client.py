"""AI客户端模块

基于 transcript_generator_mcp 的 DashScope 客户端实现，
适配教学分析的特定需求。
"""

import time
from typing import Tu<PERSON>, AsyncGenerator, Optional
from openai import AsyncOpenAI

from .config import TeachingEvaluationConfig
from .models import ModelUsageInfo
from .logger import get_logger


class DashScopeClient:
    """阿里云DashScope客户端（支持DeepSeek模型）
    
    基于 transcript_generator_mcp 的实现，适配教学分析需求：
    - 支持 DeepSeek-V3 和 DeepSeek-R1 模型
    - 完整的错误处理和重试机制
    - 用量统计和成本计算
    - 支持流式和非流式调用
    """

    def __init__(self, config: TeachingEvaluationConfig):
        """初始化DashScope客户端"""
        self.config = config
        self.logger = get_logger(__name__)
        self._setup_client()

    def _setup_client(self) -> None:
        """设置DashScope客户端"""
        self.client = AsyncOpenAI(
            api_key=self.config.ai_api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        self.logger.info(f"DashScope客户端初始化完成 - 模型: {self.config.ai_model_name}")

    async def generate_text(self, prompt: str, max_tokens: Optional[int] = None, 
                          temperature: Optional[float] = None) -> Tuple[str, ModelUsageInfo]:
        """生成文本并返回用量信息
        
        Args:
            prompt: 输入提示词
            max_tokens: 最大token数，默认使用配置值
            temperature: 生成温度，默认使用配置值
            
        Returns:
            Tuple[str, ModelUsageInfo]: 生成的文本和用量信息
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        try:
            start_time = time.time()
            
            # 使用传入参数或配置默认值
            actual_max_tokens = max_tokens or self.config.max_tokens
            actual_temperature = temperature if temperature is not None else self.config.temperature

            response = await self.client.chat.completions.create(
                model=self.config.ai_model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=actual_max_tokens,
                temperature=actual_temperature,
                timeout=self.config.ai_timeout
            )

            response_time = time.time() - start_time
            content = response.choices[0].message.content

            # 获取token使用量 - 优先使用API返回的实际数据
            usage = response.usage
            if usage and hasattr(usage, 'prompt_tokens') and hasattr(usage, 'completion_tokens'):
                # 使用API返回的实际token数据
                input_tokens = usage.prompt_tokens
                output_tokens = usage.completion_tokens
                total_tokens = usage.total_tokens if hasattr(usage, 'total_tokens') else (input_tokens + output_tokens)
                token_source = "API实际数据"
                self.logger.info(f"使用DashScope API返回的实际token数据 - 输入: {input_tokens}, 输出: {output_tokens}, 总计: {total_tokens}")
            else:
                # 后备方案：使用估算方法
                input_tokens = self._estimate_tokens(prompt)
                output_tokens = self._estimate_tokens(content)
                total_tokens = input_tokens + output_tokens
                token_source = "估算数据"
                self.logger.warning(f"API未返回token使用量信息，使用估算方法 - 输入: {input_tokens}, 输出: {output_tokens}, 总计: {total_tokens}")

            usage_info = ModelUsageInfo(
                vendor="dashscope",
                model_name=self.config.ai_model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                response_time=response_time
            )

            self.logger.info(f"文本生成完成 ({token_source}) - 输入: {input_tokens} tokens, 输出: {output_tokens} tokens, 耗时: {response_time:.2f}s")
            return content, usage_info

        except Exception as e:
            self.logger.error(f"DashScope调用失败: {str(e)}")
            raise

    async def stream_generate_text(self, prompt: str, max_tokens: Optional[int] = None,
                                 temperature: Optional[float] = None) -> AsyncGenerator[Tuple[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本
        
        Args:
            prompt: 输入提示词
            max_tokens: 最大token数，默认使用配置值
            temperature: 生成温度，默认使用配置值
            
        Yields:
            Tuple[str, Optional[ModelUsageInfo]]: 文本片段和用量信息（最后一次返回）
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        try:
            start_time = time.time()
            
            # 使用传入参数或配置默认值
            actual_max_tokens = max_tokens or self.config.max_tokens
            actual_temperature = temperature if temperature is not None else self.config.temperature

            stream = await self.client.chat.completions.create(
                model=self.config.ai_model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=actual_max_tokens,
                temperature=actual_temperature,
                stream=True,
                stream_options={"include_usage": True},
                timeout=self.config.ai_timeout
            )

            content_chunks = []
            usage_info = None

            async for chunk in stream:
                if not chunk.choices:
                    # 最后一个chunk包含usage信息
                    if chunk.usage:
                        response_time = time.time() - start_time
                        usage = chunk.usage

                        # 验证API返回的usage数据
                        if hasattr(usage, 'prompt_tokens') and hasattr(usage, 'completion_tokens'):
                            input_tokens = usage.prompt_tokens
                            output_tokens = usage.completion_tokens
                            total_tokens = usage.total_tokens if hasattr(usage, 'total_tokens') else (input_tokens + output_tokens)
                            self.logger.info(f"流式生成使用API实际token数据 - 输入: {input_tokens}, 输出: {output_tokens}, 总计: {total_tokens}")
                        else:
                            # 后备方案：使用估算
                            full_content = "".join(content_chunks)
                            input_tokens = self._estimate_tokens(prompt)
                            output_tokens = self._estimate_tokens(full_content)
                            total_tokens = input_tokens + output_tokens
                            self.logger.warning(f"流式API未返回完整token信息，使用估算 - 输入: {input_tokens}, 输出: {output_tokens}, 总计: {total_tokens}")

                        usage_info = ModelUsageInfo(
                            vendor="dashscope",
                            model_name=self.config.ai_model_name,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            total_tokens=total_tokens,
                            response_time=response_time
                        )
                        self.logger.info(f"流式生成完成 - 输入: {input_tokens} tokens, 输出: {output_tokens} tokens, 耗时: {response_time:.2f}s")
                else:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        content_chunks.append(delta.content)
                        yield delta.content, None

            # 返回最终的用量信息
            if usage_info:
                yield "", usage_info
            else:
                # 如果没有收到usage信息，创建估算的usage_info
                response_time = time.time() - start_time
                full_content = "".join(content_chunks)
                input_tokens = self._estimate_tokens(prompt)
                output_tokens = self._estimate_tokens(full_content)
                total_tokens = input_tokens + output_tokens

                usage_info = ModelUsageInfo(
                    vendor="dashscope",
                    model_name=self.config.ai_model_name,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    total_tokens=total_tokens,
                    response_time=response_time
                )
                self.logger.warning(f"流式生成未收到API usage信息，使用估算数据 - 输入: {input_tokens}, 输出: {output_tokens}")
                yield "", usage_info

        except Exception as e:
            self.logger.error(f"DashScope流式调用失败: {str(e)}")
            raise

    def _estimate_tokens(self, text: str) -> int:
        """估算文本的token数量
        
        简单的估算方法：中文字符按1个token计算，英文单词按平均1.3个token计算
        
        Args:
            text: 要估算的文本
            
        Returns:
            int: 估算的token数量
        """
        if not text:
            return 0
        
        # 简单估算：中文字符数 + 英文单词数 * 1.3
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len(text.replace(' ', '').encode('utf-8')) - chinese_chars
        
        return chinese_chars + int(english_words * 1.3)

#!/usr/bin/env python3
"""绘本生成器MCP服务器简单测试脚本"""

import json
import requests
import sys

class SimpleMCPTester:
    """简单MCP测试器 - 只测试工具调用"""
    
    def __init__(self, server_url="http://127.0.0.1:8000"):
        self.server_url = server_url
        self.session = requests.Session()
    
    def test_generate_picture_book_tool(self):
        """测试绘本生成工具"""
        print("🧪 绘本生成器MCP工具测试")
        print("=" * 50)
        
        session_id = None
        
        # 第1步：初始化MCP会话
        print("🚀 步骤1: 初始化MCP会话...")
        try:
            init_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            
            init_response = self.session.post(
                f"{self.server_url}/mcp/",
                json=init_payload,
                headers=headers,
                timeout=10
            )
            
            print(f"   初始化状态码: {init_response.status_code}")
            
            if init_response.status_code != 200:
                print(f"   ❌ 初始化失败: {init_response.text}")
                return
            else:
                # 获取会话ID
                session_id = init_response.headers.get("mcp-session-id")
                if session_id:
                    print(f"   ✅ 初始化成功，会话ID: {session_id}")
                else:
                    print("   ✅ 初始化成功，但未获取到会话ID")
                
                # 解析初始化响应（可能是SSE格式）
                init_text = init_response.text
                if "event: message" in init_text:
                    print("   📡 收到SSE格式响应")
                    # 提取JSON数据
                    import re
                    json_match = re.search(r'data: ({.*})', init_text)
                    if json_match:
                        try:
                            init_data = json.loads(json_match.group(1))
                            if "result" in init_data:
                                print("   ✅ 初始化响应验证成功")
                            elif "error" in init_data:
                                print(f"   ❌ 初始化错误: {init_data['error']}")
                        except json.JSONDecodeError:
                            pass
        
        except Exception as e:
            print(f"   ❌ 初始化错误: {e}")
            return
        
        # 第1.5步：发送initialized通知
        print("\n🔔 步骤1.5: 发送initialized通知...")
        try:
            initialized_payload = {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {}
            }
            
            initialized_headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            if session_id:
                initialized_headers["mcp-session-id"] = session_id
            
            initialized_response = self.session.post(
                f"{self.server_url}/mcp/",
                json=initialized_payload,
                headers=initialized_headers,
                timeout=10
            )
            
            print(f"   通知状态码: {initialized_response.status_code}")
            if initialized_response.status_code in [200, 202]:
                print("   ✅ initialized通知发送成功")
                # 给服务器一点时间处理初始化
                import time
                time.sleep(0.5)
                print("   ⏰ 等待服务器完成初始化...")
            else:
                print(f"   ⚠️ initialized通知失败: {initialized_response.text}")
        
        except Exception as e:
            print(f"   ⚠️ initialized通知错误: {e}")
        
        # 第2步：调用绘本生成工具
        print("\n📚 步骤2: 调用绘本生成工具...")
        
        try:
            # 构建JSON-RPC请求
            payload = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "generate_picture_book",
                    "arguments": {
                        "book_title": "小猫钓鱼",
                        "book_style": "卡通",
                        "target_pages": 4,
                        "book_theme": "一只可爱的小猫在河边钓鱼的温馨故事",
                        "user_requirements": "画面要温馨可爱，适合3-6岁儿童"
                    }
                }
            }
            
            print(f"   📦 请求负载: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            # 准备请求头，包含会话ID（如果有）
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            if session_id:
                headers["mcp-session-id"] = session_id
                print(f"   📝 使用会话ID: {session_id}")
            
            print("🔄 正在发送请求...")
            print(f"   URL: {self.server_url}/mcp/")
            print(f"   参数: book_title='小猫钓鱼', book_style='卡通', target_pages=4")
            print("   ⏳ 绘本生成可能需要几分钟时间，请耐心等待...")
            
            response = self.session.post(
                f"{self.server_url}/mcp/",
                json=payload,
                headers=headers,
                timeout=3000,  # 增加到5分钟，因为绘本生成需要时间
                stream=True    # 启用流式响应
            )
            
            print(f"\n📡 服务器响应 (流式):")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功！开始解析流式数据...")
                
                current_event = None
                for line in response.iter_lines():
                    if not line:
                        continue
                    
                    line = line.decode('utf-8')
                    
                    if line.startswith('event:'):
                        current_event = line[len('event:'):].strip()
                    elif line.startswith('data:') and current_event == 'message':
                        json_data_str = line[len('data:'):].strip()
                        if not json_data_str:
                            continue
                            
                        try:
                            data = json.loads(json_data_str)
                            
                            # Case 1: Final Result
                            if 'result' in data:
                                # 故事流结束后打印一个换行，确保格式清晰
                                print("\n\n--- 📖 故事剧本结束 ---\n")
                                print("--- ✅ 任务完成 ---")
                                final_result = data.get("result")
                                # FastMCP 2.x 将最终结果包在content/text中
                                final_content_str = final_result['content'][0]['text']
                                final_data = json.loads(final_content_str)
                                
                                # 为了可读性，单独处理页面信息
                                final_pages = final_data.pop("pages", [])
                                
                                print("最终返回数据:")
                                print(json.dumps(final_data, ensure_ascii=False, indent=2))
                                
                                if final_pages:
                                    print("\n绘本页面详情:")
                                    for i, page in enumerate(final_pages):
                                        print(f"  --- 第 {i+1} 页 ---")
                                        print(f"    标题: {page.get('page_title')}")
                                        print(f"    本地路径: {page.get('page_images')}")
                                        print(f"    远程URL: {page.get('page_image_urls')}")
                                        print(f"    描述: {page.get('page_content')[:50]}...")
                                
                            # Case 2: Error Response
                            elif 'error' in data:
                                error_info = data.get('error')
                                print(f"\n\n--- ❌ 任务失败 ---")
                                print(f"错误代码: {error_info.get('code')}")
                                print(f"错误信息: {error_info.get('message')}")

                            # Case 3: Progress/Streaming Notification
                            elif data.get('method') == 'notifications/message':
                                params = data.get('params', {})
                                level = params.get('level')
                                
                                if level == 'info':
                                    progress_data_str = params.get('data', '{}')
                                    try:
                                        # 再次解析我们自己编码的JSON
                                        payload = json.loads(progress_data_str)
                                        status = payload.get('status')
                                        
                                        if status == 'story_streaming':
                                            chunk = payload.get("chunk", "")
                                            print(chunk, end='', flush=True)
                                        elif status == 'model_usage':
                                            usage = payload.get('usage', {})
                                            print(f"\n[用量] "
                                                  f"厂商: {usage.get('vendor')}, "
                                                  f"模型: {usage.get('model_name')}, "
                                                  f"输入: {usage.get('input_tokens')}, "
                                                  f"输出: {usage.get('output_tokens')}")
                                        elif status: # 其他结构化进度
                                            message = payload.get('message', '')
                                            extra = payload.get('extra', {})
                                            
                                            if status == 'story_generation_started':
                                                print("\n--- 📖 故事剧本开始 ---")
                                            
                                            print(f"\n[进度] {message} (状态: {status})", end='')
                                            
                                            # 特别处理图片URL的实时返回
                                            if status == 'image_generation_completed':
                                                urls = extra.get('image_urls', [])
                                                if urls:
                                                    print(f" -> 图片URL: {', '.join(urls)}")
                                                else:
                                                    print() # 换行
                                            elif extra:
                                                print(f"  > 详情: {extra}")
                                            else:
                                                print() # 换行

                                    except json.JSONDecodeError:
                                        # 普通的info日志
                                        print(f"\n[INFO] {progress_data_str}")

                        except json.JSONDecodeError:
                            print(f"\n⚠️ 无法解析顶层JSON: {json_data_str}")
                        
                        # 重置事件，为下一对做准备
                        current_event = None
                
                print("\n\n✅ 流式响应接收完毕。")

            else:
                print(f"   ❌ 请求失败: {response.text}")

        except requests.exceptions.Timeout:
            print("\n   ❌ 请求超时！绘本生成时间可能过长。")
        except requests.exceptions.RequestException as e:
            print(f"\n   ❌ 请求发生错误: {e}")
            
        except requests.exceptions.ConnectionError as e:
            print("   ❌ 连接错误")
            print(f"   🔍 详细错误: {e}")
            print("   💡 可能的原因:")
            print("      1. 服务器在处理过程中断开了连接")
            print("      2. 绘本生成时间过长导致连接超时")
            print("   📁 请检查服务器是否仍在运行和输出目录")
            
        except Exception as e:
            print(f"   ❌ 发生未知错误: {e}")
            print("   📁 请检查服务器日志和输出目录")
        
        print("\n" + "=" * 50)

def main():
    """主函数"""
    print("绘本生成器MCP工具测试脚本")
    print("请确保MCP服务器正在运行在 http://127.0.0.1:8000")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    print()
    
    # 创建测试器实例
    tester = SimpleMCPTester()
    
    # 运行工具测试
    tester.test_generate_picture_book_tool()
    
    print("测试完成！")

if __name__ == "__main__":
    main()

"""Grade Analysis Safe Code Executor Module

实现安全的 Python 代码执行环境，使用 Docker 容器隔离。
"""
import json
import time
import tempfile
import os
from typing import Dict, Any, Optional
import docker
from docker.errors import ContainerError, ImageNotFound, APIError

from .config import GradeAnalysisConfig
from .logger import get_logger
from .models import CodeExecutionResult


class SafeCodeExecutor:
    """安全的 Python 代码执行器，使用 Docker 容器隔离"""
    
    def __init__(self, config: GradeAnalysisConfig):
        """
        初始化安全代码执行器。

        Args:
            config (GradeAnalysisConfig): 应用配置实例。
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.docker_client = None
        self.init_docker_client()
    
    def init_docker_client(self):
        """初始化 Docker 客户端"""
        try:
            self.docker_client = docker.from_env()
            # 测试 Docker 连接
            self.docker_client.ping()
            self.logger.info("Docker 客户端初始化成功")
        except Exception as e:
            self.logger.error(f"初始化 Docker 客户端失败: {str(e)}")
            raise ValueError(f"Docker 服务不可用: {str(e)}")
    
    def _create_safe_code(self, user_code: str, data_string: str) -> str:
        """
        创建安全的执行代码，包含数据加载和结果输出。
        
        Args:
            user_code (str): 用户生成的分析代码
            data_string (str): 原始数据字符串
            
        Returns:
            str: 完整的可执行代码
        """
        safe_code = f'''
import pandas as pd
import numpy as np
import json
import io
import sys
from datetime import datetime

# 禁用危险函数
import builtins
dangerous_functions = ['exec', 'eval', 'compile', '__import__', 'open', 'input']
for func_name in dangerous_functions:
    if hasattr(builtins, func_name):
        setattr(builtins, func_name, lambda *args, **kwargs: None)

try:
    # 数据加载
    data_content = """{data_string}"""
    
    # 尝试解析数据
    if data_content.strip().startswith('|'):
        # Markdown 表格格式
        lines = [line.strip() for line in data_content.strip().split('\\n') if line.strip()]
        if len(lines) >= 2:
            # 提取表头
            header_line = lines[0]
            headers = [col.strip() for col in header_line.split('|')[1:-1]]
            
            # 跳过分隔符行，提取数据行
            data_lines = lines[2:] if len(lines) > 2 else []
            data_rows = []
            for line in data_lines:
                if '|' in line:
                    row = [col.strip() for col in line.split('|')[1:-1]]
                    if len(row) == len(headers):
                        data_rows.append(row)
            
            # 创建 DataFrame
            df = pd.DataFrame(data_rows, columns=headers)
            
            # 尝试转换数值列
            for col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col], errors='ignore')
                except:
                    pass
    else:
        # 尝试其他格式（CSV等）
        df = pd.read_csv(io.StringIO(data_content))
    
    # 执行用户代码
    {user_code}
    
    # 如果用户代码没有定义 result 变量，尝试自动生成
    if 'result' not in locals():
        result = {{
            "data_info": {{
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": df.columns.tolist()
            }},
            "basic_stats": df.describe().to_dict() if not df.empty else {{}},
            "data_sample": df.head().to_dict() if not df.empty else {{}}
        }}
    
    # 确保结果是可序列化的
    if not isinstance(result, dict):
        result = {{"analysis_result": str(result)}}
    
    # 输出结果
    print("EXECUTION_RESULT_START")
    print(json.dumps(result, ensure_ascii=False, default=str))
    print("EXECUTION_RESULT_END")
    
except Exception as e:
    error_result = {{
        "error": True,
        "error_message": str(e),
        "error_type": type(e).__name__
    }}
    print("EXECUTION_RESULT_START")
    print(json.dumps(error_result, ensure_ascii=False))
    print("EXECUTION_RESULT_END")
'''
        return safe_code
    
    async def execute_code(
        self, 
        code: str, 
        data_string: str
    ) -> CodeExecutionResult:
        """
        在 Docker 容器中安全执行代码。
        
        Args:
            code (str): 要执行的 Python 代码
            data_string (str): 数据字符串
            
        Returns:
            CodeExecutionResult: 执行结果
        """
        self.logger.info("开始执行代码")
        start_time = time.time()
        
        try:
            # 创建安全的执行代码
            safe_code = self._create_safe_code(code, data_string)
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(safe_code)
                temp_file_path = f.name
            
            try:
                # 运行 Docker 容器
                container = self.docker_client.containers.run(
                    image=self.config.docker_image,
                    command=f"python /app/script.py",
                    volumes={
                        temp_file_path: {'bind': '/app/script.py', 'mode': 'ro'}
                    },
                    mem_limit=f"{self.config.memory_limit}m",
                    network_disabled=True,
                    remove=True,
                    detach=False,
                    stdout=True,
                    stderr=True,
                    timeout=self.config.code_execution_timeout
                )
                
                output = container.decode('utf-8')
                execution_time = time.time() - start_time
                
                # 解析执行结果
                result_data = self._parse_execution_output(output)
                
                self.logger.info(f"代码执行成功，耗时: {execution_time:.2f}秒")
                
                return CodeExecutionResult(
                    success=True,
                    output=json.dumps(result_data, ensure_ascii=False),
                    execution_time=execution_time,
                    memory_usage=None  # Docker 内存使用情况较难获取
                )
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except ContainerError as e:
            execution_time = time.time() - start_time
            error_msg = f"容器执行错误: {e.stderr.decode('utf-8') if e.stderr else str(e)}"
            self.logger.error(error_msg)
            
            return CodeExecutionResult(
                success=False,
                output="",
                error=error_msg,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"代码执行失败: {str(e)}"
            self.logger.error(error_msg)
            
            return CodeExecutionResult(
                success=False,
                output="",
                error=error_msg,
                execution_time=execution_time
            )
    
    def _parse_execution_output(self, output: str) -> Dict[str, Any]:
        """
        解析代码执行的输出，提取结果数据。
        
        Args:
            output (str): 容器的标准输出
            
        Returns:
            Dict[str, Any]: 解析后的结果数据
        """
        try:
            # 查找结果标记
            start_marker = "EXECUTION_RESULT_START"
            end_marker = "EXECUTION_RESULT_END"
            
            start_idx = output.find(start_marker)
            end_idx = output.find(end_marker)
            
            if start_idx != -1 and end_idx != -1:
                result_json = output[start_idx + len(start_marker):end_idx].strip()
                return json.loads(result_json)
            else:
                # 如果没有找到标记，返回原始输出
                return {"raw_output": output}
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"解析执行结果失败: {str(e)}")
            return {"raw_output": output, "parse_error": str(e)}
        except Exception as e:
            self.logger.error(f"处理执行输出失败: {str(e)}")
            return {"error": str(e), "raw_output": output}
    
    def check_docker_availability(self) -> bool:
        """
        检查 Docker 服务是否可用。
        
        Returns:
            bool: Docker 是否可用
        """
        try:
            if self.docker_client:
                self.docker_client.ping()
                return True
            return False
        except Exception:
            return False

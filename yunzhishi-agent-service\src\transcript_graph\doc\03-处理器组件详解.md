# 处理器组件详解

## 概述

Transcript Graph 的处理器组件是系统的核心执行单元，每个处理器都是一个专门的 AI 智能体，负责特定的任务。这些处理器采用统一的设计模式，都使用 `@traceable` 装饰器进行监控，并通过配置管理系统进行初始化。

## 核心处理器架构

### 基础设计模式

所有处理器都遵循以下设计模式：

```python
class ProcessorAgent:
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def main_method(self, ...):
        # 核心处理逻辑
        pass
```

### 共同特性

1. **配置驱动**：所有处理器都通过 `LessonPlanConfiguration` 进行配置
2. **文件管理**：使用 `FileIO` 进行文件操作
3. **模型集成**：通过 `create_llm_client` 创建 AI 模型客户端
4. **可追踪性**：使用 `@traceable` 装饰器进行全流程监控

## 详细处理器分析

### 1. TeachingProcessTxtGenerateAgent - 文本教学流程生成器

#### 功能概述
负责基于文本课程信息生成教学流程大纲，是文本处理路径的核心组件。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
class TeachingProcessTxtGenerateAgent:
    """文本教学流程生成智能体，负责基于文本信息生成教学流程"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
````
</augment_code_snippet>

#### 核心方法

**1. generate_teaching_process()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
@traceable
def generate_teaching_process(self, course_info: CourseInfo) -> str:
    """生成教学流程。"""
    # 格式化课程信息
    course_info_formatted=format_course_info(course_info)
    
    # 准备提示词
    prompt = TEACHING_PROCESS_TXT_SYSTEM_PROMPT.format(
        course_info_formatted=course_info_formatted
    )
    
    # 如果有PDF内容，添加到提示词中
    if course_info.pdf_content:
        prompt += f"\n\n参考PDF内容：\n{course_info.pdf_content}"
````
</augment_code_snippet>

**功能详解：**
- 接收 `CourseInfo` 对象作为输入
- 使用 `format_course_info()` 格式化课程信息
- 结合 PDF 内容（如果有）构建完整提示词
- 调用文本模型生成教学流程
- 将结果保存为 `teaching_process.md` 文件

**2. parse_teaching_process()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
@traceable
def parse_teaching_process(self, teaching_process: str) -> List[Dict[str, str]]:
    """解析教学流程内容，提取各个环节"""
    # 使用正则表达式匹配一级标题及其内容
    pattern = r'# (.*?)\n(.*?)(?=\n# |$)'
    matches = re.findall(pattern, teaching_process, re.DOTALL)
    
    sections = []
    for i, (title, content) in enumerate(matches):
        # 生成两位数的process_id
        process_id = f"process_{i+1:02d}"
        sections.append({
            "id": process_id,
            "title": title.strip(),
            "content": content.strip()
        })
````
</augment_code_snippet>

**功能详解：**
- 使用正则表达式解析 Markdown 格式的教学流程
- 提取一级标题（# 标题）及其内容
- 为每个环节生成唯一的 `process_id`（如 process_01, process_02）
- 返回结构化的教学环节列表

### 2. TeachingProcessVisionGenerateAgent - 视觉教学流程生成器

#### 功能概述
专门处理 PDF 文档，将 PDF 转换为图像后使用视觉模型进行分析，生成教学流程。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
class TeachingProcessVisionGenerateAgent:
    """视觉教学流程生成智能体，负责处理PDF图像并生成教学流程"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
        # 为视觉处理创建直接的模型客户端
        if config.vision_model_provider == "zhipuai":
            # ... 智谱AI客户端初始化
        elif config.vision_model_provider in ["siliconflow", "dashscope"]:
            # ... OpenAI兼容客户端初始化
````
</augment_code_snippet>

#### 核心方法

**1. _convert_pdf_to_images()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
def _convert_pdf_to_images(self, pdf_path: str, start_page: Optional[int] = None, 
                          end_page: Optional[int] = None) -> List[str]:
    """将PDF转换为图像文件"""
    doc = fitz.open(pdf_path)
    img_paths = []
    
    # 确定页面范围
    start = (start_page - 1) if start_page else 0
    end = end_page if end_page else len(doc)
    
    # 创建临时目录
    temp_dir = os.path.join(self.config.temp_dir, f"pdf_images_{int(time.time())}")
    os.makedirs(temp_dir, exist_ok=True)
````
</augment_code_snippet>

**功能详解：**
- 使用 PyMuPDF (fitz) 库处理 PDF 文档
- 支持指定页面范围转换
- 将每页转换为高质量 PNG 图像
- 创建临时目录存储图像文件
- 返回图像文件路径列表

**2. generate_teaching_process()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
@traceable
def generate_teaching_process(self, course_info: CourseInfo) -> str:
    """从PDF图像生成教学流程。"""
    try:
        # 转换PDF为图像
        img_paths = self._convert_pdf_to_images(
            course_info.pdf_temp_path,
            course_info.pdf_start_page,
            course_info.pdf_end_page
        )
        
        # 准备视觉提示
        messages = [
            {"role": "system", "content": TEACHING_PROCESS_VISION_SYSTEM_PROMPT.format(...)},
            {"role": "user", "content": [
                {"type": "text", "text": "这是课程资料的图像，请根据这些资料生成教学流程。"},
                *[{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self._encode_image(img_path)}"}} for img_path in img_paths]
            ]}
        ]
````
</augment_code_snippet>

**功能详解：**
- 将 PDF 转换为图像序列
- 构建多模态消息格式（文本 + 图像）
- 使用 base64 编码传输图像数据
- 调用视觉模型进行分析
- 自动清理临时文件

### 3. TranscriptSectionGenerateAgent - 逐字稿片段生成器

#### 功能概述
为单个教学环节生成详细的逐字稿内容，是系统的核心内容生成组件。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
class TranscriptSectionGenerateAgent:
    """教学逐字稿生成智能体，负责生成每个教学环节的逐字稿"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
````
</augment_code_snippet>

#### 核心方法

**generate_transcript_section()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
@traceable
def generate_transcript_section(self, course_info: CourseInfo, teaching_process: str, 
                               process_id: str, process_title: str, process_content: str) -> str:
    """生成教学环节的逐字稿。"""
    # 格式化课程信息
    course_basic_info=course_info.course_basic_info
    
    # 准备提示词
    prompt = TRANSCRIPT_SECTION_SYSTEM_PROMPT.format(
        course_basic_info=course_basic_info,
        teaching_process=teaching_process,
        process_title=process_title,
    )
    
    self.llm.push_trace_info(f"STR_00_{process_id}", f"教学逐字稿生成")
````
</augment_code_snippet>

**功能详解：**
- 接收单个教学环节的信息
- 结合课程信息和整体教学流程构建上下文
- 使用专门的逐字稿生成提示词
- 生成 800-1200 字的详细逐字稿
- 保存为独立的文件（如 `transcript_process_01.md`）

**特色功能：**
- 支持并行处理多个环节
- 包含教师讲话、学生互动、教学动作
- 语言自然，符合中国课堂教学风格

### 4. TranscriptSectionsMergeAgent - 逐字稿合并器

#### 功能概述
将所有教学环节的逐字稿合并为完整的教学逐字稿文档。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
class TranscriptSectionsMergeAgent:
    """教学逐字稿合并智能体，负责合并所有环节的逐字稿"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
````
</augment_code_snippet>

#### 核心方法

**merge_transcript_sections()**

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
@traceable
def merge_transcript_sections(self, teaching_processes: Dict[str, TeachingProcessInfo]) -> str:
    """合并所有教学环节的逐字稿。"""
    # 按照process_id排序
    sorted_processes = sorted(
        teaching_processes.items(),
        key=lambda x: int(x[0].split('_')[1])
    )
    
    # 直接拼接所有环节的逐字稿，不添加标题或空行
    merged_transcript = []
    for process_id, process_info in sorted_processes:
        transcript = process_info.transcript
        if transcript:
            merged_transcript.append(transcript)
````
</augment_code_snippet>

**功能详解：**
- 按 `process_id` 顺序排序所有环节
- 直接拼接逐字稿内容，保持连贯性
- 生成最终的完整逐字稿文件
- 支持文件保存和 OSS 上传

### 5. FileProcessAgent - 文件处理器

#### 功能概述
处理文件相关操作，包括 PDF 处理、文件上传、临时文件管理等。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
class FileProcessAgent:
    """文件处理智能体，负责处理文件相关操作"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.oss_manager = OSSStorageManager(config)
````
</augment_code_snippet>

**主要功能：**
- PDF 文件下载和缓存
- 临时文件管理
- OSS 文件上传
- 文件格式验证

## 工具函数

### format_course_info()

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
def format_course_info(course_info: CourseInfo) -> str:
    """格式化课程信息为字符串"""
    formatted_info = f"课程基本信息：{course_info.course_basic_info}\n"
    formatted_info += f"教学信息：{course_info.teaching_info}\n"
    
    if course_info.personal_requirements:
        formatted_info += f"个性化要求：{course_info.personal_requirements}\n"
    
    return formatted_info
````
</augment_code_snippet>

### get_transcript_folder()

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/processors.py" mode="EXCERPT">
````python
def get_transcript_folder(course_basic_info: str, config: LessonPlanConfiguration) -> str:
    """获取或创建逐字稿文件夹路径"""
    global _transcript_folder
    
    if _transcript_folder is None:
        # 从课程基本信息中提取课程名称
        course_name = extract_course_name(course_basic_info)
        sanitized_name = sanitize_filename(course_name)
        
        # 创建文件夹路径
        folder_path = os.path.join(config.output_dir, f"transcript_{sanitized_name}")
        os.makedirs(folder_path, exist_ok=True)
        _transcript_folder = folder_path
    
    return _transcript_folder
````
</augment_code_snippet>

## 处理器协作流程

### 1. 文本处理路径
```
TeachingProcessTxtGenerateAgent → TranscriptSectionGenerateAgent → TranscriptSectionsMergeAgent
```

### 2. 视觉处理路径
```
TeachingProcessVisionGenerateAgent → TranscriptSectionGenerateAgent → TranscriptSectionsMergeAgent
```

### 3. 并行处理机制
- `TranscriptSectionGenerateAgent` 支持并行处理多个教学环节
- 使用 LangGraph 的 Send 机制实现任务分发
- 每个环节独立生成逐字稿，提高效率

## 错误处理和监控

### 1. 追踪机制
- 所有核心方法都使用 `@traceable` 装饰器
- 自动记录处理时间、输入输出、错误信息
- 支持分布式追踪和性能分析

### 2. 错误处理
- 文件操作异常处理
- 模型调用失败重试
- 临时文件自动清理
- 详细的错误日志记录

### 3. 资源管理
- 自动清理临时图像文件
- PDF 文件缓存管理
- 内存使用优化

## 扩展指南

### 添加新处理器
1. 继承基础设计模式
2. 实现必要的初始化方法
3. 添加 `@traceable` 装饰器
4. 在 `graph.py` 中注册新节点

### 自定义提示词
1. 在 `prompts.py` 中定义新提示词
2. 在处理器中引用和使用
3. 支持动态参数替换

### 模型集成
1. 使用 `create_llm_client()` 创建客户端
2. 通过配置文件指定模型参数
3. 支持多种模型提供商

这些处理器组件构成了 Transcript Graph 的核心执行引擎，通过精心设计的协作机制，实现了高效、可靠的教学逐字稿生成功能。

# 教案生成服务压力测试指南

## 项目概述

本项目是一个针对教案生成服务的压力测试系统，主要用于评估服务在高并发场景下的性能表现。项目采用FastAPI构建测试服务，使用Apache Bench (ab)工具进行压力测试。

## 项目结构

```
test0212/
├── app.py              # FastAPI测试服务实现
├── run_benchmark.sh    # 压力测试执行脚本
├── requirements.txt    # 项目依赖
├── logs/              # 测试日志目录
└── results/           # 测试结果目录
```

## 技术栈

- FastAPI: 构建高性能异步Web服务
- Apache Bench (ab): 压力测试工具
- Python 3.x
- Shell Script

## 核心组件

### 1. 测试服务 (app.py)

测试服务实现了以下主要功能：

- 异步请求处理
- 多worker并发处理
- 随机测试数据生成
- 请求队列管理
- 结果追踪和状态检查

主要特点：
- 使用异步队列管理请求
- 支持5个并发worker
- 内置模板化测试数据
- 完整的错误处理和日志记录

#### 代码实现：

```python
from fastapi import FastAPI, HTTPException
import json
import random
from datetime import datetime
import httpx
from typing import Dict, Any, List
import os
from pydantic import BaseModel
import logging
import asyncio
from langgraph_sdk import get_client

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="教案生成压力测试服务")

# 全局变量
request_queue = asyncio.Queue()
WORKER_COUNT = 5  # 并发worker数量
active_workers = []
results = {}  # 存储请求结果

class TestDataGenerator:
    def __init__(self):
        self.used_templates = set()
        self.templates = {
            "templates": [
                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "语文",
                            "lesson_name": "白鹭",
                            "lesson_count": "1",
                            "lesson_target": "五年级",
                            "lesson_pdf": "chinese_book.pdf"
                        }
                    }]
                },
                                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "语文",
                            "lesson_name": "山水",
                            "lesson_count": "1",
                            "lesson_target": "四年级",
                            "lesson_pdf": "chinese_book.pdf"
                        }
                    }]
                },
                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "数学",
                            "lesson_name": "数学思维",
                            "lesson_count": "1",
                            "lesson_target": "五年级",
                            "lesson_pdf": ""
                        }
                    }]
                },
                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "科学",
                            "lesson_name": "小小工程师",
                            "lesson_count": "1",
                            "lesson_target": "五年级",
                            "lesson_pdf": "science_book.pdf"
                        }
                    }]
                },
                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "科学",
                            "lesson_name": "节能小屋",
                            "lesson_count": "1",
                            "lesson_target": "六年级",
                            "lesson_pdf": ""
                        }
                    }]
                },
                {
                    "messages": [{
                        "role": "human",
                        "content": {
                            "lesson_coursename": "英语",
                            "lesson_name": "hello",
                            "lesson_count": "1",
                            "lesson_target": "三年级",
                            "lesson_pdf": ""
                        }
                    }]
                }
                # ... 其他模板 ...
            ]
        }

    def get_random_template(self) -> Dict[str, Any]:
        available_indices = set(range(len(self.templates["templates"]))) - self.used_templates
        if not available_indices:
            self.used_templates.clear()
            available_indices = set(range(len(self.templates["templates"])))
        
        template_index = random.choice(list(available_indices))
        self.used_templates.add(template_index)
        template = self.templates["templates"][template_index].copy()
        
        lesson_name = template["messages"][0]["content"]["lesson_name"]
        random_suffix = random.randint(1000, 9999)
        template["messages"][0]["content"]["lesson_name"] = f"{lesson_name}_{random_suffix}"
        
        return template

test_data_generator = TestDataGenerator()

async def process_request(request_id: int):
    """处理单个请求"""
    try:
        client = get_client(url="http://47.102.193.108:8024")
        test_data = test_data_generator.get_random_template()
        logger.info(f"任务 {request_id} 开始处理")
        
        async for chunk in client.runs.stream(
            None,
            "lessonplan",
            input=test_data,
            stream_mode="updates"
        ):
            if chunk.event == "updates":
                if isinstance(chunk.data, dict):
                    if 'teaching_process_outline' in str(chunk.data):
                        logger.info(f"任务 {request_id} 完成")
                        results[request_id] = chunk.data
                        return chunk.data
            elif chunk.event == "error":
                logger.error(f"任务 {request_id} 错误: {chunk.data}")
                results[request_id] = {"error": str(chunk.data)}
                raise HTTPException(status_code=500, detail=str(chunk.data))
                
    except Exception as e:
        logger.error(f"任务 {request_id} 处理失败: {str(e)}")
        results[request_id] = {"error": str(e)}
        raise HTTPException(status_code=500, detail=str(e))

async def worker(worker_id: int):
    """Worker处理函数"""
    while True:
        try:
            request_id = await request_queue.get()
            logger.info(f"Worker {worker_id} 开始处理请求 {request_id}")
            asyncio.create_task(process_request(request_id))
            logger.info(f"Worker {worker_id} 已分配请求 {request_id}")
            request_queue.task_done()
        except asyncio.CancelledError:
            logger.info(f"Worker {worker_id} 被取消")
            break
        except Exception as e:
            logger.error(f"Worker {worker_id} 处理请求时出错: {str(e)}")
            request_queue.task_done()

@app.on_event("startup")
async def startup_event():
    """服务启动时初始化"""
    global active_workers
    logger.info("启动worker池...")
    active_workers = [
        asyncio.create_task(worker(i))
        for i in range(WORKER_COUNT)
    ]

@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭时清理资源"""
    global active_workers
    logger.info("关闭worker池...")
    for w in active_workers:
        w.cancel()
    await asyncio.gather(*active_workers, return_exceptions=True)

@app.post("/generate_lesson_plan")
async def generate_lesson_plan():
    """生成教案API"""
    try:
        request_id = random.randint(1000, 9999)
        await request_queue.put(request_id)
        logger.info(f"请求 {request_id} 已加入队列")
        
        while request_id not in results:
            await asyncio.sleep(0.1)
        
        result = results.pop(request_id)
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        return result
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2. 压力测试脚本 (run_benchmark.sh)

脚本实现了完整的压力测试流程：

1. 环境检查
   - 验证服务可访问性
   - 创建必要的日志目录

2. 测试执行
   - 支持5并发的测试场景
   - 每个并发级别执行2次尝试
   - 自动失败重试机制
   - 测试间隔控制

3. 结果收集
   - 自动生成Markdown格式测试报告
   - 详细的性能指标统计
   - 完整的日志记录

#### 代码实现：

```bash
#!/bin/bash

echo "开始教案生成性能测试..."

# 创建日志目录
mkdir -p logs results

# 验证服务可访问性
echo "验证服务可访问性..."
if ! curl -s -X GET http://localhost:8000/docs > /dev/null; then
    echo "FastAPI服务不可访问，请确保服务已启动"
    exit 1
fi

for c in 5; do
    echo "执行 $c 并发测试..."
    for attempt in {1..2}; do
        echo "尝试第 $attempt 次..."
        
        # 记录开始时间
        START_TIME=$(date +%s)
        
        # 执行测试，添加 -l 参数忽略长度检查
        ab -n 10 -c $c \
           -T 'application/json' \
           -H "Accept: application/json" \
           -m POST \
           -s 300 \
           -r \
           -v 4 \
           -l \
           "http://localhost:8000/generate_lesson_plan" > logs/concurrent_${c}_attempt_${attempt}.log 2>&1
        
        # 记录结束时间和耗时
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        echo "测试完成，耗时: ${DURATION}秒"
        
        # 检查失败率（排除长度不一致的情况）
        NON_2XX=$(grep "Non-2xx responses:" logs/concurrent_${c}_attempt_${attempt}.log | awk '{print $3}')
        if [ -z "$NON_2XX" ] || [ "$NON_2XX" = "0" ]; then
            echo "测试成功完成"
            break
        fi
        echo "测试失败，非2xx响应数: $NON_2XX"
        echo "等待系统恢复(60秒)..."
        sleep 60
    done
    echo "等待下一轮并发测试(60秒)..."
    sleep 60
done

# 生成测试报告
echo "生成测试报告..."
cat > results/test_report.md << EOF
# 教案生成服务压力测试报告

## 测试环境
- 测试时间：$(date '+%Y-%m-%d %H:%M:%S')
- 测试工具：Apache Bench (ab)
- 测试接口：http://localhost:8000/generate_lesson_plan

## 测试结果

$(for c in 5; do
    for attempt in {1..2}; do
        if [ -f "logs/concurrent_${c}_attempt_${attempt}.log" ]; then
            echo "### 并发数 $c - 尝试 $attempt"
            echo "- 总请求数：$(grep "Complete requests:" logs/concurrent_${c}_attempt_${attempt}.log | awk '{print $3}')"
            echo "- 非2xx响应：$(grep "Non-2xx responses:" logs/concurrent_${c}_attempt_${attempt}.log | awk '{print $3}')"
            echo "- 平均响应时间：$(grep "Time per request:" logs/concurrent_${c}_attempt_${attempt}.log | head -1 | awk '{print $4}') ms"
            echo
        fi
    done
done)

EOF

echo "测试完成。结果保存在 results 目录中。"
echo "请查看 results/test_report.md 获取详细报告。"
```

## 测试流程

1. **环境准备**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动测试服务**
   ```bash
   python app.py
   ```

3. **执行压力测试**
   ```bash
   bash run_benchmark.sh
   ```

4. **查看测试结果**
   - 测试日志：`logs/concurrent_*_attempt_*.log`
   - 测试报告：`results/test_report.md`

## 测试指标

测试报告包含以下关键指标：
- 总请求数
- 成功率（2xx响应）
- 平均响应时间
- 并发处理能力
- 错误率统计

## 注意事项

1. 执行测试前确保：
   - 测试服务正常运行（默认端口8000）
   - 系统资源充足
   - 日志目录具有写入权限

2. 测试过程中：
   - 每轮测试间有60秒冷却时间
   - 失败自动重试，最多2次
   - 支持中断恢复

3. 结果分析：
   - 关注非2xx响应数量
   - 监控平均响应时间变化
   - 检查错误日志

## 依赖版本

```
fastapi==0.104.1
uvicorn==0.24.0
httpx==0.25.1
pydantic==2.4.2
``` 
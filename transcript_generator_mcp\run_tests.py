#!/usr/bin/env python3
"""测试运行脚本 - 一键运行所有测试"""
import asyncio
import os
import sys
import time
from pathlib import Path

def setup_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 设置环境变量
    os.environ.update({
        "AI_PROVIDER": "zhipuai",
        "AI_API_KEY": "a8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8.b8b8b8b8b8b8b8b8",
        "AI_MODEL_NAME": "glm-4-flash",
        "TEST_MODE": "false",
        "LOG_LEVEL": "INFO",
        "PARALLEL_LIMIT": "5",
        "MAX_SECTIONS": "6"
    })
    
    # 创建必要的目录
    Path("test_outputs").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    Path("outputs").mkdir(exist_ok=True)
    
    print("✅ 环境设置完成")

def run_unit_tests():
    """运行单元测试"""
    print("\n🧪 运行单元测试...")
    
    import subprocess
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_models.py", 
            "tests/test_services.py",
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("✅ 单元测试通过")
            return True
        else:
            print("❌ 单元测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 单元测试执行失败: {str(e)}")
        return False

async def run_comparison_tests():
    """运行功能对比测试"""
    print("\n🔍 运行功能对比测试...")
    
    try:
        from tests.test_comparison import TestFunctionalComparison
        
        test_instance = TestFunctionalComparison()
        
        # 创建模拟上下文
        class MockContext:
            def __init__(self):
                self.info_calls = []
                self.error_calls = []
            
            async def info(self, message):
                self.info_calls.append(message)
            
            async def error(self, error):
                self.error_calls.append(error)
        
        mock_context = MockContext()
        
        # 运行测试
        test_cases = [
            {
                "name": "小学数学面积课程",
                "course_basic_info": "小学三年级数学，第五单元：面积",
                "teaching_info": "让学生理解面积概念，学会计算长方形和正方形的面积",
                "personal_requirements": "注重动手操作，多用生活实例，增加师生互动"
            }
        ]
        
        await test_instance.test_mcp_service_functionality(test_cases, mock_context)
        
        print("✅ 功能对比测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 功能对比测试失败: {str(e)}")
        return False

async def run_mcp_integration_tests():
    """运行MCP集成测试"""
    print("\n🔗 运行MCP集成测试...")
    
    try:
        from tests.test_mcp_client import MCPIntegrationTest
        
        test_runner = MCPIntegrationTest()
        await test_runner.run_all_tests()
        
        print("✅ MCP集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ MCP集成测试失败: {str(e)}")
        return False

async def run_performance_tests():
    """运行性能测试"""
    print("\n⚡ 运行性能测试...")
    
    try:
        from tests.test_performance import PerformanceTest
        
        test_runner = PerformanceTest()
        await test_runner.run_performance_tests()
        
        print("✅ 性能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False

def generate_final_report():
    """生成最终测试报告"""
    print("\n📊 生成最终测试报告...")
    
    import json
    from datetime import datetime
    
    # 收集所有测试报告
    reports = {}
    
    # 读取各个测试报告
    test_outputs_dir = Path("test_outputs")
    
    for report_file in test_outputs_dir.glob("*.json"):
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                reports[report_file.stem] = json.load(f)
        except Exception as e:
            print(f"⚠️ 无法读取报告文件 {report_file}: {str(e)}")
    
    # 生成综合报告
    final_report = {
        "test_summary": {
            "timestamp": datetime.now().isoformat(),
            "total_test_suites": len(reports),
            "reports_generated": list(reports.keys())
        },
        "detailed_reports": reports
    }
    
    # 保存最终报告
    final_report_path = test_outputs_dir / "final_test_report.json"
    with open(final_report_path, 'w', encoding='utf-8') as f:
        json.dump(final_report, f, ensure_ascii=False, indent=2)
    
    print(f"📄 最终测试报告已保存到: {final_report_path}")
    
    # 打印摘要
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 测试套件数: {len(reports)}")
    print(f"📁 报告文件: {len(list(test_outputs_dir.glob('*.json')))} 个")
    
    if "comparison_test_report" in reports:
        comp_report = reports["comparison_test_report"]
        summary = comp_report.get("test_summary", {})
        print(f"✅ 功能测试成功率: {summary.get('successful_tests', 0)}/{summary.get('total_tests', 0)}")
    
    if "performance_report" in reports:
        perf_report = reports["performance_report"]
        summary = perf_report.get("summary", {})
        print(f"⚡ 平均响应时间: {summary.get('avg_response_time', 0):.2f}秒")
        print(f"🔄 最大并发数: {summary.get('max_concurrent', 0)}")
    
    print("=" * 60)

async def main():
    """主函数"""
    print("🚀 开始教学逐字稿生成器MCP服务测试")
    print("=" * 60)
    
    start_time = time.time()
    
    # 1. 设置环境
    setup_environment()
    
    # 2. 运行单元测试
    unit_test_success = run_unit_tests()
    
    # 3. 运行功能对比测试
    comparison_test_success = await run_comparison_tests()
    
    # 4. 运行MCP集成测试
    mcp_test_success = await run_mcp_integration_tests()
    
    # 5. 运行性能测试
    performance_test_success = await run_performance_tests()
    
    # 6. 生成最终报告
    generate_final_report()
    
    # 计算总耗时
    total_time = time.time() - start_time
    
    # 统计结果
    total_tests = 4
    successful_tests = sum([
        unit_test_success,
        comparison_test_success,
        mcp_test_success,
        performance_test_success
    ])
    
    print(f"\n🏁 测试完成!")
    print(f"⏱️ 总耗时: {total_time:.2f}秒")
    print(f"📊 成功率: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    
    if successful_tests == total_tests:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print("⚠️ 部分测试失败，请查看详细报告")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())

"""
Teaching Evaluation MCP Server

一个基于 FastMCP 框架的课堂教学分析服务，提供：
- 课堂教学转录文本的智能分析
- 基于 DashScope DeepSeek-V3 模型的专业评估
- 五维度教学质量评价体系
- 实时进度反馈和详细日志记录
- 结构化分析报告和可视化雷达图

作者: Teaching Evaluation MCP Team
版本: 1.0.0
许可: MIT License
"""

__version__ = "1.0.0"
__author__ = "Teaching Evaluation MCP Team"
__email__ = "<EMAIL>"
__description__ = "Professional classroom teaching analysis MCP server"

# 导出主要组件
from .config import TeachingEvaluationConfig
from .models import (
    TeachingAnalysisRequest,
    TeachingAnalysisResponse,
    ProgressUpdate,
    ModelUsageInfo,
    ErrorInfo
)

__all__ = [
    "TeachingEvaluationConfig",
    "TeachingAnalysisRequest", 
    "TeachingAnalysisResponse",
    "ProgressUpdate",
    "ModelUsageInfo",
    "ErrorInfo",
    "__version__",
    "__author__",
    "__email__",
    "__description__"
]

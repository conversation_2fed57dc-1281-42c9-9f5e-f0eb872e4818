import os
from typing import Dict, Any, Optional, List, Generator, ContextManager, Callable, Literal
from typing_extensions import TypedDict
from contextlib import contextmanager
import re

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, END, START
from langgraph.constants import Send
from langgraph.types import Command, interrupt
from langchain_core.runnables import RunnableConfig

from lessonplan_graph.state import (LessonPlanState, LessonPlanInput, LessonPlanOutput,
    TeachingProcessInfo, Router, CourseInfo, TeachingProcessOutline, TeachingProcessOutlinePreprocess, create_initial_state)
from shared.utils import get_message_content, FileIO, OSSStorageManager, sanitize_filename
from shared.configuration import LessonPlanConfiguration 
from lessonplan_graph.processors import (TeachingProcessOutlineTxtAgent,TeachingProcessOutlineVisionAgent,TeachingProcessExpandAgent,LessonplanSectionsMergeAgent,TeachingelementsGenerateAgent,FileProcessAgent,
    ElementCustomizeAgent, TeachingProcessOutlineModifyAgent, ElementModifyGenerateAgent, MindmapGenerateAgent, TranscriptGenerateAgent, PlananalysisGenerateAgent, TeachingMethodologyAgent)

# 加载环境变量配置
load_dotenv()

class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]   # 文本模型提供商
    vision_model_provider: Optional[str]   # 视觉模型提供商
    text_model: Optional[str]   # 文本模型名称
    vision_model: Optional[str]   # 视觉模型名称
    temp_dir: Optional[str]   # 临时文件目录
    output_dir: Optional[str]   # 输出目录
    oss_endpoint: Optional[str]   # 阿里云 OSS endpoint
    oss_bucket: Optional[str]   # 阿里云 OSS bucket name

# 全局智能体实例
teaching_process_outline_txt_agent = None
teaching_process_outline_vision_agent = None
teaching_process_outline_modify_agent = None
teaching_process_expand_agent = None
lessonplan_sections_merge_agent = None
teaching_elements_generate_agent = None
file_process_agent = None
element_customize_agent = None
element_modify_agent = None
mindmap_generate_agent = None
transcript_generate_agent = None
plananalysis_generate_agent = None
teaching_methodology_agent = None
lesson_plan_config = None  # 全局配置实例

def create_graph(config: Optional[RunnableConfig] = None) -> StateGraph:
    """创建工作流程图
    
    Args:
        config: 来自LangGraph Studio的配置
    """
    # 如果提供了配置，使用LessonPlanConfiguration .from_runnable_config创建配置对象
    global lesson_plan_config
    if config is not None:
        lesson_plan_config = LessonPlanConfiguration .from_runnable_config(config)
    else:
        lesson_plan_config = LessonPlanConfiguration ()
    
    # 初始化全局代理
    global teaching_process_outline_txt_agent, teaching_process_outline_vision_agent
    global teaching_process_outline_modify_agent, teaching_process_expand_agent, lessonplan_sections_merge_agent
    global teaching_elements_generate_agent, file_process_agent
    global element_customize_agent, element_modify_agent, mindmap_generate_agent, transcript_generate_agent, plananalysis_generate_agent
    global teaching_methodology_agent
    
    teaching_process_outline_txt_agent = TeachingProcessOutlineTxtAgent(lesson_plan_config)
    teaching_process_outline_vision_agent = TeachingProcessOutlineVisionAgent(lesson_plan_config)
    teaching_process_outline_modify_agent = TeachingProcessOutlineModifyAgent(lesson_plan_config)
    teaching_process_expand_agent = TeachingProcessExpandAgent(lesson_plan_config)
    lessonplan_sections_merge_agent = LessonplanSectionsMergeAgent(lesson_plan_config)
    teaching_elements_generate_agent = TeachingelementsGenerateAgent(lesson_plan_config)
    file_process_agent = FileProcessAgent(lesson_plan_config)
    element_customize_agent = ElementCustomizeAgent(lesson_plan_config)
    element_modify_agent = ElementModifyGenerateAgent(lesson_plan_config)
    mindmap_generate_agent = MindmapGenerateAgent(lesson_plan_config)
    transcript_generate_agent = TranscriptGenerateAgent(lesson_plan_config)
    plananalysis_generate_agent = PlananalysisGenerateAgent(lesson_plan_config)
    teaching_methodology_agent = TeachingMethodologyAgent(lesson_plan_config)
        
    # 创建工作流程图，传入配置模式
    workflow = StateGraph(LessonPlanState, ConfigSchema, input=LessonPlanInput, output=LessonPlanOutput)
    
    # 添加工作节点
    workflow.add_node("preprocess_info", preprocess_info)
    workflow.add_node("process_file", process_file)
    workflow.add_node("read_teaching_process_outline", read_teaching_process_outline)
    workflow.add_node("SUB_05_generate_modify_teaching_process_outline", generate_modify_teaching_process_outline)
    
    workflow.add_node("STR_00_generate_teaching_objectives", generate_teaching_objectives)
    workflow.add_node("STR_01_generate_teaching_keypoints", generate_teaching_keypoints)
    workflow.add_node("STR_02_generate_teaching_studentanalysis", generate_teaching_studentanalysis)
    workflow.add_node("STR_03_generate_teaching_methods", generate_teaching_methods)
    workflow.add_node("STR_04_generate_teaching_activities", generate_teaching_activities)
    workflow.add_node("STR_06_generate_teaching_boarddesign", generate_teaching_boarddesign)
    workflow.add_node("STR_05_generate_teaching_processes", expand_teaching_process_outline)
    
    workflow.add_node("merge_teachingplan_sections", merge_sections)
    workflow.add_node("wait_custom_feedback", wait_custom_feedback)
    workflow.add_node("add_custom_element", add_custom_element)
    workflow.add_node("modify_generated_element", modify_generated_element)
    workflow.add_node("generate_mindmap", generate_mindmap)
    workflow.add_node("generate_transcript", generate_transcript)
    workflow.add_node("generate_plananalysis", generate_plananalysis)
    
    # 设置工作流入口点
    workflow.add_edge(START, "preprocess_info")
    workflow.add_edge("process_file", "SUB_05_generate_modify_teaching_process_outline")
    workflow.add_edge("read_teaching_process_outline", "SUB_05_generate_modify_teaching_process_outline")

    workflow.add_conditional_edges(
        "SUB_05_generate_modify_teaching_process_outline",
        send_expand_task,
        [
            "STR_00_generate_teaching_objectives",
            "STR_01_generate_teaching_keypoints",
            "STR_02_generate_teaching_studentanalysis",
            "STR_03_generate_teaching_methods",
            "STR_04_generate_teaching_activities",
            "STR_06_generate_teaching_boarddesign",
            "STR_05_generate_teaching_processes"
        ]
    )

    # 添加检查完成状态的边
    workflow.add_edge("STR_00_generate_teaching_objectives", "merge_teachingplan_sections")
    workflow.add_edge("STR_01_generate_teaching_keypoints", "merge_teachingplan_sections")
    workflow.add_edge("STR_02_generate_teaching_studentanalysis", "merge_teachingplan_sections")
    workflow.add_edge("STR_03_generate_teaching_methods", "merge_teachingplan_sections")
    workflow.add_edge("STR_04_generate_teaching_activities", "merge_teachingplan_sections")
    workflow.add_edge("STR_06_generate_teaching_boarddesign", "merge_teachingplan_sections")
    workflow.add_edge("STR_05_generate_teaching_processes", "merge_teachingplan_sections")
    
    workflow.add_edge("merge_teachingplan_sections", "wait_custom_feedback")

    workflow.add_edge("add_custom_element", "wait_custom_feedback")
    workflow.add_edge("modify_generated_element", "wait_custom_feedback")
    workflow.add_edge("generate_mindmap", "wait_custom_feedback")
    workflow.add_edge("generate_transcript", "wait_custom_feedback")
    workflow.add_edge("generate_plananalysis", "wait_custom_feedback")
    
    return workflow.compile()

class GraphConfig(TypedDict):
    """工作流配置类型定义"""
    model_name: Literal["zhipuai"]  # 使用的模型名称
    lesson_stage: Optional[str]  # 学段
    lesson_coursename: Optional[str]  # 课程名称
    lesson_unitname: Optional[str]  # 课时名称
    lesson_version: Optional[str]  # 教材版本
    lesson_count: Optional[int]  # 课时数
    lesson_grade: Optional[str]  # 课程年级
    lesson_semester: Optional[str]  # 课程学期
    lesson_pdf_already: Optional[int]  # PDF情况，1系统已有，2用户自传，3无教材
    lesson_pdf_path: Optional[str]  # PDF文件路径（可选）
    lesson_pdf_start_page: Optional[int]  # PDF起始页码
    lesson_pdf_end_page: Optional[int]  # PDF结束页码
    lesson_methodology: Optional[str]  # 教学法，auto表示自动匹配，空或None表示不使用特定教学法
    user_personal: Optional[str]  # 用户个人信息和教学偏好

@contextmanager
def manage_workflow_state() -> Generator[LessonPlanConfiguration , None, None]:
    """管理工作流状态的上下文管理器"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 创建默认配置实例
        config = LessonPlanConfiguration ()
        
        yield config
        
    except Exception as e:
        raise
    finally:
        pass

@contextmanager
def manage_state_update(stage: str, initial_status: str) -> Generator[Dict[str, Any], None, None]:
    """管理状态更新的上下文"""
    state_update = {}
    try:
        yield state_update
        # 只有在没有设置router的情况下才设置默认状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 只有STR_开头的节点才添加ERR前缀
        if stage.startswith("STR_"):
            # 从STR_XX_中提取XX作为错误编号
            error_prefix = f"ERR_{stage[4:6]}"
            error_status = f"{error_prefix}_{initial_status}失败"
        else:
            error_status = f"{initial_status}失败"
            
        state_update["router"] = Router(
            stage=stage,
            status=error_status,
            error=str(e)
        )
        raise

def preprocess_info(state: LessonPlanState) -> Command[Literal["process_file", "read_teaching_process_outline", "SUB_05_generate_modify_teaching_process_outline"]]:
    """预处理用户输入的课程信息和获取用户习惯"""
    with manage_state_update("preprocess_info", "信息预处理") as state_update:
        # 获取用户输入
        last_message = state["messages"][-1]
        user_input = get_message_content(last_message)
        
        # 准备课程信息（不包含 PDF 内容，这部分将在 process_file 中处理）
        course_info = CourseInfo(
            lesson_stage=user_input.get("lesson_stage", ""),
            lesson_coursename=user_input.get("lesson_coursename", ""),
            lesson_unitname=user_input.get("lesson_unitname", ""),
            lesson_version=user_input.get("lesson_version"),
            lesson_grade=user_input.get("lesson_grade", ""),
            lesson_semester=user_input.get("lesson_semester", ""),
            lesson_count=int(user_input.get("lesson_count", 0)),
            lesson_pdf_already=int(user_input.get("lesson_pdf_already", 3)),
            lesson_pdf_path=user_input.get("lesson_pdf_path", ""),
            lesson_pdf_type="text",  # 默认值，将在 process_file 中更新
            lesson_pdf_content=None,  # 将在 process_file 中填充
            lesson_pdf_start_page=user_input.get("lesson_pdf_start_page"),
            lesson_pdf_end_page=user_input.get("lesson_pdf_end_page"),
            lesson_methodology=user_input.get("lesson_methodology"),
        )

        user_id = user_input.get("user_id")
        session_id = user_input.get("session_id")
        
        # 获取用户个人信息
        user_personal = user_input.get("user_personal", "")
        
        # 获取用户习惯
        user_habits = ""
        try:
            # 这里应该是实际的 API 调用
            # 例如：user_habits = api_client.get_user_habits(user_id)
            # 暂时使用空字符串作为默认值
            pass
        except Exception as e:
            print(f"获取用户习惯失败: {str(e)}")
        
        # 如果user_personal不为空，将其添加到user_habits中，中间加换行符
        if user_personal:
            if user_habits:
                user_habits += f"\n{user_personal}"
            else:
                user_habits = user_personal
        
        # 处理lesson_methodology
        if course_info.lesson_methodology:
            if course_info.lesson_methodology.lower() == "auto":
                # 自动匹配教学法
                try:
                    matched_methodology = teaching_methodology_agent.match_methodology(
                        course_info, user_habits, 
                        user_id=user_id, 
                        session_id=session_id
                    )
                
                    course_info.lesson_methodology = matched_methodology
                    # 将匹配结果添加到user_habits
                    if user_habits:
                        user_habits += f"\n用户要求使用教学法：{matched_methodology}"
                    else:
                        user_habits = f"用户要求使用教学法：{matched_methodology}"
                except Exception as e:
                    print(f"自动匹配教学法失败: {str(e)}")
            else:
                # 用户指定了特定教学法
                if user_habits:
                    user_habits += f"\n用户要求使用教学法：{course_info.lesson_methodology}"
                else:
                    user_habits = f"用户要求使用教学法：{course_info.lesson_methodology}"
                
        # 在处理完用户个人信息后，更新全局配置中的课程信息
        global lesson_plan_config
        lesson_plan_config.current_course_name = course_info.lesson_coursename
        lesson_plan_config.current_lesson_unitname = course_info.lesson_unitname
        
        # 主动初始化输出目录
        FileIO.reset_task_dir()  # 重置当前任务目录
        file_io = FileIO(lesson_plan_config)
        file_io._get_task_dir(lesson_plan_config)  # 创建新的任务目录
        
        # 更新状态
        state_update.update({
            "user_habits": user_habits,
            "course_info": course_info,
            "user_id": user_id,
            "session_id": session_id
        })
        
        # 根据lesson_pdf_already的值决定下一步
        if course_info.lesson_pdf_already == 3:
            # 无教材，直接跳转到生成教学大纲
            return Command(
                update=state_update,
                goto="SUB_05_generate_modify_teaching_process_outline"
            )
        elif course_info.lesson_pdf_already == 1:
            # 系统已有教材，跳转到读取预处理的教学大纲
            return Command(
                update=state_update,
                goto="read_teaching_process_outline"
            )
        else:
            # 用户自传教材，进入文件处理
            return Command(
                update=state_update,
                goto="process_file"
            )

def process_file(state: LessonPlanState) :
    """处理PDF文件并提取内容"""
    with manage_state_update("process_file", "文件处理") as state_update:
        # 重置任务目录
        FileIO.reset_task_dir()
        
        # 获取当前课程信息
        course_info = state["course_info"]
        
        # 处理用户自传的PDF文件
        if course_info.lesson_pdf_path:
            # 获取页面范围
            start_page = course_info.lesson_pdf_start_page
            end_page = course_info.lesson_pdf_end_page
            
            # 如果提供了页面范围，转换为整数
            if start_page is not None:
                start_page = int(start_page)
            if end_page is not None:
                end_page = int(end_page)
                
            # 处理PDF文件
            lesson_pdf_result = file_process_agent.process_pdf(
                course_info.lesson_pdf_path,
                start_page=start_page,
                end_page=end_page,
                lesson_pdf_already=course_info.lesson_pdf_already
            )
            
            # 更新课程信息中的PDF相关字段
            course_info.lesson_pdf_type = lesson_pdf_result["lesson_pdf_type"]
            course_info.lesson_pdf_content = lesson_pdf_result["lesson_pdf_content"]
            
            # 如果是视觉类型的PDF且是用户自传的，保存临时文件路径
            if "temp_pdf_path" in lesson_pdf_result and course_info.lesson_pdf_already == 2:
                course_info.lesson_pdf_temp_path = lesson_pdf_result["temp_pdf_path"]
        
        # 更新状态
        state_update.update({
            "course_info": course_info
        })
        
        return Command(
            update=state_update,
        )

def read_teaching_process_outline(state: LessonPlanState):
    """从OSS读取预处理的教学流程大纲"""
    with manage_state_update("read_teaching_process_outline", "读取预处理教学流程大纲") as state_update:
        # 获取课程信息
        course_info = state["course_info"]
        
        # 构建OSS路径
        stage = sanitize_filename(course_info.lesson_stage)
        coursename = sanitize_filename(course_info.lesson_coursename)
        version = sanitize_filename(course_info.lesson_version or "tongbian")  # 默认使用通编版本
        grade = sanitize_filename(course_info.lesson_grade)
        semester = sanitize_filename(course_info.lesson_semester)
        unitname = sanitize_filename(course_info.lesson_unitname)
        
        # 构建OSS对象名称
        # 格式: stage/coursename/version/grade/semester/result/stage_coursename_version_grade_semester_result_unitname.md
        object_name = f"{stage}/{coursename}/{version}/{grade}/{semester}/result/{stage}_{coursename}_{version}_{grade}_{semester}_result_{unitname}.md"
        
        
        # 创建OSS存储管理器
        oss_manager = OSSStorageManager(lesson_plan_config)
        
        try:
            # 检查文件是否存在
            if not oss_manager.check_file_exists(object_name):

                print(f"警告：系统中不存在教材预处理文件 {object_name}")
                # 构建内容文件路径
                # 格式: stage/coursename/version/grade/semester/content/stage_coursename_version_grade_semester_content_{page_no}.md
                content_base_path = f"{stage}/{coursename}/{version}/{grade}/{semester}/content/{stage}_{coursename}_{version}_{grade}_{semester}_content"
                
                # 获取页面范围
                start_page = course_info.lesson_pdf_start_page
                end_page = course_info.lesson_pdf_end_page
                
                # 如果提供了页面范围，转换为整数
                if start_page is not None:
                    start_page = int(start_page)
                else:
                    start_page = 1  # 默认从第1页开始
                    
                if end_page is not None:
                    end_page = int(end_page)
                
                # 尝试读取内容文件
                content_text = ""
                page_no = start_page
                # 将page_no补全至三位
                formatted_page_no = f"{page_no:03d}"
                content_object_name = f"{content_base_path}_{formatted_page_no}.md"
                
                # 检查第一个内容文件是否存在
                if not oss_manager.check_file_exists(content_object_name):
                    print(f"警告：系统中不存在教材内容文件 {content_object_name}")
                    # 设置为vision类型
                    course_info.lesson_pdf_type = "vision"
                    # 更新状态
                    state_update.update({
                        "course_info": course_info
                    })
                    return Command(
                        update=state_update,
                        goto="SUB_05_generate_modify_teaching_process_outline"
                    )
                
                # 读取所有内容文件并合并
                while oss_manager.check_file_exists(content_object_name):
                    try:
                        with oss_manager.get_temp_file(content_object_name, suffix='.md') as temp_path:
                            with open(temp_path, 'r', encoding='utf-8') as f:
                                content_text += f.read() + "\n\n"
                    except Exception as e:
                        print(f"警告：读取教材的内容文件 {content_object_name} 失败: {str(e)}")
                        course_info.lesson_pdf_content = content_text
                        if content_text.strip():
                            course_info.lesson_pdf_type = "text"  # 有文本内容，设置为文本类型
                        else:
                            course_info.lesson_pdf_type = "vision"  # 无文本内容，设置为视觉类型
                        
                        # 更新状态
                        state_update.update({
                            "course_info": course_info
                        })
                        
                        return Command(
                            update=state_update,
                            goto="SUB_05_generate_modify_teaching_process_outline"
                        )
                    
                    # 尝试下一个页面
                    page_no += 1
                    
                    # 如果设置了结束页码且已达到，则停止读取
                    if end_page is not None and page_no > end_page:
                        break
                        
                    content_object_name = f"{content_base_path}_{page_no}.md"
                
                # 更新课程信息中的PDF内容
                course_info.lesson_pdf_content = content_text
                # 根据内容是否为空来设置PDF处理类型
                if content_text.strip():
                    course_info.lesson_pdf_type = "text"  # 有文本内容，设置为文本类型
                    print(f"读取预处理教材的内容文件 {object_name} 有文本内容，设置为文本类型")
                else:
                    course_info.lesson_pdf_type = "vision"  # 无文本内容，设置为视觉类型
                    print(f"读取预处理教材的内容文件 {object_name} 无有效文本，设置为视觉类型")
                
                # 更新状态
                state_update.update({
                    "course_info": course_info
                })
                
                return Command(
                    update=state_update,
                    goto="SUB_05_generate_modify_teaching_process_outline"
                )
            
            # 创建临时文件并下载内容
            print(f"读取预处理教学流程大纲文件 {object_name}")
            with oss_manager.get_temp_file(object_name, suffix='.md') as temp_path:
                # 读取文件内容
                with open(temp_path, 'r', encoding='utf-8') as f:
                    outline_content = f.read()
                
                # 更新状态
                state_update.update({
                    "teaching_process_outline_preprocess": TeachingProcessOutlinePreprocess(content=outline_content)
                })
                
            return Command(
                update=state_update,
            )
        except Exception as e:
            print(f"警告：读取预处理教学流程大纲失败: {str(e)}，将重新生成教学大纲")
            return Command(
                update=state_update,
                goto="SUB_05_generate_modify_teaching_process_outline"
            )

def generate_modify_teaching_process_outline(state: LessonPlanState) :
    """生成或修改教学流程大纲"""
    with manage_state_update("SUB_05_generate_modify_teaching_process_outline", "教学流程大纲生成或修改") as state_update:
        # 获取课程信息
        course_info = state["course_info"]
        
        # 获取用户习惯
        user_habits = state.get("user_habits", "")
        
        # 检查是否有预处理的教学大纲
        teaching_process_outline_preprocess = state.get("teaching_process_outline_preprocess")
        if teaching_process_outline_preprocess and teaching_process_outline_preprocess.content:
            # 如果有预处理的教学大纲
            if not user_habits:
                # 如果user_habits为空，直接使用预处理的教学大纲
                output = teaching_process_outline_preprocess.content
            else:
                # 如果user_habits不为空，使用修改智能体对大纲进行修改
                output = teaching_process_outline_modify_agent.modify_outline(
                    course_info=course_info,
                    original_outline=teaching_process_outline_preprocess.content,
                    user_habits=user_habits,
                    user_id=state.get("user_id"),
                    session_id=state.get("session_id")
                )
        else:
            # 确保页面范围是整数类型
            if course_info.lesson_pdf_start_page is not None:
                course_info.lesson_pdf_start_page = int(course_info.lesson_pdf_start_page)
            if course_info.lesson_pdf_end_page is not None:
                course_info.lesson_pdf_end_page = int(course_info.lesson_pdf_end_page)
            
            # 根据PDF处理结果选择处理方式
            if course_info.lesson_pdf_type == "vision":
                output = teaching_process_outline_vision_agent.generate_outline(
                    course_info, user_habits,
                    user_id=state.get("user_id"),
                    session_id=state.get("session_id")
                )
            else:
                output = teaching_process_outline_txt_agent.generate_outline(
                    course_info, user_habits,
                    user_id=state.get("user_id"),
                    session_id=state.get("session_id")
                )
        
        # 计算教学流程数量
        sections = teaching_process_expand_agent.parse_sections(output)
        total_sections = len(sections)
        formatted_total = f"{total_sections:02d}"  # 转换为两位数格式
        
        # 更新大纲和状态
        state_update.update({
            "teaching_process_outline": TeachingProcessOutline(content=output),
            "router": Router(
                stage="SUB_05_generate_modify_teaching_process_outline",
                status=f"SUB_05_{formatted_total}_教学流程大纲生成成功且需进行展开"
            )
        })
        
        return Command(
            update=state_update,
        )
    
def generate_teaching_objectives(state: LessonPlanState) -> Dict[str, Any]:
    """生成教学目标"""
    with manage_state_update("STR_00_generate_teaching_objectives", "生成教学目标") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        objectives_content = teaching_elements_generate_agent.generate_objectives(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        state_update.update({
            "teaching_objectives": objectives_content,
            "router": Router(
                stage="STR_00_generate_teaching_objectives",
                status="FIN_00_生成教学目标成功"
            )
        })
    return state_update

def generate_teaching_keypoints(state: LessonPlanState) -> Dict[str, Any]:
    """生成教学重点难点"""
    with manage_state_update("STR_01_generate_teaching_keypoints", "生成教学重点难点") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        keypoints_content = teaching_elements_generate_agent.generate_keypoints(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        state_update.update({
            "teaching_keypoints": keypoints_content,
            "router": Router(
                stage="STR_01_generate_teaching_keypoints",
                status="FIN_01_生成教学重点难点成功"
            )
        })
    return state_update

def generate_teaching_studentanalysis(state: LessonPlanState) -> Dict[str, Any]:
    """生成学情分析"""
    with manage_state_update("STR_02_generate_teaching_studentanalysis", "生成学情分析") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        studentanalysis_content = teaching_elements_generate_agent.generate_studentanalysis(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        state_update.update({
            "teaching_studentanalysis": studentanalysis_content,
            "router": Router(
                stage="STR_02_generate_teaching_studentanalysis",
                status="FIN_02_生成学情分析成功"
            )
        })
    return state_update

def generate_teaching_methods(state: LessonPlanState) -> Dict[str, Any]:
    """生成教学方法"""
    with manage_state_update("STR_03_generate_teaching_methods", "生成教学方法") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        methods_content = teaching_elements_generate_agent.generate_methods(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )

        state_update.update({
            "teaching_methods": methods_content,
            "router": Router(
                stage="STR_03_generate_teaching_methods",
                status="FIN_03_生成教学方法成功"
            )
        })
    return state_update

def generate_teaching_activities(state: LessonPlanState) -> Dict[str, Any]:
    """生成教学活动"""
    with manage_state_update("STR_04_generate_teaching_activities", "生成教学活动") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        activities_content = teaching_elements_generate_agent.generate_activities(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )

        state_update.update({
            "teaching_activities": activities_content,
            "router": Router(
                stage="STR_04_generate_teaching_activities",
                status="FIN_04_生成教学活动成功"
            )
        })
    return state_update

def generate_teaching_boarddesign(state: LessonPlanState) -> Dict[str, Any]:
    """生成教学板书设计"""
    with manage_state_update("STR_06_generate_teaching_boarddesign", "生成教学板书设计") as state_update:
        course_info = state["course_info"]
        lesson_plan = state["teaching_process_outline"].content
        boarddesign_content = teaching_elements_generate_agent.generate_boarddesign(
            course_info, lesson_plan,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        state_update.update({
            "teaching_boarddesign": boarddesign_content,
            "router": Router(
                stage="STR_06_generate_teaching_boarddesign",
                status="FIN_06_生成教学板书设计成功"
            )
        })
    return state_update

def expand_teaching_process_outline(state: LessonPlanState) -> Dict[str, Any]:
    """展开教学流程中的单个部分"""
    with manage_state_update("STR_05_generate_teaching_processes", "展开教学流程") as state_update:
        # 获取教学流程大纲和课程信息
        course_info = state["course_info"]
        outline_content = state["teaching_process_outline"].content
        
        # 获取当前需要处理的部分
        current_section = state.get("current_section")
        if not current_section:
            raise ValueError("未提供要展开的部分信息")
        
        process_id = current_section.process_id
        process_title = current_section.process_title
        
        # 展开该部分
        content = teaching_process_expand_agent.expand_section(
            process_id, process_title, course_info, outline_content,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 从process_id中提取数字部分
        process_number = int(''.join(filter(str.isdigit, process_id)))
        formatted_number = f"{process_number:02d}" 
        
        # 添加到结果中并更新状态
        state_update.update({
            "teaching_processes": {
                process_id: TeachingProcessInfo(
                    process_id=process_id,
                    process_title=process_title,
                    content=content
                )
            },
            "router": Router(
                stage="STR_05_generate_teaching_processes",
                status=f"FIN_05_{formatted_number}_展开及生成教学流程成功"
            )
        })
    
    return state_update

def send_expand_task(state: Dict[str, Any]) :
    """根据状态决定下一步任务"""
    # 获取课程信息和大纲内容
    course_info = state["course_info"]
    teaching_process_outline = state["teaching_process_outline"]
    user_habits = state.get("user_habits", "")
    
    # 准备共享状态
    shared_state = {
        "course_info": course_info,
        "teaching_process_outline": teaching_process_outline,
        "user_habits": user_habits,
        "user_id": state.get("user_id"),
        "session_id": state.get("session_id")
    }
    
    # 从大纲中提取章节
    sections = teaching_process_expand_agent.parse_sections(teaching_process_outline.content)
    
    # 启动所有并行任务
    tasks = [
        Send("STR_00_generate_teaching_objectives", shared_state),
        Send("STR_01_generate_teaching_keypoints", shared_state),
        Send("STR_02_generate_teaching_studentanalysis", shared_state),
        Send("STR_03_generate_teaching_methods", shared_state),
        Send("STR_04_generate_teaching_activities", shared_state),
        Send("STR_06_generate_teaching_boarddesign", shared_state)
    ]
    
    # 为每个部分创建单独的任务
    for section in sections:
        process_id = section["id"]
        process_title = section["title"]
        
        # 创建包含当前部分信息的状态
        section_state = shared_state.copy()
        section_state["current_section"] = TeachingProcessInfo(
            process_id=process_id,
            process_title=process_title
        )
        
        # 添加到任务列表
        tasks.append(Send("STR_05_generate_teaching_processes", section_state))
    
    return tasks

def merge_sections(state: Dict[str, Any]) :
    """合并各部分内容"""
    with manage_state_update("merge_teachingplan_sections", "教学内容合并") as state_update:
        # 获取所有内容并合并
        final_plan = lessonplan_sections_merge_agent.merge_sections(
            teaching_sections=state.get("teaching_processes", {}),
            teaching_objectives=state.get("teaching_objectives", ""),
            teaching_keypoints=state.get("teaching_keypoints", ""),
            teaching_studentanalysis=state.get("teaching_studentanalysis", ""),
            teaching_methods=state.get("teaching_methods", ""),
            teaching_activities=state.get("teaching_activities", ""),
            teaching_boarddesign=state.get("teaching_boarddesign", "")
        )
        
        # 更新状态
        state_update.update({
            "final_plan": final_plan,
            "router": Router(
                stage="merge_teachingplan_sections",
                status="FIN_ALL_内容全部生成且合并成功"
            )
        })

        return Command(
            update=state_update,
        )

def wait_custom_feedback(state: LessonPlanState) -> Command[Literal["add_custom_element", "modify_generated_element", "generate_mindmap", "generate_transcript", "generate_plananalysis", END]]:
    """等待用户自定义反馈"""
    with manage_state_update("wait_custom_feedback", "用户反馈等待") as state_update:
        response = interrupt("请选择操作:\n0. 完成\n1. 修改已生成的要素\n2. 添加新的要素\n3. 创建思维导图\n4. 创建逐字稿\n5. 创建教案分析\n请输入选项(0/1/2/3/4/5):")
        choice = response.get("confirm", "").strip()
        
        state_update["wants_modify_element"] = choice == "1"
        state_update["wants_add_element"] = choice == "2"
        state_update["wants_generate_mindmap"] = choice == "3"
        state_update["wants_generate_transcript"] = choice == "4"
        state_update["wants_generate_plananalysis"] = choice == "5"
        
        if choice == "0":
            goto = END
        elif choice == "1":
            goto = "modify_generated_element"
        elif choice == "3":
            goto = "generate_mindmap"
        elif choice == "4":
            goto = "generate_transcript"
        elif choice == "5":
            goto = "generate_plananalysis"
        else:
            goto = "add_custom_element"
        
        return Command(
            update=state_update,
            goto=goto
        )

def add_custom_element(state: LessonPlanState):
    """添加自定义元素"""
    with manage_state_update("add_custom_element", "自定义要素添加") as state_update:
        # 获取要素名称
        response = interrupt("请输入教案要素名称(如'教学准备'、'课后作业'等):")
        element_name = response.get("element_name", "")  # 使用element_name字段表示要素名称
        
        if not element_name:
            raise ValueError("要素名称不能为空")
        
        # 验证要素名称不重复
        custom_elements = state.get("custom_elements", {})
        if element_name in custom_elements:
            raise ValueError(f"要素'{element_name}'已存在")
        
        # 使用全局agent生成要素内容
        element_content = element_customize_agent.generate_element(
            course_info=state["course_info"],
            element_name=element_name,
            final_plan=state.get("final_plan", ""),
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
            
        # 更新自定义要素
        custom_elements[element_name] = element_content
        state_update["custom_elements"] = custom_elements
            
        return Command(
            update=state_update
        )

def modify_generated_element(state: LessonPlanState):
    """修改生成的元素"""
    with manage_state_update("modify_generated_element", "修改已生成要素") as state_update:
        # 获取用户输入
        response = interrupt("请输入要修改的要素类型和修改要求:")
        
        # 从response中获取所有需要的字段
        element_type = response.get("element_type", "").strip()
        modify_requirements = response.get("requirements", "").strip()
        process_id = response.get("process_id", "").strip()
        element_name = response.get("element_name", "").strip()

        if not element_type:
            raise ValueError("要素类型不能为空")
        if not modify_requirements:
            raise ValueError("修改要求不能为空")

        # 处理教学流程和自定义要素的特殊情况
        if element_type == "teaching_processes":
            if not process_id or process_id not in state.get("teaching_processes", {}):
                raise ValueError("无效的流程ID")
            element_name = process_id
            original_content = state["teaching_processes"][element_name].content
            
        elif element_type == "custom_elements":
            if not element_name or element_name not in state.get("custom_elements", {}):
                raise ValueError("无效的要素名称")
            original_content = state["custom_elements"][element_name]
            
        else:
            # 验证是否是有效的基本要素类型
            valid_basic_types = {"teaching_objectives", "teaching_keypoints", "teaching_methods", "teaching_activities"}
            if element_type not in valid_basic_types:
                raise ValueError(f"无效的要素类型: {element_type}")
            
            element_name = element_type
            original_content = state.get(element_type)
            if not original_content:
                raise ValueError(f"没有找到{element_type}的内容")

        # 调用修改智能体
        modified_content = element_modify_agent.modify_element(
            course_info=state["course_info"],
            element_type=element_type,
            element_name=element_name,
            original_content=original_content,
            modify_requirements=modify_requirements,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )

        # 验证修改后的内容不为空
        if not modified_content:
            raise ValueError("修改后的内容不能为空")
        
        # 根据要素类型更新状态
        if element_type == "teaching_processes":
            # 创建新的TeachingProcessInfo对象
            new_process_info = TeachingProcessInfo(
                process_id=element_name,
                process_title=state["teaching_processes"][element_name].process_title,
                content=modified_content
            )
            # 复制原有字典并更新特定流程
            updated_process_detail = dict(state["teaching_processes"])
            updated_process_detail[element_name] = new_process_info
            state_update["teaching_processes"] = updated_process_detail
            
        elif element_type == "custom_elements":
            # 复制原有字典并更新特定要素
            updated_custom_elements = dict(state.get("custom_elements", {}))
            updated_custom_elements[element_name] = modified_content
            state_update["custom_elements"] = updated_custom_elements
            
        else:
            # 对于基本要素，直接更新内容
            state_update[element_type] = modified_content
        
        return Command(
            update=state_update
        )

def generate_mindmap(state: LessonPlanState):
    """生成思维导图"""
    with manage_state_update("generate_mindmap", "思维导图生成") as state_update:
        # 获取教学流程信息
        teaching_processes = state.get("teaching_processes", {})
        
        # 验证是否有足够的教学流程信息
        if not teaching_processes:
            raise ValueError("缺少教学流程信息，无法生成思维导图")
        
        # 使用思维导图生成智能体生成思维导图
        mindmap_content = mindmap_generate_agent.generate_mindmap(
            teaching_processes,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 更新状态
        state_update.update({
            "mindmap_content": mindmap_content,
            "router": Router(
                stage="generate_mindmap",
                status="FIN_ALL_思维导图生成成功"
            )
        })
        
        return Command(
            update=state_update
        )

def generate_transcript(state: LessonPlanState):
    """生成教学逐字稿"""
    with manage_state_update("generate_transcript", "教学逐字稿生成") as state_update:
        # 获取教学流程信息
        teaching_processes = state.get("teaching_processes", {})
        
        # 验证是否有足够的教学流程信息
        if not teaching_processes:
            raise ValueError("缺少教学流程信息，无法生成教学逐字稿")
        
        # 获取课程信息
        course_info = state["course_info"]
        
        # 使用逐字稿生成智能体生成完整逐字稿
        transcript_content = transcript_generate_agent.generate_full_transcript(
            course_info=course_info,
            teaching_processes=teaching_processes,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 更新状态
        state_update.update({
            "transcript_content": transcript_content,
            "router": Router(
                stage="generate_transcript",
                status="FIN_ALL_教学逐字稿生成成功"
            )
        })
        
        return Command(
            update=state_update
        )

def generate_plananalysis(state: LessonPlanState):
    """生成教案分析"""
    with manage_state_update("generate_plananalysis", "教案分析生成") as state_update:
        # 获取教学流程信息
        teaching_processes = state.get("teaching_processes", {})
        
        # 验证是否有足够的教学流程信息
        if not teaching_processes:
            raise ValueError("缺少教学流程信息，无法生成教学逐字稿")
        
        # 获取课程信息
        course_info = state["course_info"]
        
        # 获取用户习惯
        user_habits = state.get("user_habits", "")
        
        # 使用教案分析生成智能体生成分析
        plananalysis_content = plananalysis_generate_agent.generate_plananalysis(
            course_info=course_info,
            teaching_processes=teaching_processes,
            user_habits=user_habits,
            user_id=state.get("user_id"),
            session_id=state.get("session_id")
        )
        
        # 更新状态
        state_update.update({
            "plananalysis_content": plananalysis_content,
            "router": Router(
                stage="generate_plananalysis",
                status="FIN_ALL_教案分析生成成功"
            )
        })
        
        return Command(
            update=state_update
        )
"""教学逐字稿生成器核心服务模块

包含所有核心业务逻辑的服务类，实现教学流程生成、逐字稿生成和内容合并等功能。
"""

import asyncio
import re
import time
import os
import json
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from contextlib import asynccontextmanager

from src.models import (
    TranscriptRequest,
    TranscriptResponse,
    TeachingSection,
    ProgressUpdate,
    ModelUsageInfo,
    ErrorInfo,
    TeachingFlow,
    SectionParseResult,
    AIClientConfig
)
from src.prompts import (
    TEACHING_PROCESS_TXT_SYSTEM_PROMPT,
    TRANSCRIPT_SECTION_SYSTEM_PROMPT,
    format_course_info
)
from src.ai_client import AIClientFactory, BaseAIClient
from src.config import TranscriptGeneratorConfig
from src.logger import get_logger


@asynccontextmanager
async def handle_service_errors(stage: str, ctx=None):
    """服务错误处理上下文管理器"""
    try:
        yield
    except Exception as e:
        error_info = ErrorInfo(
            error_type=type(e).__name__,
            error_message=f"{stage}阶段发生错误",
            error_details=str(e),
            stage=stage,
            retry_suggested=True
        )

        if ctx:
            await ctx.error(error_info.model_dump())

        # 使用全局logger记录错误，因为这是在上下文管理器中
        import logging
        logging.getLogger("TranscriptGeneratorService").error(f"服务错误 [{stage}]: {error_info.error_message}", exc_info=True)
        raise


class TeachingFlowService:
    """教学流程生成服务

    负责根据课程信息生成结构化的教学流程大纲，包括各个教学环节的
    标题、内容要点和预计时长。

    Attributes:
        config: 服务配置对象
        logger: 日志记录器
        ai_client: AI客户端实例
    """

    def __init__(self, config: TranscriptGeneratorConfig):
        """初始化教学流程生成服务

        Args:
            config: 服务配置对象，包含AI模型配置等信息
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.ai_client = self._create_ai_client()
    
    def _create_ai_client(self) -> BaseAIClient:
        """创建AI客户端实例

        根据配置创建相应的AI客户端，支持测试模式。

        Returns:
            BaseAIClient: 配置好的AI客户端实例
        """
        ai_config = AIClientConfig(
            provider=self.config.ai_provider,
            api_key=self.config.ai_api_key,
            model_name=self.config.ai_model_name,
            base_url=self.config.ai_base_url,
            timeout=self.config.ai_timeout,
            max_retries=self.config.ai_max_retries
        )
        

        
        return AIClientFactory.create_client(ai_config)
    
    async def generate_teaching_flow(
        self, 
        request: TranscriptRequest, 
        ctx=None
    ) -> Tuple[TeachingFlow, List[SectionParseResult]]:
        """
        生成教学流程大纲
        
        Args:
            request: 逐字稿生成请求
            ctx: MCP上下文对象
            
        Returns:
            Tuple[TeachingFlow, List[SectionParseResult]]: (教学流程, 环节列表)
        """
        async with handle_service_errors("教学流程生成", ctx):
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="flow_generation_started",
                    message="开始分析课程信息，生成教学流程大纲",
                    progress=5.0
                ).model_dump())
            
            # 格式化课程信息
            course_info_formatted = format_course_info(
                request.course_basic_info,
                request.teaching_info,
                request.personal_requirements
            )
            
            # 准备提示词
            prompt = TEACHING_PROCESS_TXT_SYSTEM_PROMPT.format(
                course_info_formatted=course_info_formatted
            )
            
            self.logger.info(f"[进度] flow_generation_started: 开始生成教学流程 - 课程: {request.course_basic_info}")

            # 调用AI生成教学流程
            flow_content, usage_info = await self.ai_client.generate_text(prompt)
            
            # 报告用量
            if ctx:
                await ctx.info({
                    "status": "model_usage",
                    "usage": usage_info.model_dump(),
                    "stage": "teaching_flow_generation"
                })
            
            # 验证教学流程格式（只检查中文数字开头的一级标题）
            section_titles = re.findall(r'# ([一二三四五六七八九十].*)', flow_content)
            if len(section_titles) < 3:
                raise ValueError("教学环节数量不足，至少需要3个环节")
            if len(section_titles) > 8:
                raise ValueError("教学环节数量过多，最多支持8个环节")

            self.logger.info(f"教学流程验证通过 - 检测到{len(section_titles)}个环节")
            
            # 解析教学流程为环节列表
            sections = self._parse_teaching_flow(flow_content)
            
            # 创建教学流程对象
            teaching_flow = TeachingFlow(
                content=flow_content,
                sections_count=len(sections)
            )
            
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="flow_generation_completed",
                    message=f"教学流程生成完成，共设计{len(sections)}个教学环节",
                    progress=30.0
                ).model_dump())
            
            self.logger.info(f"[进度] flow_generation_completed: 教学流程生成成功 - 环节数: {len(sections)}, "
                       f"用量: {usage_info.total_tokens} tokens")
            
            return teaching_flow, sections
    
    def _parse_teaching_flow(self, flow_content: str) -> List[SectionParseResult]:
        """
        解析教学流程内容，提取各个环节

        Args:
            flow_content: 教学流程内容

        Returns:
            List[SectionParseResult]: 环节列表
        """
        sections = []

        # 使用与原项目相同的正则表达式，只匹配中文数字开头的一级标题
        pattern = r'# ([一二三四五六七八九十].*?)\n(.*?)(?=\n# [一二三四五六七八九十]|$)'
        matches = re.findall(pattern, flow_content, re.DOTALL)

        for i, (title, content) in enumerate(matches):
            section_id = f"process_{i+1:02d}"  # process_01, process_02...

            # 清理内容
            cleaned_content = content.strip()

            # 从标题中提取时长信息
            duration = self._extract_duration_from_title(title)

            sections.append(SectionParseResult(
                section_id=section_id,
                title=title.strip(),
                content=cleaned_content,
                duration_minutes=duration
            ))

        self.logger.info(f"解析教学流程完成 - 共{len(sections)}个环节")
        return sections

    def _extract_duration_from_title(self, title: str) -> Optional[int]:
        """从标题中提取时长信息

        Args:
            title: 环节标题，如"一、课程导入（5分钟）"

        Returns:
            Optional[int]: 提取的分钟数，如果没有找到则返回None
        """
        # 匹配括号中的数字+分钟
        pattern = r'[（(](\d+)分钟[）)]'
        match = re.search(pattern, title)
        if match:
            return int(match.group(1))
        return None


class TranscriptSectionService:
    """逐字稿环节生成服务

    负责为每个教学环节生成详细的逐字稿内容，支持流式生成和并行处理。

    Attributes:
        config: 服务配置对象
        logger: 日志记录器
        ai_client: AI客户端实例
    """

    def __init__(self, config: TranscriptGeneratorConfig):
        """初始化逐字稿环节生成服务

        Args:
            config: 服务配置对象，包含AI模型配置等信息
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.ai_client = self._create_ai_client()
    
    def _create_ai_client(self) -> BaseAIClient:
        """创建AI客户端"""
        ai_config = AIClientConfig(
            provider=self.config.ai_provider,
            api_key=self.config.ai_api_key,
            model_name=self.config.ai_model_name,
            base_url=self.config.ai_base_url,
            timeout=self.config.ai_timeout,
            max_retries=self.config.ai_max_retries
        )
        

        
        return AIClientFactory.create_client(ai_config)
    
    async def generate_section_transcript(
        self,
        request: TranscriptRequest,
        teaching_flow: TeachingFlow,
        section: SectionParseResult,
        ctx=None
    ) -> str:
        """生成单个环节的逐字稿"""
        
        async with handle_service_errors("环节逐字稿生成", ctx):
            section_id = section.section_id
            
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="section_generation_started",
                    message=f"开始生成环节逐字稿: {section.title}",
                    section_id=section_id
                ).model_dump())
            
            # 准备提示词
            prompt = TRANSCRIPT_SECTION_SYSTEM_PROMPT.format(
                course_basic_info=request.course_basic_info,
                teaching_process=teaching_flow.content,
                process_title=section.title
            )
            
            self.logger.info(f"[进度] section_generation_started: 开始生成环节逐字稿 - {section_id}: {section.title}")

            # 流式生成逐字稿
            transcript_chunks = []
            total_chunks = 0
            
            async for chunk, usage_info in self.ai_client.stream_generate_text(prompt):
                if chunk:  # 流式内容
                    transcript_chunks.append(chunk)
                    total_chunks += 1
                    
                    # 实时返回内容片段
                    if ctx:
                        await ctx.info(ProgressUpdate(
                            status="section_streaming",
                            message=f"正在生成逐字稿内容... ({total_chunks}个片段)",
                            section_id=section_id,
                            chunk=chunk
                        ).model_dump())
                
                elif usage_info:  # 用量信息
                    # 报告用量
                    if ctx:
                        await ctx.info({
                            "status": "model_usage",
                            "usage": usage_info.model_dump(),
                            "stage": f"section_generation_{section_id}"
                        })
            
            transcript = "".join(transcript_chunks)
            
            # 简单的逐字稿质量检查
            if len(transcript) < 100:
                self.logger.warning(f"逐字稿内容过短 [{section_id}]: {len(transcript)}字")
            elif "老师：" not in transcript:
                self.logger.warning(f"逐字稿缺少教师对话 [{section_id}]")
            
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="section_generation_completed",
                    message=f"环节逐字稿生成完成: {section.title} ({len(transcript)}字)",
                    section_id=section_id
                ).model_dump())
            
            self.logger.info(f"[进度] section_generation_completed: 环节逐字稿生成成功 - {section_id}: {len(transcript)}字")
            
            return transcript
    
    async def generate_all_sections_parallel(
        self,
        request: TranscriptRequest,
        teaching_flow: TeachingFlow,
        sections: List[SectionParseResult],
        ctx=None
    ) -> List[TeachingSection]:
        """并行生成所有环节的逐字稿"""
        
        async with handle_service_errors("并行逐字稿生成", ctx):
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="parallel_generation_started",
                    message=f"开始并行生成{len(sections)}个环节的逐字稿",
                    progress=40.0
                ).model_dump())
            
            # 限制并行数量
            semaphore = asyncio.Semaphore(self.config.parallel_limit)
            
            async def generate_with_semaphore(section: SectionParseResult) -> TeachingSection:
                async with semaphore:
                    transcript = await self.generate_section_transcript(
                        request, teaching_flow, section, ctx
                    )
                    
                    return TeachingSection(
                        section_id=section.section_id,
                        title=section.title,
                        content=section.content,
                        transcript=transcript,
                        duration_minutes=section.duration_minutes  # 使用教学流程中的预计时间
                    )
            
            # 并行执行所有任务
            tasks = [generate_with_semaphore(section) for section in sections]
            teaching_sections = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            successful_sections = []
            for i, result in enumerate(teaching_sections):
                if isinstance(result, Exception):
                    self.logger.error(f"环节生成失败 [{sections[i].section_id}]: {str(result)}")
                    if ctx:
                        await ctx.error({
                            "type": "section_generation_error",
                            "message": f"环节生成失败: {sections[i].title}",
                            "details": str(result),
                            "section_id": sections[i].section_id
                        })
                else:
                    successful_sections.append(result)
            
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="parallel_generation_completed",
                    message=f"并行生成完成，成功生成{len(successful_sections)}个环节",
                    progress=80.0
                ).model_dump())
            
            self.logger.info(f"并行逐字稿生成完成 - 成功: {len(successful_sections)}, "
                       f"失败: {len(sections) - len(successful_sections)}")
            
            return successful_sections
    



class TranscriptMergeService:
    """逐字稿合并服务

    负责将各个教学环节的逐字稿合并成完整的教学文档，
    包括添加标题、格式化和质量检查。

    Attributes:
        config: 服务配置对象
        logger: 日志记录器
    """

    def __init__(self, config: TranscriptGeneratorConfig):
        """初始化逐字稿合并服务

        Args:
            config: 服务配置对象
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)

    async def merge_transcripts(
        self,
        teaching_sections: List[TeachingSection],
        ctx=None
    ) -> str:
        """合并所有环节的逐字稿"""

        async with handle_service_errors("逐字稿合并", ctx):
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="merge_started",
                    message="正在合并各环节逐字稿，生成完整文档",
                    progress=85.0
                ).model_dump())

            if not teaching_sections:
                raise ValueError("没有可合并的逐字稿环节")

            # 按section_id排序
            sorted_sections = sorted(
                teaching_sections,
                key=lambda x: int(x.section_id.split('_')[1])
            )

            # 构建完整逐字稿
            merged_parts = []

            # 添加标题
            merged_parts.append("# 教学逐字稿\n")

            # 添加课程信息摘要（如果有的话）
            total_duration = sum(s.duration_minutes or 0 for s in sorted_sections)
            merged_parts.append(f"**预计总时长**: {total_duration}分钟\n")
            merged_parts.append(f"**教学环节**: {len(sorted_sections)}个\n\n")

            # 合并各环节逐字稿
            for section in sorted_sections:
                if section.transcript:
                    # 确保逐字稿以环节标题开头
                    transcript = section.transcript.strip()
                    if not transcript.startswith('#'):
                        transcript = f"# {section.title}\n\n{transcript}"

                    merged_parts.append(transcript)
                    merged_parts.append("\n\n---\n\n")  # 环节分隔符

            # 移除最后一个分隔符
            if merged_parts and merged_parts[-1] == "\n\n---\n\n":
                merged_parts.pop()

            final_transcript = "".join(merged_parts)

            if ctx:
                await ctx.info(ProgressUpdate(
                    status="merge_completed",
                    message=f"逐字稿合并完成，总长度{len(final_transcript)}字",
                    progress=95.0
                ).model_dump())

            self.logger.info(f"逐字稿合并完成 - 环节数: {len(sorted_sections)}, "
                       f"总长度: {len(final_transcript)}字")

            return final_transcript


class TranscriptGeneratorService:
    """教学逐字稿生成主服务

    这是教学逐字稿生成的主要服务类，协调各个子服务完成完整的
    逐字稿生成流程，包括教学流程设计、环节逐字稿生成、内容合并
    和文件保存等功能。

    主要功能：
    - 根据课程信息生成教学流程大纲
    - 为每个教学环节生成详细逐字稿
    - 合并各环节内容形成完整文档
    - 保存逐字稿文件和元数据
    - 提供实时进度反馈

    Attributes:
        config: 服务配置对象
        logger: 日志记录器
        teaching_flow_service: 教学流程生成服务
        transcript_section_service: 逐字稿环节生成服务
        transcript_merge_service: 逐字稿合并服务
    """

    def __init__(self, config: TranscriptGeneratorConfig):
        """初始化教学逐字稿生成主服务

        Args:
            config: 服务配置对象，包含AI模型配置、输出目录等信息
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.teaching_flow_service = TeachingFlowService(config)
        self.transcript_section_service = TranscriptSectionService(config)
        self.transcript_merge_service = TranscriptMergeService(config)
        self.logger.info("教学逐字稿生成服务已初始化")

    def _create_output_directory(self, request: TranscriptRequest) -> str:
        """创建输出目录"""
        # 生成安全的文件名
        safe_title = re.sub(r'[^\w\s-]', '', request.course_basic_info)
        safe_title = re.sub(r'[-\s]+', '-', safe_title)[:50]

        # 创建带时间戳的目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        transcript_dir = os.path.join(self.config.output_dir, f"{safe_title}_{timestamp}")

        os.makedirs(transcript_dir, exist_ok=True)
        self.logger.info(f"创建输出目录: {transcript_dir}")

        return transcript_dir

    def _save_transcript_files(
        self,
        transcript_dir: str,
        request: TranscriptRequest,
        response: TranscriptResponse,
        final_transcript: str,
        teaching_flow: 'TeachingFlow',
        teaching_sections: List['TeachingSection']
    ):
        """保存逐字稿文件和元数据"""

        # 保存逐字稿markdown文件
        transcript_file = os.path.join(transcript_dir, "transcript.md")
        with open(transcript_file, 'w', encoding='utf-8') as f:
            f.write(final_transcript)
        self.logger.info(f"逐字稿已保存至: {transcript_file}")

        # 构建教学流程信息
        from .models import TeachingProcess, TeachingProcessInfo

        teaching_process = TeachingProcess(
            total_sections=len(teaching_sections),
            total_duration=sum(s.duration_minutes or 0 for s in teaching_sections),
            flow_content=teaching_flow.content,
            sections_summary=[s.title for s in teaching_sections]
        )

        # 构建各环节详细信息
        teaching_processes = {}
        for section in teaching_sections:
            teaching_processes[section.section_id] = TeachingProcessInfo(
                section_id=section.section_id,
                title=section.title,
                content=section.content,
                duration_minutes=section.duration_minutes,
                transcript_length=len(section.transcript) if section.transcript else 0
            ).model_dump()

        # 保存元数据
        metadata = {
            "request_info": {
                "course_basic_info": request.course_basic_info,
                "teaching_info": request.teaching_info,
                "personal_requirements": request.personal_requirements
            },
            "generation_info": {
                "timestamp": datetime.now().isoformat(),
                "sections_count": response.total_sections,
                "transcript_length": len(final_transcript)
            },
            "teaching_process": teaching_process.model_dump(),
            "teaching_processes": teaching_processes,
            "transcript_content": final_transcript
        }

        metadata_file = os.path.join(transcript_dir, "metadata.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        self.logger.info(f"元数据已保存至: {metadata_file}")

    async def generate_transcript(
        self,
        request: TranscriptRequest,
        ctx=None
    ) -> TranscriptResponse:
        """生成完整教学逐字稿的主流程"""

        start_time = time.time()

        async with handle_service_errors("教学逐字稿生成", ctx):
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="started",
                    message="教学逐字稿生成任务开始",
                    progress=0.0
                ).model_dump())

            self.logger.info(f"[进度] transcript_generation_started: 开始生成教学逐字稿 - 课程: {request.course_basic_info}")

            # 1. 生成教学流程
            teaching_flow, sections = await self.teaching_flow_service.generate_teaching_flow(
                request, ctx
            )

            # 2. 并行生成各环节逐字稿
            teaching_sections = await self.transcript_section_service.generate_all_sections_parallel(
                request, teaching_flow, sections, ctx
            )

            # 3. 合并最终逐字稿
            final_transcript = await self.transcript_merge_service.merge_transcripts(
                teaching_sections, ctx
            )

            # 4. 构建响应
            generation_time = time.time() - start_time
            total_duration = sum(s.duration_minutes or 0 for s in teaching_sections)

            response = TranscriptResponse(
                final_transcript=final_transcript,
                teaching_sections=teaching_sections,
                total_sections=len(teaching_sections),
                estimated_duration=total_duration,
                generation_time=generation_time
            )

            if ctx:
                await ctx.info(ProgressUpdate(
                    status="completed",
                    message=f"教学逐字稿生成完成！共{len(teaching_sections)}个环节，"
                           f"预计时长{total_duration}分钟，耗时{generation_time:.1f}秒",
                    progress=100.0
                ).model_dump())

            self.logger.info(f"[进度] transcript_generation_completed: 教学逐字稿生成完成 - 环节数: {len(teaching_sections)}, "
                       f"总时长: {total_duration}分钟, 耗时: {generation_time:.2f}秒")

            # 保存逐字稿文件
            if ctx:
                await ctx.info(ProgressUpdate(
                    status="saving_files",
                    message="正在保存逐字稿文件和元数据",
                    progress=98.0
                ).model_dump())

            transcript_dir = self._create_output_directory(request)
            self._save_transcript_files(transcript_dir, request, response, final_transcript, teaching_flow, teaching_sections)

            # 更新响应中的文件路径信息
            response.output_directory = transcript_dir

            return response



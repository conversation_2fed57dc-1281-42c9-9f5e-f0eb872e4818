# MCP (Model Context Protocol) 项目集合

这是一个基于 **MCP (Model Context Protocol)** 的AI应用开发项目集合，专注于构建现代化的AI工具和服务，利用标准化的协议接口为各种AI助手提供强大的功能扩展。

## 🎯 项目概述

MCP是一个用于AI应用和工具之间标准化通信的协议框架。本项目集合展示了如何使用MCP构建高质量的AI服务，重点关注深度学习、计算机视觉和自然语言处理应用。

### 核心技术栈

- **协议框架**: MCP (Model Context Protocol) 1.2.0+
- **深度学习**: PyTorch + Transformers + Diffusers
- **AI服务**: 豆包API (火山引擎)
- **Web框架**: FastMCP + HTTP Streamable Transport
- **数据验证**: Pydantic 2.0+
- **环境管理**: Python 3.10-3.12

## 📁 项目结构

```
MCPSERVER/
├── README.md                    # 主项目说明文档
├── picturebook-generator-mcp/   # AI绘本生成MCP服务器
└── [未来项目...]               # 更多MCP服务器项目
```

## 🚀 当前子项目

### 1. AI绘本生成MCP服务器 (`picturebook-generator-mcp/`)

一个专业的AI绘本生成服务，能够根据用户需求自动创作完整的图文绘本。

**核心功能**:
- 🎨 **智能故事创作**: 使用大语言模型生成适合儿童的故事内容
- 🖼️ **精美图片生成**: 为每个故事页面生成对应的插画
- 🎭 **多样化风格**: 支持卡通、水彩、油画、简笔画等多种艺术风格
- 📚 **完整绘本输出**: 生成包含元数据的完整绘本项目


详细文档请查看: [picturebook-generator-mcp/README.md](./picturebook-generator-mcp/README.md)

## 🛠️ 开发指南

### 环境要求

- **Python**: 3.10-3.12
- **操作系统**: Windows 10/11, macOS, Linux
- **内存**: 建议8GB以上
- **网络**: 需要稳定的互联网连接（访问AI服务API）

### 通用依赖

所有子项目共享的核心依赖:

```toml
[dependencies]
mcp = ">=1.2.0"
pydantic = ">=2.0.0"
python-dotenv = ">=1.0.0"
httpx = ">=0.24.0"
```

### 开发最佳实践

1. **遵循MCP标准**: 所有工具函数都应符合MCP协议规范
2. **类型安全**: 使用Pydantic进行数据验证和类型检查
3. **错误处理**: 实现完整的异常处理和用户友好的错误消息
4. **测试驱动**: 每个功能都应有对应的测试用例
5. **文档完整**: 保持代码注释和README文档的更新

### 代码规范

- **命名风格**: 使用描述性的变量和函数名
- **代码风格**: 遵循PEP 8 Python代码规范
- **架构设计**: 采用模块化设计，职责分离
- **性能优化**: 合理使用GPU加速和混合精度训练



## 📚 学习资源

### MCP相关

- [MCP官方文档](https://modelcontextprotocol.io/)
- [FastMCP框架文档](https://github.com/jlowin/fastmcp)
- [MCP规范说明](https://spec.modelcontextprotocol.io/)

### AI/ML资源

- [PyTorch官方教程](https://pytorch.org/tutorials/)
- [Transformers库文档](https://huggingface.co/docs/transformers/)
- [Diffusers库文档](https://huggingface.co/docs/diffusers/)
- [豆包API文档](https://www.volcengine.com/docs/82379)

"""AI演示文稿生成服务器配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class PPTGeneratorConfig(BaseModel):
    """PPT生成配置类"""
    
    # DashScope API配置
    dashscope_api_key: str = Field(description="DashScope API密钥")
    dashscope_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        description="DashScope API基础URL"
    )
    dashscope_model: str = Field(
        default="deepseek-v3",
        description="DashScope模型名称"
    )
    
    # 输入文件路径配置
    html_spec_file_path: str = Field(description="HTML规范文件路径")
    # editable_guidelines_file_path: str = Field(description="可编辑指导文件路径")  # 暂时不需要可编辑功能
    
    # 输出配置 (固定目录)
    output_dir: str = Field(default="outputs", description="输出根目录")
    
    # 日志配置
    log_dir: str = Field(default="logs", description="日志文件目录")
    log_level: str = Field(default="INFO", description="日志级别")
    log_console: bool = Field(default=True, description="是否输出到控制台")
    
    # 生成配置
    max_tokens: int = Field(default=6000, description="最大token数量")
    temperature: float = Field(default=0.7, description="生成温度")
    max_parallel_workers: int = Field(
        default=20,
        description="并行生成的最大工作线程数"
    )
    
    @classmethod
    def from_env(cls) -> "PPTGeneratorConfig":
        """从环境变量创建配置"""
        dashscope_api_key = os.getenv("DASHSCOPE_API_KEY")
        if not dashscope_api_key:
            raise ValueError("请设置环境变量 DASHSCOPE_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上三级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        default_html_spec_path = os.path.join(
            project_root, 
            "specs", 
            "spec_html_presentation_standards.md"
        )
        
        return cls(
            dashscope_api_key=dashscope_api_key,
            dashscope_base_url=os.getenv(
                "DASHSCOPE_BASE_URL",
                "https://dashscope.aliyuncs.com/compatible-mode/v1"
            ),
            dashscope_model=os.getenv("DASHSCOPE_MODEL", "deepseek-v3"),
            html_spec_file_path=os.getenv(
                "HTML_SPEC_FILE_PATH",
                default_html_spec_path
            ),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true",
            max_tokens=int(os.getenv("MAX_TOKENS", "6000")),
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_parallel_workers=int(os.getenv("MAX_PARALLEL_WORKERS", "20"))
        ) 
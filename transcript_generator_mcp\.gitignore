# =============================================================================
# Python
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# IDEs and Editors
# =============================================================================

# PyCharm
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Project Specific
# =============================================================================

# 日志文件
logs/
*.log

# 输出文件
outputs/
temp/
cache/

# 配置文件 (包含敏感信息)
.env
.env.local
.env.production
.env.staging
config.ini
secrets.json

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 文档生成
docs/_build/
docs/build/

# 性能分析文件
*.prof
*.profile

# =============================================================================
# AI/ML Specific
# =============================================================================

# 模型文件
*.model
*.pkl
*.pickle
*.h5
*.onnx
*.pt
*.pth

# 数据文件
data/
datasets/
*.csv
*.json
*.jsonl
*.parquet

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# =============================================================================
# Docker
# =============================================================================

# Docker
.dockerignore
Dockerfile.dev
docker-compose.override.yml

# =============================================================================
# Cloud and Deployment
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# AWS
.aws/

# Google Cloud
.gcloud/

# =============================================================================
# Monitoring and Metrics
# =============================================================================

# Prometheus
prometheus_data/

# Grafana
grafana_data/

# =============================================================================
# Development Tools
# =============================================================================

# Pre-commit
.pre-commit-config.yaml

# Tox
.tox/

# Coverage
.coverage
htmlcov/

# Profiling
.prof/

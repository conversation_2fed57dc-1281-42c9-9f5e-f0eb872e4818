#!/usr/bin/env python3
"""PPT生成器MCP服务器简单测试脚本"""

import json
import requests
import sys

class SimpleMCPTester:
    """简单MCP测试器 - 只测试工具调用"""
    
    def __init__(self, server_url="http://127.0.0.1:8000"):
        self.server_url = server_url
        self.session = requests.Session()
    
    def test_generate_ppt_tool(self):
        """测试PPT生成工具"""
        print("🧪 PPT生成器MCP工具测试")
        print("=" * 50)
        
        session_id = None
        
        # 第1步：初始化MCP会话
        print("🚀 步骤1: 初始化MCP会话...")
        try:
            init_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            
            init_response = self.session.post(
                f"{self.server_url}/mcp/",
                json=init_payload,
                headers=headers,
                timeout=10
            )
            
            print(f"   初始化状态码: {init_response.status_code}")
            
            if init_response.status_code != 200:
                print(f"   ❌ 初始化失败: {init_response.text}")
                return
            else:
                # 获取会话ID
                session_id = init_response.headers.get("mcp-session-id")
                if session_id:
                    print(f"   ✅ 初始化成功，会话ID: {session_id}")
                else:
                    print("   ✅ 初始化成功，但未获取到会话ID")
                
                # 解析初始化响应（可能是SSE格式）
                init_text = init_response.text
                if "event: message" in init_text:
                    print("   📡 收到SSE格式响应")
                    # 提取JSON数据
                    import re
                    json_match = re.search(r'data: ({.*})', init_text)
                    if json_match:
                        try:
                            init_data = json.loads(json_match.group(1))
                            if "result" in init_data:
                                print("   ✅ 初始化响应验证成功")
                            elif "error" in init_data:
                                print(f"   ❌ 初始化错误: {init_data['error']}")
                        except json.JSONDecodeError:
                            pass
        
        except Exception as e:
            print(f"   ❌ 初始化错误: {e}")
            return
        
        # 第1.5步：发送initialized通知
        print("\n🔔 步骤1.5: 发送initialized通知...")
        try:
            initialized_payload = {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {}
            }
            
            initialized_headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            if session_id:
                initialized_headers["mcp-session-id"] = session_id
            
            initialized_response = self.session.post(
                f"{self.server_url}/mcp/",
                json=initialized_payload,
                headers=initialized_headers,
                timeout=10
            )
            
            print(f"   通知状态码: {initialized_response.status_code}")
            if initialized_response.status_code in [200, 202]:
                print("   ✅ initialized通知发送成功")
                # 给服务器一点时间处理初始化
                import time
                time.sleep(0.5)
                print("   ⏰ 等待服务器完成初始化...")
            else:
                print(f"   ⚠️ initialized通知失败: {initialized_response.text}")
        
        except Exception as e:
            print(f"   ⚠️ initialized通知错误: {e}")
        
        # 第2步：调用PPT生成工具
        print("\n📊 步骤2: 调用PPT生成工具...")
        
        # 准备测试用的参考资料
        test_reference_content = """
# 人工智能技术发展报告

## 概述
人工智能（AI）技术在近年来取得了突破性进展，深度学习、自然语言处理、计算机视觉等领域都有重大突破。

## 核心技术发展
### 深度学习
- 神经网络架构不断优化
- 训练效率大幅提升
- 模型解释性持续改善

### 自然语言处理
- 大型语言模型（LLM）崛起
- 多模态理解能力增强
- 对话系统更加智能

### 计算机视觉
- 图像识别准确率持续提高
- 视频理解技术日趋成熟
- 生成式AI在视觉领域应用广泛

## 应用场景
AI技术已经在医疗、金融、教育、交通等多个领域得到广泛应用，带来了显著的效率提升和创新突破。

## 未来展望
随着技术的不断发展，AI将在更多领域发挥重要作用，推动社会进步和产业升级。
        """
        
        try:
            # 构建JSON-RPC请求
            payload = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "generate_ppt",
                    "arguments": {
                        "topic": "人工智能技术发展报告",
                        "reference_content": test_reference_content
                    }
                }
            }
            
            print(f"   📦 请求负载: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            # 准备请求头，包含会话ID（如果有）
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json, text/event-stream"
            }
            if session_id:
                headers["mcp-session-id"] = session_id
                print(f"   📝 使用会话ID: {session_id}")
            
            print("🔄 正在发送请求...")
            print(f"   URL: {self.server_url}/mcp/")
            print(f"   参数: topic='人工智能技术发展报告', reference_content='{test_reference_content[:50]}...'")
            print("   ⏳ PPT生成可能需要几分钟时间，请耐心等待...")
            
            response = self.session.post(
                f"{self.server_url}/mcp/",
                json=payload,
                headers=headers,
                timeout=3000  # 增加到5分钟，因为PPT生成需要时间
            )
            
            print(f"\n📡 服务器响应:")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功！")
                
                # 处理SSE格式响应
                response_text = response.text
                if "event: message" in response_text:
                    print("   📡 收到SSE格式响应")
                    # 提取JSON数据
                    import re
                    json_match = re.search(r'data: ({.*})', response_text)
                    if json_match:
                        try:
                            result = json.loads(json_match.group(1))
                        except json.JSONDecodeError:
                            print("   ❌ SSE数据不是有效的JSON")
                            print(f"   原始响应: {response_text}")
                            return
                    else:
                        print("   ❌ 未找到SSE数据")
                        print(f"   原始响应: {response_text}")
                        return
                else:
                    # 尝试直接解析JSON
                    try:
                        result = response.json()
                    except json.JSONDecodeError:
                        print("   ❌ 响应不是有效的JSON或SSE")
                        print(f"   原始响应: {response_text}")
                        return
                
                # 解析响应
                if "result" in result:
                    tool_result = result["result"]
                    print(f"\n📋 工具返回结果:")
                    
                    if "content" in tool_result:
                        content = tool_result["content"]
                        try:
                            # 尝试解析JSON内容
                            if isinstance(content, str):
                                content_data = json.loads(content)
                            else:
                                content_data = content
                            
                            print(f"   🎯 执行状态: {content_data.get('success', 'Unknown')}")
                            print(f"   💬 消息: {content_data.get('message', 'No message')}")
                            
                            if "generated_files" in content_data:
                                files = content_data["generated_files"]
                                print(f"   📁 生成文件数量: {len(files)}")
                                if files:
                                    print("   📄 生成的文件:")
                                    for i, file in enumerate(files[:5], 1):  # 显示前5个文件
                                        print(f"      {i}. {file}")
                                    if len(files) > 5:
                                        print(f"      ... 还有 {len(files) - 5} 个文件")
                            
                            if "index_page" in content_data:
                                print(f"   🏠 索引页面: {content_data['index_page']}")
                            
                            if "error" in content_data:
                                print(f"   ❌ 错误: {content_data['error']}")
                                
                        except json.JSONDecodeError:
                            print(f"   📝 原始返回内容:")
                            print(f"      {content}")
                    else:
                        print("   ⚠️ 未找到content字段")
                        print(f"   原始结果: {tool_result}")
                
                elif "error" in result:
                    error = result["error"]
                    print(f"   ❌ JSON-RPC错误:")
                    print(f"      代码: {error.get('code', 'Unknown')}")
                    print(f"      消息: {error.get('message', 'No message')}")
                    if "data" in error:
                        print(f"      详情: {error['data']}")
                
                else:
                    print("   ⚠️ 响应格式异常")
                    print(f"   完整响应: {result}")
                    
            else:
                print(f"   ❌ 请求失败")
                print(f"   错误内容: {response.text}")
                
        except requests.exceptions.Timeout:
            print("   ⏰ 请求超时")
            print("   💡 这通常是正常现象，PPT生成需要较长时间")
            print("   📁 请检查服务器日志和输出目录是否有生成的文件")
            print(f"   📂 输出目录应该在: outputs/人工智能技术发展报告_*")
            
        except requests.exceptions.ConnectionError as e:
            print("   ❌ 连接错误")
            print(f"   🔍 详细错误: {e}")
            print("   💡 可能的原因:")
            print("      1. 服务器在处理过程中断开了连接")
            print("      2. PPT生成时间过长导致连接超时")
            print("   📁 请检查服务器是否仍在运行和输出目录")
            
        except Exception as e:
            print(f"   ❌ 发生未知错误: {e}")
            print("   📁 请检查服务器日志和输出目录")
        
        print("\n" + "=" * 50)

def main():
    """主函数"""
    print("PPT生成器MCP工具测试脚本")
    print("请确保MCP服务器正在运行在 http://127.0.0.1:8000")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    print()
    
    # 创建测试器实例
    tester = SimpleMCPTester()
    
    # 运行工具测试
    tester.test_generate_ppt_tool()
    
    print("测试完成！")

if __name__ == "__main__":
    main() 
    
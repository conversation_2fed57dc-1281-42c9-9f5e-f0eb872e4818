"""AI绘本生成MCP服务器主文件"""
import json
import sys

from fastmcp import FastMCP, Context

from src.picturebook_generator.config import PictureBookGeneratorConfig
from src.picturebook_generator.logger import get_logger, PictureBookLogger
from src.picturebook_generator.models import PictureBookInfo
from src.picturebook_generator.generators import PictureBookGeneratorService

# 初始化MCP服务器
mcp = FastMCP("Picture Book Generator MCP Server")

# 全局配置和服务实例
config = None
book_service = None
logger = get_logger(__name__)


def initialize_service():
    """初始化服务"""
    global config, book_service
    try:
        config = PictureBookGeneratorConfig.from_env()
        
        # 使用配置中的日志设置重新初始化日志系统
        PictureBookLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console
        )
        
        book_service = PictureBookGeneratorService(config)
        logger.info("绘本生成服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise

@mcp.tool()
async def generate_picture_book(
    book_title: str,
    book_style: str,
    target_pages: int,
    ctx: Context,
    book_theme: str = "",
    user_requirements: str = ""
) -> str:
    """根据绘本信息生成完整的图文绘本

    Args:
        book_title (str): 绘本标题.
        book_style (str): 绘本风格（如：卡通、水彩、油画、简笔画等）.
        target_pages (int): 目标页数.
        ctx (Context): MCP上下文，由框架自动注入.
        book_theme (str, optional): 绘本主题和描述. Defaults to "".
        user_requirements (str, optional): 用户特殊要求. Defaults to "".

    Returns:
        str: JSON格式的最终生成结果.
    """
    global book_service

    if not book_service:
        error_msg = "服务未初始化"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "Picture book generation service not initialized"
        }, ensure_ascii=False)

    try:
        logger.info(
            f"开始生成绘本: 标题='{book_title}', 风格='{book_style}', "
            f"页数={target_pages}"
        )
        
        # 创建绘本信息对象
        book_info = PictureBookInfo(
            book_title=book_title,
            book_style=book_style,
            target_pages=target_pages,
            book_theme=book_theme,
            user_requirements=user_requirements
        )

        # 生成绘本，并传入请求上下文
        result = await book_service.generate_picture_book(book_info, ctx)
        
        if result.get("success", False):
            logger.info(f"绘本生成成功: {result.get('message', '')}")
        else:
            logger.error(f"绘本生成失败: {result.get('message', '')}")

        # book_service已经返回了序列化后的结果，直接返回即可
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_msg = f"绘本生成过程中发生错误: {str(e)}"
        logger.exception(error_msg)  # 使用exception记录完整的错误堆栈
        return json.dumps({
            "success": False,
            "message": "绘本生成过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)

if __name__ == "__main__":
    # 初始化服务
    try:
        initialize_service()
        logger.info("启动绘本生成器MCP服务器...")
        # 运行MCP服务器（HTTP流模式）
        mcp.run(transport='streamable-http')
    except Exception as e:
        logger.critical(f"服务器启动失败: {str(e)}")
        sys.exit(1)

"""装饰器工具模块"""

import functools
from typing import Any, Callable

def traceable(func: Callable) -> Callable:
    """函数调用跟踪装饰器
    
    记录函数的调用、参数和返回值信息
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            raise
            
    return wrapper 
# 教学逐字稿生成器 MCP 服务

这是一个基于 **MCP (Model Context Protocol)** 的 **教学逐字稿生成服务器**，专门用于根据课程信息自动生成完整的教学逐字稿。

## 🎯 项目功能

- **智能教学设计**: 根据课程信息自动生成教学流程
- **详细逐字稿生成**: 为每个教学环节生成师生对话内容
- **多AI模型支持**: 支持阿里云DashScope、智谱AI、SiliconFlow等
- **实时进度反馈**: 通过MCP协议提供实时生成状态
- **并行处理**: 支持多环节同时生成，提升效率

## 🏗️ 项目结构

```
transcript_generator_mcp/
├── README.md              # 项目说明
├── pyproject.toml         # 项目配置
├── main.py                # MCP服务器入口
├── src/                   # 核心源码目录
│   ├── __init__.py        # 模块初始化
│   ├── config.py          # 配置管理
│   ├── models.py          # 数据模型
│   ├── generators.py      # 核心生成逻辑
│   ├── ai_client.py       # AI客户端封装
│   ├── logger.py          # 日志配置
│   └── prompts.py         # 提示词模板
└── outputs/               # 生成的逐字稿输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`generate_transcript`工具函数
- **`src/generators.py`** - 核心逐字稿生成模块
  - `TranscriptGeneratorService`: 主服务类
- **`src/ai_client.py`** - AI客户端封装
  - `AIClientFactory`: AI客户端工厂
  - 支持DashScope、智谱AI、SiliconFlow
- **`src/models.py`** - 数据模型定义
- **`src/config.py`** - 配置管理
- **`src/logger.py`** - 日志系统配置

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**:
  - 阿里云DashScope: `deepseek-v3`, `deepseek-r1`
  - 智谱AI: `glm-4`, `glm-4-flash`
  - SiliconFlow: `Qwen/Qwen2.5-72B-Instruct`
- **数据验证**: Pydantic
- **环境配置**: python-dotenv

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -e .

# 设置环境变量
export AI_API_KEY="your_api_key_here"
```

### 2. 配置选项

环境变量配置：
- `AI_PROVIDER`: AI服务提供商（dashscope/zhipuai/siliconflow）
- `AI_API_KEY`: API密钥（必需）
- `AI_MODEL_NAME`: 模型名称（可选）
- `OUTPUT_DIR`: 输出目录（可选，默认: outputs）

### 3. 运行服务

```bash
# 启动MCP服务器
python main.py
# 服务器将在 http://127.0.0.1:8000/mcp/ 启动
```

## 📚 使用示例

### MCP工具调用

```python
# 生成教学逐字稿
result = await generate_transcript(
    course_basic_info="高中数学《导数的概念与初步应用》",
    teaching_info="教学目标：1. 理解导数的定义及其几何意义；2. 掌握求函数导数的基本方法..."
)
```

### 参数说明

- `course_basic_info`: 课程基本信息（必需）
- `teaching_info`: 教学信息和目标（必需）

## 🎨 工作流程

项目的工作流程被设计为高度透明和实时，确保调用方能随时了解逐字稿的生成进度。

1. **接收课程信息** - MCP工具`generate_transcript`接收课程信息和教学目标
2. **启动实时通信** - 通过`fastmcp`的`Context`对象，开始向客户端进行实时状态推送
3. **生成教学流程** - 调用AI模型生成教学环节和时间安排
4. **并行生成逐字稿** - 为每个教学环节并行生成详细的师生对话内容
5. **输出最终结果** - 返回完整的教学逐字稿和元数据

## 📢 实时通知

在调用工具后，客户端可以通过监听服务端的实时消息来获取生成进度。

关键的`status`类型包括：
- `transcript_generation_started`: 逐字稿生成开始
- `flow_generation_completed`: 教学流程生成完成
- `section_generation_started`: 开始生成某个环节
- `section_generation_completed`: 某个环节生成完成
- `transcript_generation_completed`: 逐字稿生成完成

## 📁 输出格式

### 生成内容

生成的逐字稿包含：
- **教学流程**: 完整的教学环节和时间安排
- **逐字稿内容**: 每个环节的详细师生对话
- **元数据文件**: 包含生成信息、用量统计等

### 文件结构

```
outputs/课程名称_时间戳/
├── transcript.md      # 完整逐字稿
└── metadata.json      # 元数据信息
```

## 🌟 特色功能

- **智能教学设计**: 自动规划教学环节和时间分配
- **真实对话生成**: 生成自然的师生互动对话
- **多模型支持**: 灵活切换不同AI服务提供商
- **实时进度反馈**: 透明的生成过程和状态更新
- **并行高效处理**: 多环节同时生成，节省时间

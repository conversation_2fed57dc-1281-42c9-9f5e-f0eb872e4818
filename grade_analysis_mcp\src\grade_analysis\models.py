"""Grade Analysis MCP Server Data Models

定义完整的 Pydantic 数据模型，参考 picturebook_generator_mcp 的设计模式。
"""
import uuid
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class AnalysisRequest(BaseModel):
    """封装用户请求的分析基本信息"""
    user_request: str = Field(description="用户提出的个性化分析要求")
    data_string: str = Field(description="包含完整成绩数据的字符串，推荐使用Markdown表格格式")
    analysis_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="分析任务的唯一标识符"
    )


class AnalysisResult(BaseModel):
    """分析结果的完整数据结构"""
    success: bool = Field(description="分析是否成功完成")
    analysis_id: str = Field(description="分析任务的唯一标识符")
    report: str = Field(description="Markdown格式的分析报告")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="包含分析统计信息、用量数据、生成时间等元数据"
    )
    files: List[str] = Field(
        default_factory=list,
        description="生成的文件列表（报告文件、数据文件等）"
    )


class ModelUsageInfo(BaseModel):
    """记录单次模型调用的用量信息（复用参考项目的实现）"""
    vendor: str = Field(description="提供模型的厂商，例如：dashscope, doubao")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")
    cost_estimate: Optional[float] = Field(
        default=None,
        description="本次调用的预估成本（可选）"
    )


class ProgressUpdate(BaseModel):
    """进度更新消息的标准结构"""
    status: str = Field(description="当前状态标识")
    message: str = Field(description="用户友好的状态描述")
    progress: Optional[float] = Field(
        default=None,
        description="进度百分比（0-100）"
    )
    extra_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="额外的状态数据"
    )


class ErrorInfo(BaseModel):
    """错误信息的标准结构"""
    error_type: str = Field(description="错误类型标识")
    error_message: str = Field(description="用户友好的错误描述")
    error_details: str = Field(description="详细的技术错误信息")
    stage: str = Field(description="发生错误的阶段")
    retry_suggested: bool = Field(
        default=False,
        description="是否建议重试"
    )
    analysis_id: Optional[str] = Field(
        default=None,
        description="相关的分析任务ID"
    )


class CodeExecutionResult(BaseModel):
    """代码执行结果的数据结构"""
    success: bool = Field(description="代码执行是否成功")
    output: str = Field(description="代码执行的标准输出")
    error: Optional[str] = Field(
        default=None,
        description="代码执行的错误信息"
    )
    execution_time: float = Field(description="代码执行耗时（秒）")
    memory_usage: Optional[int] = Field(
        default=None,
        description="内存使用量（MB）"
    )


class AnalysisMetadata(BaseModel):
    """分析任务的元数据信息"""
    analysis_id: str = Field(description="分析任务的唯一标识符")
    start_time: str = Field(description="分析开始时间")
    end_time: Optional[str] = Field(
        default=None,
        description="分析结束时间"
    )
    total_duration: Optional[float] = Field(
        default=None,
        description="总耗时（秒）"
    )
    data_rows: Optional[int] = Field(
        default=None,
        description="数据行数"
    )
    data_columns: Optional[int] = Field(
        default=None,
        description="数据列数"
    )
    code_generated: Optional[str] = Field(
        default=None,
        description="生成的分析代码"
    )
    model_usage: List[ModelUsageInfo] = Field(
        default_factory=list,
        description="所有模型调用的用量信息"
    )
    total_tokens: Optional[int] = Field(
        default=None,
        description="总Token消耗"
    )
    estimated_cost: Optional[float] = Field(
        default=None,
        description="预估总成本"
    )

# Grade Analysis MCP Server Environment Variables
# Copy this file to .env and fill in your actual values

# === OpenAI Compatible API Configuration (DashScope) ===
# Required: Your DashScope API key (used as OpenAI API key)
OPENAI_API_KEY=your_dashscope_api_key_here

# Optional: OpenAI compatible base URL (default: https://dashscope.aliyuncs.com/compatible-mode/v1)
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# Optional: Model names for analysis (default: deepseek-v3)
ANALYST_MODEL=deepseek-v3
REPORTER_MODEL=deepseek-v3

# === Analysis Configuration ===
# Optional: Maximum tokens for model calls (default: 4000)
MAX_TOKENS=4000

# Optional: Temperature for model calls (default: 0.3)
TEMPERATURE=0.3

# Optional: Analysis timeout in seconds (default: 300)
ANALYSIS_TIMEOUT=300

# === Code Execution Configuration ===
# Optional: Docker image for safe code execution (default: python:3.11-slim)
DOCKER_IMAGE=python:3.11-slim

# Optional: Code execution timeout in seconds (default: 60)
CODE_EXECUTION_TIMEOUT=60

# Optional: Memory limit for code execution in MB (default: 512)
MEMORY_LIMIT=512

# === Output Configuration ===
# Optional: Output directory for analysis results (default: outputs)
OUTPUT_DIR=outputs

# === Logging Configuration ===
# Optional: Log directory (default: logs)
LOG_DIR=logs

# Optional: Log level (default: INFO)
LOG_LEVEL=INFO

# Optional: Enable console output (default: true)
LOG_CONSOLE=true

# Optional: Maximum log file size in MB (default: 10)
LOG_MAX_FILE_SIZE=10

# Optional: Number of backup log files to keep (default: 5)
LOG_BACKUP_COUNT=5

﻿
# 必需配置
# DashScope API密钥（阿里云百炼）
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# =============================================================================
# 可选配置
# DashScope API配置
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_MODEL=deepseek-v3

# 文件路径配置
HTML_SPEC_FILE_PATH=specs/spec_html_presentation_standards.md
OUTPUT_DIR=outputs

# 日志配置
LOG_DIR=logs
LOG_LEVEL=INFO
LOG_CONSOLE=true

# 生成配置
MAX_TOKENS=6000
TEMPERATURE=0.7
MAX_PARALLEL_WORKERS=20

# =============================================================================
# 配置说明
# =============================================================================

# DASHSCOPE_API_KEY: 
#   获取方式：访问 https://bailian.console.aliyun.com/ 创建API密钥
#   
# LOG_LEVEL 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
#   DEBUG: 详细的调试信息
#   INFO: 一般信息（推荐）
#   WARNING: 警告信息
#   ERROR: 错误信息
#   CRITICAL: 严重错误
#
# LOG_CONSOLE: 是否同时输出日志到控制台（true/false）
#
# MAX_PARALLEL_WORKERS: 并行生成幻灯片的最大线程数
#   建议值：2-20，根据你的系统性能和API限制调整

"""思维导图生成的处理器模块。"""

from typing import Dict, List, Optional, Any, Generator, Union, TypedDict, cast
from contextlib import contextmanager
import base64
import re
import os
import json
from pathlib import Path

from shared.decorators import traceable
from shared.configuration import LessonPlanConfiguration
from shared.utils import <PERSON><PERSON>, OSSStorageManager
from shared.model import CustomChatZhipuAI, CustomChatOpenAI, manage_txt_llm_call, manage_vision_llm_call, create_llm_client
from mindmap_graph.state import MindMapInfo, Router
from mindmap_graph.prompts import (
    TEXT_MINDMAP_SYSTEM_PROMPT,VISION_MINDMAP_DIRECT_PROMPT,
    VISION_MINDMAP_BATCH_PROMPT,VISION_MINDMAP_SUMMARY_PROMPT
)

def format_mindmap_info(mindmap_info: MindMapInfo) -> str:
    """格式化思维导图信息。
    
    Args:
        mindmap_info: MindMapInfo对象，包含思维导图相关信息
        
    Returns:
        str: 格式化后的思维导图信息字符串
    """
    info_items = []
    
    if mindmap_info.mindmap_topic:
        info_items.append(f"- 主题：{mindmap_info.mindmap_topic}")
    if mindmap_info.mindmap_description:
        info_items.append(f"- 描述：{mindmap_info.mindmap_description}")
    if mindmap_info.mindmap_requirements:
        info_items.append(f"- 要求：{mindmap_info.mindmap_requirements}")
    if mindmap_info.pdf_content and mindmap_info.pdf_content.strip():
        info_items.append(f"- 参考内容：\n{mindmap_info.pdf_content}")
        
    return "\n".join(info_items) if info_items else ""

class FileProcessAgent:
    """文件处理智能体，负责处理PDF文件并提取内容"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        # 尝试使用zhipuai的API key用于PDF处理
        zhipuai_key = os.getenv("ZHIPUAI_API_KEY") or config.text_api_key
        if not zhipuai_key:
            print("警告：未找到ZHIPUAI_API_KEY环境变量，将使用备用的本地PDF处理方法")
            self.use_api = False
        else:
            try:
                from zhipuai import ZhipuAI
                self.client = ZhipuAI(api_key=zhipuai_key)
                self.use_api = True
            except ImportError:
                print("警告：未安装zhipuai库，将使用备用的本地PDF处理方法")
                self.use_api = False
        
        # 使用与lessons相同的API和配置
        self.llm = create_llm_client(config)
        self.oss_manager = OSSStorageManager(config)
        self.pdf_cache = {}  # 用于缓存PDF临时文件路径
        
    @contextmanager
    def _manage_uploaded_file_api(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Generator[str, None, None]:
        """使用API管理上传的文件生命周期，确保文件被正确删除"""
        # 如果指定了页面范围，创建临时PDF文件
        import fitz
        temp_pdf_path = pdf_path
        if start_page is not None and end_page is not None:
            with fitz.open(pdf_path) as doc:
                # 验证页码范围
                if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                    raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                
                # 创建新的PDF文档
                new_doc = fitz.open()
                for i in range(start_page - 1, end_page):
                    new_doc.insert_pdf(doc, from_page=i, to_page=i)
                
                # 保存临时文件
                temp_pdf_path = pdf_path.replace('.pdf', f'_temp_{start_page}_{end_page}.pdf')
                new_doc.save(temp_pdf_path)
                new_doc.close()
        
        file_id = None
        try:
            file_object = self.client.files.create(
                file=Path(temp_pdf_path),
                purpose="file-extract"
            )
            file_id = file_object.id
            yield file_id
        finally:
            if file_id:
                self.client.files.delete(file_id=file_id)
            # 如果创建了临时文件，删除它
            if temp_pdf_path != pdf_path:
                try:
                    os.remove(temp_pdf_path)
                except Exception as e:
                    print(f"清理临时文件失败: {str(e)}")
            
    @traceable
    def process_pdf(self, object_name: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Dict[str, Any]:
        """处理PDF文档
        
        Args:
            object_name: OSS中的对象名称
            start_page: 起始页码（可选）
            end_page: 结束页码（可选）
            
        Returns:
            Dict: 包含处理结果的字典
        """
        # 生成缓存键
        cache_key = f"{object_name}_{start_page or ''}_{end_page or ''}"
        
        # 检查是否已有缓存的临时文件
        if cache_key in self.pdf_cache and os.path.exists(self.pdf_cache[cache_key]):
            temp_path = self.pdf_cache[cache_key]
            # 使用缓存的临时文件
            if self.use_api:
                try:
                    with self._manage_uploaded_file_api(temp_path, start_page, end_page) as file_id:
                        pdf_content = json.loads(
                            self.client.files.content(file_id=file_id).content
                        )["content"]
                        
                        result = {
                            "pdf_content": pdf_content.strip(),
                            "pdf_type": "text" if pdf_content.strip() else "vision",
                            "pdf_path": temp_path,
                            "pdf_temp_path": temp_path  # 返回缓存的临时文件路径
                        }
                        
                        return result
                except Exception as e:
                    print(f"API方法处理缓存PDF文件失败: {str(e)}")
        
        # 从OSS下载文件
        with self.oss_manager.get_temp_file(object_name) as temp_path:
            if self.use_api:
                try:
                    # 使用API方法处理PDF
                    with self._manage_uploaded_file_api(temp_path, start_page, end_page) as file_id:
                        pdf_content = json.loads(
                            self.client.files.content(file_id=file_id).content
                        )["content"]
                        
                        # 判断PDF类型
                        pdf_type = "text" if pdf_content.strip() else "vision"
                        
                        result = {
                            "pdf_content": pdf_content.strip(),
                            "pdf_type": pdf_type,
                            "pdf_path": temp_path
                        }
                        
                        # 如果是视觉类型的PDF，保留临时文件
                        if pdf_type == "vision":
                            # 复制临时文件到可持久保存的位置
                            persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                            import shutil
                            shutil.copy2(temp_path, persistent_temp_path)
                            # 缓存临时文件路径
                            self.pdf_cache[cache_key] = persistent_temp_path
                            result["pdf_temp_path"] = persistent_temp_path
                        
                        return result
                except Exception as e:
                    print(f"API方法处理PDF失败，将尝试本地方法: {str(e)}")
            
            
class MindmapTextGenerateAgent:
    """文本思维导图生成智能体，负责生成思维导图的Markdown格式文本"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
        
    @traceable
    def generate_mindmap(self, mindmap_info: MindMapInfo, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """生成思维导图Markdown文本
        
        Args:
            mindmap_info: 思维导图信息
            user_id: 用户ID，用于token统计
            session_id: 会话ID，用于token统计
            
        Returns:
            str: 生成的思维导图Markdown文本
        """
        # 格式化思维导图信息
        mindmap_info_formatted = format_mindmap_info(mindmap_info)
        
        # 使用配置对象中的提示词模板
        prompt = TEXT_MINDMAP_SYSTEM_PROMPT.format(
            mindmap_info_formatted=mindmap_info_formatted
        )
            
        self.llm.push_trace_info("STR_00", "思维导图文本生成")
        
        # 调用语言模型生成思维导图文本
        with manage_txt_llm_call(self.llm, prompt, self.config, user_id, session_id) as mindmap_text:
            # 创建目录并保存思维导图内容
            import time, os
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            topic = mindmap_info.mindmap_topic
            sanitized_topic = "".join(c if c.isalnum() else "_" for c in topic)[:50]
            output_dir = os.path.join(self.config.output_dir, f"mindmap_{timestamp}_{sanitized_topic}")
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存文件
            md_file_path = os.path.join(output_dir, "mindmap.md")
            with open(md_file_path, "w", encoding="utf-8") as f:
                f.write(mindmap_text)
            
            return mindmap_text
            
class MindmapVisionGenerateAgent:
    """视觉思维导图生成智能体，负责从PDF图像直接生成思维导图"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.max_batch_size = 5  # 每批最多处理5页
        
        # 根据不同的视觉模型提供商初始化客户端
        if config.vision_model_provider == "zhipuai":
            from zhipuai import ZhipuAI
            zhipuai_key = config.vision_api_key
            self.client = ZhipuAI(api_key=zhipuai_key)
            self.use_openai_compatible = False
        elif config.vision_model_provider in ["siliconflow", "dashscope"]:
            # 使用OpenAI兼容接口的提供商（SiliconFlow和DashScope）
            from openai import OpenAI
            self.client = OpenAI(
                api_key=config.vision_api_key,
                base_url=config.vision_model_base_url
            )
            self.use_openai_compatible = True
        else:
            raise ValueError(f"不支持的视觉模型提供商: {config.vision_model_provider}")
        
    def _get_pdf_image_bases(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> List[str]:
        """将PDF页面转换为base64编码的图像列表
        
        Args:
            pdf_path: PDF文件路径
            start_page: 起始页码（可选）
            end_page: 结束页码（可选）
            
        Returns:
            List[str]: base64编码的图像列表
        """
        # 获取PDF页面图像
        import fitz
        
        # 创建图像列表
        doc = fitz.open(pdf_path)
        
        # 确定页码范围
        start = start_page - 1 if start_page else 0
        end = min(end_page or doc.page_count, doc.page_count)
        
        # 收集所有页面的base64编码
        page_bases = []
        for page_num in range(start, end):
            page = doc[page_num]
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes("png")
            img_base64 = base64.b64encode(img_data).decode('utf-8')
            page_bases.append(img_base64)
            
        doc.close()
        return page_bases
    
    def _call_vision_model(self, img_bases: List[str], prompt: str, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """调用视觉模型处理图像
        
        Args:
            img_bases: base64编码的图像列表
            prompt: 提示词
            user_id: 用户ID，用于token统计
            session_id: 会话ID，用于token统计
            
        Returns:
            str: 模型生成的内容
        """
        # 准备消息
        messages = [{"role": "user", "content": []}]
        messages[0]["content"].append({"type": "text", "text": prompt})
        
        # 添加图像
        for img_base in img_bases:
            messages[0]["content"].append({
                "type": "image_url",
                "image_url": {"url": f"data:image/png;base64,{img_base}"}
            })
        
        # 使用manage_vision_llm_call上下文管理器
        with manage_vision_llm_call(self.client, messages, self.config, trace_desc="思维导图图像处理", user_id=user_id, session_id=session_id) as content:
            return content
    
    @traceable
    def generate_mindmap_from_pdf(self, mindmap_info: MindMapInfo, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """从PDF图像直接生成思维导图
        
        Args:
            mindmap_info: 思维导图信息
            user_id: 用户ID，用于token统计
            session_id: 会话ID，用于token统计
            
        Returns:
            str: 生成的思维导图Markdown文本
        """
        # 获取PDF页面的base64编码列表
        img_bases = []
        cached_pdf_path = None
        
        # 检查PDF路径是否有效
        if mindmap_info.pdf_temp_path and os.path.exists(mindmap_info.pdf_temp_path):
            # 使用缓存的临时文件
            cached_pdf_path = mindmap_info.pdf_temp_path
            try:
                print(f"处理缓存的PDF文件: {mindmap_info.pdf_temp_path}")
                img_bases = self._get_pdf_image_bases(
                    mindmap_info.pdf_temp_path,
                    mindmap_info.pdf_start_page,
                    mindmap_info.pdf_end_page
                )
            except Exception as e:
                print(f"处理缓存的PDF文件失败: {str(e)}")
                # 如果处理缓存文件失败，尝试从OSS重新下载
                if mindmap_info.pdf_path:
                    print(f"尝试从OSS重新下载PDF文件: {mindmap_info.pdf_path}")
                    try:
                        from shared.utils import OSSStorageManager
                        oss_manager = OSSStorageManager(self.config)
                        with oss_manager.get_temp_file(mindmap_info.pdf_path) as temp_path:
                            img_bases = self._get_pdf_image_bases(
                                temp_path,
                                mindmap_info.pdf_start_page,
                                mindmap_info.pdf_end_page
                            )
                    except Exception as oss_error:
                        print(f"从OSS下载PDF文件失败: {str(oss_error)}")
                        raise ValueError(f"无法处理PDF文件: {mindmap_info.pdf_path}, 错误: {str(oss_error)}")
        elif mindmap_info.pdf_path:
            # 如果没有缓存文件，从OSS下载
            try:
                print(f"从OSS下载PDF文件: {mindmap_info.pdf_path}")
                from shared.utils import OSSStorageManager
                oss_manager = OSSStorageManager(self.config)
                with oss_manager.get_temp_file(mindmap_info.pdf_path) as temp_path:
                    img_bases = self._get_pdf_image_bases(
                        temp_path,
                        mindmap_info.pdf_start_page,
                        mindmap_info.pdf_end_page
                    )
            except Exception as e:
                print(f"从OSS下载PDF文件失败: {str(e)}")
                raise ValueError(f"无法处理OSS中的PDF文件: {mindmap_info.pdf_path}")
        else:
            raise ValueError("未提供有效的PDF文件路径")
        
        try:
            # 总页数
            total_pages = len(img_bases)
            if total_pages == 0:
                raise ValueError("PDF文件没有可用的页面")
            
            # 格式化思维导图信息 - 提前格式化，所有提示词都使用
            mindmap_info_formatted = format_mindmap_info(mindmap_info)
            
            # 如果PDF页数不超过5页，直接用视觉大模型生成思维导图
            if total_pages <= self.max_batch_size:
                # 使用视觉直接生成提示词
                prompt = VISION_MINDMAP_DIRECT_PROMPT.format(
                    mindmap_info_formatted=mindmap_info_formatted
                )
                
                # 添加跟踪信息
                self.client.push_trace_info("STR_00", "思维导图汇总")
                
                # 调用视觉模型生成思维导图
                mindmap_text = self._call_vision_model(img_bases, prompt, user_id, session_id)
            else:
                # 如果PDF页数超过5页，分批处理然后汇总
                batch_results = []
                
                # 将图片分成最多5张一组进行处理
                for i in range(0, total_pages, self.max_batch_size):
                    batch = img_bases[i:i+self.max_batch_size]
                    batch_num = i // self.max_batch_size + 1
                    
                    # 处理当前批次
                    prompt = VISION_MINDMAP_BATCH_PROMPT.format(
                        mindmap_info_formatted=mindmap_info_formatted,
                        total_pages=total_pages,
                        batch_num=batch_num,
                        batch_size=len(batch)
                    )
                    
                    # 调用视觉模型生成当前批次的思维导图片段
                    batch_result = self._call_vision_model(batch, prompt, user_id, session_id)
                    batch_results.append(batch_result)
                
                # 汇总所有批次的结果
                batch_contents = '\n'.join(batch_results)
                
                # 构建汇总提示词
                prompt = VISION_MINDMAP_SUMMARY_PROMPT.format(
                    mindmap_info_formatted=mindmap_info_formatted,
                    batch_count=len(batch_results),
                    batch_contents=batch_contents
                )
                
                # 使用文本模型汇总
                llm = create_llm_client(self.config)
                llm.push_trace_info("STR_00", "思维导图汇总")
                
                # 调用语言模型汇总思维导图
                with manage_txt_llm_call(llm, prompt, self.config, user_id, session_id) as summary:
                    mindmap_text = summary
            
            # 创建目录并保存思维导图内容
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            topic = mindmap_info.mindmap_topic
            sanitized_topic = "".join(c if c.isalnum() else "_" for c in topic)[:50]
            output_dir = os.path.join(self.config.output_dir, f"mindmap_{timestamp}_{sanitized_topic}")
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存文件
            md_file_path = os.path.join(output_dir, "mindmap.md")
            with open(md_file_path, "w", encoding="utf-8") as f:
                f.write(mindmap_text)
                
            return mindmap_text
            
        finally:
            # 清理缓存的临时PDF文件
            if cached_pdf_path and os.path.exists(cached_pdf_path):
                try:
                    # 先关闭所有可能打开的文件句柄
                    import gc
                    gc.collect()  # 强制垃圾回收
                    
                    # 尝试直接删除文件
                    os.remove(cached_pdf_path)
                    print(f"已清理缓存的临时PDF文件: {cached_pdf_path}")
                    
                    # 清理映射信息中的临时路径
                    mindmap_info.pdf_temp_path = None
                except Exception as e:
                    print(f"清理缓存的临时PDF文件失败: {cached_pdf_path}, 错误: {str(e)}")
            
class XMindConverterAgent:
    """XMind转换智能体，负责将Markdown格式的思维导图转换为XMind格式"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
    
    def _md_to_xmind(self, md_text: str) -> str:
        """将markdown文本转换为XMind格式并导出
        
        完全按照用户提供的示例实现
        """
        import time, re, os
        
        # 查找最新的思维导图目录
        output_dirs = [d for d in os.listdir(self.config.output_dir) 
                     if os.path.isdir(os.path.join(self.config.output_dir, d)) 
                     and d.startswith("mindmap_")]
        
        # 如果找到思维导图目录，使用最新的一个
        if output_dirs:
            # 按创建时间排序，使用最新的目录
            output_dirs.sort(key=lambda x: os.path.getctime(os.path.join(self.config.output_dir, x)), reverse=True)
            output_dir = os.path.join(self.config.output_dir, output_dirs[0])
        else:
            # 如果找不到目录，创建新目录
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(self.config.output_dir, f"mindmap_{timestamp}_xmind")
            os.makedirs(output_dir, exist_ok=True)
        
        # 创建临时的markdown文件
        temp_md_path = os.path.join(output_dir, "temp.md")
        with open(temp_md_path, 'w', encoding='utf-8') as f:
            f.write(md_text)
        
        # 设置输出文件路径
        output_path = os.path.join(output_dir, "mindmap.xmind")
        
        try:
            # 尝试从Markdown内容中提取第一个标题作为XMind主题
            topic_match = re.search(r'# (.+?)(?:\n|$)', md_text)
            topic = topic_match.group(1) if topic_match else "思维导图"
            
            # 使用process_file进行转换
            from md2xmind import process_file
            process_file(temp_md_path, output_path, topic)
            
            # 删除临时文件
            if os.path.exists(temp_md_path):
                os.remove(temp_md_path)
            
            print(f"成功将Markdown转换为XMind: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"生成XMind文件时出错: {str(e)}")
            if os.path.exists(temp_md_path):
                os.remove(temp_md_path)
                
            # 创建一个错误提示文件
            error_path = os.path.join(output_dir, "转换失败.txt")
            with open(error_path, "w", encoding="utf-8") as f:
                f.write(f"XMind转换失败: {str(e)}\n请使用第三方工具转换Markdown文件。")
                
            return ""  # 返回空字符串表示转换失败
    
    @traceable
    def convert_to_xmind(self, mindmap_markdown: str) -> str:
        """将Markdown格式的思维导图转换为XMind格式
        
        Args:
            mindmap_markdown: Markdown格式的思维导图
            
        Returns:
            str: XMind文件的路径，或者空字符串表示转换失败
        """
        try:
            return self._md_to_xmind(mindmap_markdown)
        except Exception as e:
            print(f"XMind转换过程出错: {str(e)}")
            return "" # 返回空字符串表示转换失败
# TeacherAssistantAgent

基于 LangGraph 的智能教学助手，支持教案生成、思维导图生成、教学逐字稿生成和教案分析。

## 功能特点

### 1. 教案生成
- 支持文本和 PDF 输入
- 自动生成教学大纲，并根据用户习惯进行调整
- 生成完整教案，包含：
  - 教学目标
  - 重点难点
  - 教学方法
  - 教学活动
  - 教学流程
- 支持自定义教学要素
- 支持用户习惯个性化

### 2. 思维导图生成
- 支持从教案自动生成思维导图
- 多种导出格式：Markdown、XMind
- 支持自定义主题和样式
- 智能布局优化
- 支持节点自动分类和组织

### 3. 教学逐字稿生成
- 基于教学流程自动生成教学逐字稿
- 支持多个教学环节的逐字稿生成
- 自动合并成完整的教学文稿
- 按照教学环节保存独立文件
- 支持基于课程信息的定制化生成

### 4. 教案分析
- 对教案进行专业、系统的分析和评价
- 分析内容包括：
  - 教学内容分析
  - 教学过程分析
  - 教学法分析
  - 总体优点与特色
- 支持个性化分析需求
- 提供具体、专业的分析意见
- 保存为Markdown格式文件

## 系统工作流程

### 1. 教案生成流程
```mermaid
graph TD
    START[开始] --> A[preprocess_info]
    A -->|无教材| C[generate_modify_teaching_process_outline]
    A -->|用户自传教材| B[process_file]
    A -->|系统已有教材| R[read_teaching_process_outline]
    B --> C
    R --> C
    C --> D1[generate_teaching_objectives]
    C --> D2[generate_teaching_keypoints]
    C --> D3[generate_teaching_methods]
    C --> D4[generate_teaching_activities]
    C --> D5[expand_teaching_process_outline]
    D1 --> E[merge_sections]
    D2 --> E
    D3 --> E
    D4 --> E
    D5 --> E
    E --> F[wait_custom_feedback]
    F -->|添加自定义要素| G[add_custom_element]
    F -->|修改已生成要素| H[modify_generate_element]
    G --> F
    H --> F 
    F -->|完成| END[结束]
```

### 2. 思维导图生成流程
```mermaid
graph TD
    START[开始] --> A[preprocess_info]
    A -->|文本类型| B[generate_text_mindmap]
    A -->|图像类型| C[generate_vision_mindmap]
    B --> D[convert_to_xmind]
    C --> D
    D --> END[结束]
```

### 3. 教学逐字稿生成流程
```mermaid
graph TD
    START[开始] --> A[process_info]
    A --> B[generate_teaching_process]
    B --> C{分发任务}
    C --> D1[STR_00_generate_transcript_section]
    C --> D2[STR_00_generate_transcript_section]
    C --> D3[STR_00_generate_transcript_section]
    D1 --> E[merge_transcript_sections]
    D2 --> E
    D3 --> E
    E --> END[结束]
```

### 4. 教案分析流程
```mermaid
graph TD
    START[开始] --> A[process_info]
    A --> B[generate_plan_analysis]
    B --> END[结束]
```

## 项目结构

```
TeacherAssistantAgent/
├── src/
│   ├── lessonplan_graph/      # 教案生成模块
│   │   ├── graph.py          # 工作流定义
│   │   ├── processors.py     # 处理器实现
│   │   ├── state.py         # 状态定义
│   │   └── prompts.py       # 提示词模板
│   │
│   ├── mindmap_graph/       # 思维导图生成模块
│   │   ├── graph.py         # 工作流定义
│   │   ├── processors.py    # 处理器实现
│   │   ├── state.py        # 状态定义
│   │   └── prompts.py      # 提示词模板
│   │
│   ├── transcript_graph/    # 教学逐字稿生成模块
│   │   ├── graph.py        # 工作流定义
│   │   ├── processors.py   # 处理器实现
│   │   ├── state.py       # 状态定义
│   │   └── prompts.py     # 提示词模板
│   │
│   ├── plananalysis_graph/  # 教案分析模块
│   │   ├── graph.py        # 工作流定义
│   │   ├── processors.py   # 处理器实现
│   │   ├── state.py       # 状态定义
│   │   └── prompts.py     # 提示词模板
│   │
│   └── shared/              # 共享模块
│       ├── utils.py        # 工具函数
│       ├── configuration.py # 配置管理
│       └── decorators.py   # 装饰器
│
├── outputs/                 # 输出目录
│
├── docker/                # Docker 相关文件
│   ├── Dockerfile        # 主 Dockerfile
│   └── Dockerfile.base   # 基础镜像 Dockerfile
│
├── docs/                 # 文档
│   ├── deployment_guide.md    # 部署指南
│   └── pressure_test_guide.md # 压力测试指南
│
├── config.example.json    # 配置文件示例
├── .env.example          # 环境变量示例
└── pyproject.toml        # 项目元数据和依赖
```


## 环境变量说明

复制 `env.example` 文件为 `.env` 并配置以下环境变量：

- `ZHIPUAI_API_KEY`: 智谱 AI API 密钥
- `SILICONFLOW_API_KEY`: SiliconFlow API 密钥（如果使用SiliconFlow模型）
- `DASHSCOPE_API_KEY`: 阿里云百炼 API 密钥（如果使用百炼模型）
- `OSS_ACCESS_KEY_ID`: 阿里云 OSS 访问密钥 ID
- `OSS_ACCESS_KEY_SECRET`: 阿里云 OSS 访问密钥
- `TOKEN_API_DOMAIN`: Token统计API域名（默认：http://localhost:8080）

## 配置示例

1. 智谱AI配置示例：
```json
{
    "model_provider": "zhipuai",
    "text_model": "zhipuai/glm-4-air",
    "vision_model": "zhipuai/glm-4v-plus",
    "temp_dir": "temp_files",
    "output_dir": "output",
    "oss_endpoint": "oss-cn-shanghai.aliyuncs.com",
    "oss_bucket": "pdf-test0120"
}
```

2. SiliconFlow配置示例：
```json
{
    "model_provider": "siliconflow",
    "text_model": "THUDM/glm-4-9b-chat",  // 或 "Qwen/Qwen2.5-32B-Instruct" 等
    "vision_model": "zhipuai/glm-4v-plus",
    "temp_dir": "temp_files",
    "output_dir": "output",
    "oss_endpoint": "oss-cn-shanghai.aliyuncs.com",
    "oss_bucket": "pdf-test0120"
}
```

## 文件存储

项目使用阿里云OSS进行文件存储，支持以下功能：
- PDF 文件上传和下载
- 临时文件管理
- 文件存在性检查
- 安全的文件名处理

## 开发说明

1. 代码风格
- 使用 Python 类型注解
- 遵循 PEP 8 规范
- 使用上下文管理器管理资源

2. 错误处理
- 使用自定义异常
- 包含重试机制
- 详细的错误信息

3. 文档
- 使用 docstring 记录函数和类的用法
- 包含参数和返回值说明
- 提供使用示例


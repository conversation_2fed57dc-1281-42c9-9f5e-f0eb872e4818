"""AI绘本生成服务器配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class PictureBookGeneratorConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- 豆包API配置 ---
    doubao_api_key: str = Field(description="豆包平台的API访问密钥")
    doubao_base_url: str = Field(
        default="https://ark.cn-beijing.volces.com/api/v3",
        description="豆包API服务的基地址"
    )
    doubao_image_model: str = Field(
        default="doubao-seedream-3-0-t2i-250415",
        description="用于生成绘本插图的文生图模型名称"
    )
    doubao_story_model: str = Field(
        default="doubao-1-5-pro-32k-250115",
        description="用于创作绘本故事的语言大模型名称"
    )
    
    # --- 图片生成参数 ---
    image_size: str = Field(default="1280x720", description="生成图片的目标尺寸")
    guidance_scale: float = Field(
        default=7.5, 
        description="引导系数，控制图片与提示的贴合程度"
    )
    watermark: bool = Field(default=False, description="是否在生成的图片中添加平台水印")
    
    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的绘本文件存放的根目录")
    
    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @classmethod
    def from_env(cls) -> "PictureBookGeneratorConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如DOUBAO_API_KEY）。

        Returns:
            PictureBookGeneratorConfig: 一个包含所有最终配置值的实例。
        """
        doubao_api_key = os.getenv("DOUBAO_API_KEY")
        if not doubao_api_key:
            raise ValueError("请设置环境变量 DOUBAO_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上两级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            doubao_api_key=doubao_api_key,
            doubao_base_url=os.getenv(
                "DOUBAO_BASE_URL", 
                "https://ark.cn-beijing.volces.com/api/v3"
            ),
            doubao_image_model=os.getenv(
                "DOUBAO_IMAGE_MODEL", 
                "doubao-seedream-3-0-t2i-250415"
            ),
            doubao_story_model=os.getenv(
                "DOUBAO_STORY_MODEL", 
                "doubao-1-5-pro-32k-250115"
            ),
            image_size=os.getenv("IMAGE_SIZE", "1280x720"),
            guidance_scale=float(os.getenv("GUIDANCE_SCALE", "7.5")),
            watermark=os.getenv("WATERMARK", "false").lower() == "true",
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        )

# 开发扩展指南

## 概述

本文档为开发者提供 Transcript Graph 系统的扩展和定制指南，包括如何添加新的处理器、修改工作流、集成新的AI模型、调试技巧等。通过本指南，开发者可以深入理解系统架构，并根据具体需求进行扩展开发。

## 开发环境设置

### 1. 环境准备

#### 系统要求
- Python 3.9+
- Git
- 足够的磁盘空间（建议20GB+）

#### 开发工具推荐
- **IDE**: VS Code / PyCharm
- **版本控制**: Git
- **包管理**: pip / conda
- **调试工具**: Python Debugger
- **API测试**: Postman / curl

### 2. 项目设置

#### 克隆项目
```bash
git clone [项目仓库地址]
cd yunzhishi-agent-service
```

#### 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

#### 安装依赖
```bash
pip install -e ".[dev]"
```

#### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的API密钥
```

### 3. 开发模式启动

```bash
# 启动开发服务器
langgraph dev --host 0.0.0.0 --port 2024

# 或使用热重载模式
langgraph dev --watch
```

## 添加新的处理器

### 1. 处理器基础结构

创建新的处理器需要遵循统一的设计模式：

```python
# 文件: transcript_graph/processors.py

from shared.decorators import traceable
from shared.configuration import LessonPlanConfiguration
from shared.model import create_llm_client

class NewProcessorAgent:
    """新处理器的描述"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def main_processing_method(self, input_data):
        """主要处理方法"""
        # 实现具体的处理逻辑
        pass
```

### 2. 实际示例：添加摘要生成器

```python
class TranscriptSummaryAgent:
    """教学逐字稿摘要生成智能体"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_summary(self, final_transcript: str, course_info: CourseInfo) -> str:
        """生成逐字稿摘要"""
        
        # 构建提示词
        prompt = f"""
        作为一名专业的教学分析专家，请为以下教学逐字稿生成简洁的摘要。
        
        课程信息：{course_info.course_basic_info}
        
        逐字稿内容：
        {final_transcript}
        
        要求：
        1. 摘要长度控制在200-300字
        2. 突出教学重点和亮点
        3. 包含教学方法和互动特色
        4. 语言简洁专业
        """
        
        self.llm.push_trace_info("summary_generation", "逐字稿摘要生成")
        
        with manage_txt_llm_call(self.llm, prompt, self.config, None, None) as summary:
            return summary
```

### 3. 在模块中注册新处理器

```python
# 文件: transcript_graph/__init__.py

from transcript_graph.processors import (
    TeachingProcessTxtGenerateAgent,
    TeachingProcessVisionGenerateAgent,
    TranscriptSectionGenerateAgent,
    TranscriptSectionsMergeAgent,
    FileProcessAgent,
    TranscriptSummaryAgent  # 新增
)

__all__ = [
    'create_graph',
    'create_initial_state',
    # ... 其他导出
    'TranscriptSummaryAgent'  # 新增
]
```

## 修改工作流

### 1. 添加新节点

在 `graph.py` 中添加新的工作流节点：

```python
# 文件: transcript_graph/graph.py

# 全局变量中添加新的智能体实例
transcript_summary_agent = None

def create_graph(config: Optional[Dict[str, Any]] = None):
    # 初始化新的智能体
    global transcript_summary_agent
    transcript_summary_agent = TranscriptSummaryAgent(lesson_plan_config)
    
    # 添加新节点
    workflow.add_node("generate_summary", generate_summary)
    
    # 修改边连接
    workflow.add_edge("merge_transcript_sections", "generate_summary")
    workflow.add_edge("generate_summary", END)
    
    return workflow.compile()

def generate_summary(state: TranscriptState) -> Dict[str, Any]:
    """生成逐字稿摘要节点"""
    with manage_state_update("generate_summary", "生成逐字稿摘要") as state_update:
        # 获取最终逐字稿
        final_transcript = state.get("final_transcript")
        if not final_transcript:
            raise ValueError("缺少最终逐字稿，无法生成摘要")
        
        # 生成摘要
        summary = transcript_summary_agent.generate_summary(
            final_transcript, 
            state["course_info"]
        )
        
        # 更新状态
        state_update["transcript_summary"] = summary
        state_update["router"] = Router(
            stage="generate_summary",
            status="生成逐字稿摘要成功"
        )
    
    return state_update
```

### 2. 修改状态定义

如果新功能需要新的状态字段，需要更新状态定义：

```python
# 文件: transcript_graph/state.py

class TranscriptState(TypedDict):
    """教学逐字稿状态"""
    messages: List[HumanMessage]
    router: Annotated[Router, operator.or_]
    course_info: Annotated[Optional[CourseInfo], operator.or_]
    teaching_process: Annotated[TeachingProcess, operator.or_]
    teaching_processes: Annotated[Dict[str, TeachingProcessInfo], operator.or_]
    current_section: Optional[TeachingProcessInfo]
    final_transcript: Optional[str]
    transcript_summary: Optional[str]  # 新增字段

class TranscriptOutput(TypedDict):
    """教学逐字稿输出状态"""
    final_transcript: str
    teaching_processes: Dict[str, TeachingProcessInfo]
    transcript_summary: str  # 新增字段
```

### 3. 条件分支和复杂路由

添加条件分支逻辑：

```python
def route_by_content_length(state: TranscriptState):
    """根据内容长度决定处理路径"""
    final_transcript = state.get("final_transcript", "")
    
    if len(final_transcript) > 5000:
        # 长内容需要生成摘要
        return "generate_summary"
    else:
        # 短内容直接结束
        return END

# 在工作流中使用条件边
workflow.add_conditional_edges(
    "merge_transcript_sections",
    route_by_content_length,
    {
        "generate_summary": "generate_summary",
        END: END
    }
)
```

## 集成新的AI模型

### 1. 添加新的模型提供商

```python
# 文件: shared/configuration.py

@property
def txt_api_key(self) -> str:
    """获取文本模型API密钥"""
    if self.txt_model_provider == "zhipuai":
        # ... 现有代码
    elif self.txt_model_provider == "new_provider":  # 新增
        key = os.getenv("NEW_PROVIDER_API_KEY")
        if not key:
            raise ValueError("请设置环境变量 NEW_PROVIDER_API_KEY")
        return key.strip()
    else:
        raise ValueError(f"不支持的文本模型提供商: {self.txt_model_provider}")

@property
def model_base_url(self) -> str:
    """获取模型基础URL"""
    if self.txt_model_provider == "new_provider":  # 新增
        return "https://api.newprovider.com/v1"
    # ... 其他提供商
```

### 2. 扩展模型客户端创建

```python
# 文件: shared/model.py

def create_llm_client(config: LessonPlanConfiguration):
    """创建LLM客户端"""
    
    if config.txt_model_provider == "new_provider":
        from new_provider_sdk import NewProviderClient
        
        client = NewProviderClient(
            api_key=config.txt_api_key,
            base_url=config.model_base_url
        )
        return client
    
    # ... 其他提供商的处理逻辑
```

### 3. 模型调用适配

```python
def manage_new_provider_call(client, prompt, config, user_id, session_id):
    """新提供商的模型调用管理"""
    
    @contextmanager
    def call_manager():
        try:
            response = client.chat.completions.create(
                model=config.text_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            yield content
            
        except Exception as e:
            logger.error(f"新提供商模型调用失败: {str(e)}")
            raise
    
    return call_manager()
```

## 自定义提示词

### 1. 添加新提示词

```python
# 文件: transcript_graph/prompts.py

# 新增摘要生成提示词
TRANSCRIPT_SUMMARY_SYSTEM_PROMPT = """作为一名专业的教学分析专家，请为教学逐字稿生成简洁的摘要。

课程信息：
{course_basic_info}

逐字稿内容：
{final_transcript}

要求：
1. 摘要长度控制在200-300字
2. 突出教学重点和亮点  
3. 包含教学方法和互动特色
4. 语言简洁专业
5. 格式清晰，便于阅读

请直接输出摘要内容，不要包含其他解释。"""
```

### 2. 提示词参数化

```python
def format_summary_prompt(course_info: CourseInfo, final_transcript: str) -> str:
    """格式化摘要生成提示词"""
    
    return TRANSCRIPT_SUMMARY_SYSTEM_PROMPT.format(
        course_basic_info=course_info.course_basic_info,
        final_transcript=final_transcript[:3000]  # 限制长度避免超出模型限制
    )
```

### 3. 动态提示词生成

```python
def generate_dynamic_prompt(template: str, **kwargs) -> str:
    """动态生成提示词"""
    
    # 预处理参数
    processed_kwargs = {}
    for key, value in kwargs.items():
        if isinstance(value, str) and len(value) > 1000:
            # 长文本截断
            processed_kwargs[key] = value[:1000] + "..."
        else:
            processed_kwargs[key] = value
    
    return template.format(**processed_kwargs)
```

## 调试技巧

### 1. 日志配置

```python
# 文件: shared/logging_config.py

import logging
import sys

def setup_logging(level=logging.INFO):
    """设置日志配置"""
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    file_handler = logging.FileHandler('transcript_graph.log')
    file_handler.setFormatter(formatter)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
```

### 2. 状态调试

```python
def debug_state(state: TranscriptState, stage: str):
    """调试状态信息"""
    
    logger.debug(f"=== 状态调试 - {stage} ===")
    logger.debug(f"Router: {state.get('router')}")
    logger.debug(f"Course Info: {state.get('course_info')}")
    logger.debug(f"Teaching Processes Count: {len(state.get('teaching_processes', {}))}")
    logger.debug(f"Final Transcript Length: {len(state.get('final_transcript', ''))}")
    logger.debug("=" * 50)

# 在节点函数中使用
def generate_summary(state: TranscriptState) -> Dict[str, Any]:
    debug_state(state, "generate_summary_start")
    
    # ... 处理逻辑
    
    debug_state(updated_state, "generate_summary_end")
    return state_update
```

### 3. 性能分析

```python
import time
import functools

def performance_monitor(func):
    """性能监控装饰器"""
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            
            logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
            return result
            
        except Exception as e:
            end_time = time.time()
            logger.error(f"{func.__name__} 执行失败，耗时: {end_time - start_time:.2f}秒，错误: {str(e)}")
            raise
    
    return wrapper

# 使用示例
@performance_monitor
def generate_summary(state: TranscriptState) -> Dict[str, Any]:
    # ... 实现
    pass
```

### 4. 单元测试

```python
# 文件: tests/test_transcript_graph.py

import unittest
from unittest.mock import Mock, patch
from transcript_graph.processors import TranscriptSummaryAgent
from transcript_graph.state import CourseInfo

class TestTranscriptSummaryAgent(unittest.TestCase):
    
    def setUp(self):
        """测试设置"""
        self.config = Mock()
        self.agent = TranscriptSummaryAgent(self.config)
    
    @patch('transcript_graph.processors.manage_txt_llm_call')
    def test_generate_summary(self, mock_llm_call):
        """测试摘要生成"""
        
        # 模拟LLM响应
        mock_llm_call.return_value.__enter__.return_value = "测试摘要内容"
        
        # 准备测试数据
        course_info = CourseInfo(
            course_basic_info="测试课程",
            teaching_info="测试教学信息"
        )
        final_transcript = "这是一个测试逐字稿内容..."
        
        # 执行测试
        result = self.agent.generate_summary(final_transcript, course_info)
        
        # 验证结果
        self.assertEqual(result, "测试摘要内容")
        mock_llm_call.assert_called_once()

if __name__ == '__main__':
    unittest.main()
```

## 部署和发布

### 1. 构建Docker镜像

```dockerfile
# 自定义Dockerfile
FROM registry.cn-shanghai.aliyuncs.com/alice_zhang/agent-service-base:latest

WORKDIR /app

# 复制项目文件
COPY pyproject.toml *.json .
COPY src/ src/

# 安装依赖
RUN pip install --no-cache-dir -e ".[inmem]"

# 设置环境变量
ENV PYTHONPATH=/app
ENV PATH=/usr/local/bin:$PATH

EXPOSE 2024

# 启动命令
CMD ["langgraph", "dev", "--host", "0.0.0.0", "--port", "2024"]
```

### 2. 版本管理

```python
# 文件: transcript_graph/__version__.py

__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

# 在__init__.py中导入
from transcript_graph.__version__ import __version__
```

### 3. 配置管理

```python
# 生产环境配置
PRODUCTION_CONFIG = {
    "txt_model_provider": "zhipuai",
    "vision_model_provider": "zhipuai",
    "temp_dir": "/tmp/transcript_temp",
    "output_dir": "/app/outputs",
    "log_level": "INFO"
}

# 开发环境配置
DEVELOPMENT_CONFIG = {
    "txt_model_provider": "zhipuai",
    "vision_model_provider": "zhipuai", 
    "temp_dir": "./temp_files",
    "output_dir": "./outputs",
    "log_level": "DEBUG"
}
```

## 最佳实践

### 1. 代码规范

- 使用类型注解
- 遵循PEP 8编码规范
- 编写清晰的文档字符串
- 使用有意义的变量和函数名

### 2. 错误处理

- 使用具体的异常类型
- 提供有用的错误信息
- 实现优雅的降级策略
- 记录详细的错误日志

### 3. 性能优化

- 避免不必要的重复计算
- 使用适当的缓存策略
- 优化文件I/O操作
- 监控内存使用情况

### 4. 安全考虑

- 验证所有输入数据
- 安全地处理文件路径
- 保护API密钥和敏感信息
- 实施适当的访问控制

通过本开发扩展指南，开发者可以深入理解Transcript Graph的架构设计，并根据具体需求进行定制化开发，为系统添加新的功能和特性。

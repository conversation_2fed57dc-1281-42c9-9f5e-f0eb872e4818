"""Token消费统计服务模块。"""
import json
import requests
from typing import Optional, Dict, Any
from dataclasses import dataclass
import asyncio
import aiohttp
from shared.configuration import LessonPlanConfiguration
import logging

logger = logging.getLogger(__name__)

@dataclass
class TokenConsumption:
    """Token消费数据结构"""
    session_id: str  # 会话ID，非空
    user_id: str  # 用户ID，非空
    llm_name: str  # 大模型名称(qwen-plus、deepseek-r1、zhipu-32k)，非空
    plat_form: str  # 平台名称（ali、deepseek、zhipu），非空
    input_token: int  # 请求token数，非空
    output_token: int  # 应答token数，非空
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "llm_name": self.llm_name,
            "plat_form": self.plat_form,
            "input_token": self.input_token,
            "output_token": self.output_token
        }

class TokenStatService:
    """Token统计服务类"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.api_url = f"{config.token_api_domain.rstrip('/')}/api/v1/token/consumption"
        
    def map_provider_to_platform(self, provider: str) -> str:
        """将提供商映射到平台名称"""
        provider_mapping = {
            "zhipuai": "zhipu",
            "siliconflow": "siliconflow", 
            "dashscope": "ali",
            "deepseek": "deepseek"
        }
        return provider_mapping.get(provider.lower(), provider.lower())
    
    def map_model_to_llm_name(self, model_name: str, provider: str) -> str:
        """将模型名称映射到标准LLM名称"""
        # 处理智谱AI模型
        if provider == "zhipuai":
            if "glm-4" in model_name.lower():
                if "32k" in model_name.lower():
                    return "zhipu-32k"
                else:
                    return "zhipu-4"
            elif "glm-3" in model_name.lower():
                return "zhipu-3"
            else:
                return "zhipu-4"  # 默认
        
        # 处理阿里云模型
        elif provider == "dashscope":
            if "qwen" in model_name.lower():
                if "plus" in model_name.lower():
                    return "qwen-plus"
                elif "turbo" in model_name.lower():
                    return "qwen-turbo"
                elif "max" in model_name.lower():
                    return "qwen-max"
                else:
                    return "qwen-plus"  # 默认
            elif "deepseek" in model_name.lower():
                return "deepseek-r1"
            else:
                return "qwen-plus"  # 默认
        
        # 处理DeepSeek模型
        elif provider == "deepseek":
            return "deepseek-r1"
        
        # 处理SiliconFlow模型
        elif provider == "siliconflow":
            if "qwen" in model_name.lower():
                return "qwen-plus"
            elif "deepseek" in model_name.lower():
                return "deepseek-r1"
            else:
                return "qwen-plus"  # 默认
        
        # 默认情况
        return "qwen-plus"
    
    def send_token_consumption(
        self, 
        user_id: str,
        session_id: str, 
        model_name: str,
        provider: str,
        input_tokens: int, 
        output_tokens: int
    ) -> bool:
        """发送token消费统计（同步版本）
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            model_name: 模型名称
            provider: 提供商
            input_tokens: 输入token数
            output_tokens: 输出token数
            
        Returns:
            bool: 是否发送成功
        """
        if not user_id or not session_id:
            logger.warning("用户ID或会话ID为空，跳过token统计")
            return False
            
        try:
            # 映射提供商和模型名称
            platform = self.map_provider_to_platform(provider)
            llm_name = self.map_model_to_llm_name(model_name, provider)
            
            # 创建消费数据
            consumption = TokenConsumption(
                session_id=session_id,
                user_id=user_id,
                llm_name=llm_name,
                plat_form=platform,
                input_token=input_tokens,
                output_token=output_tokens
            )
            
            # 发送请求
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            # 打印发送的数据
            print(f"📊 发送Token统计数据: {json.dumps(consumption.to_dict(), ensure_ascii=False, indent=2)}")
            print(f"🌐 API地址: {self.api_url}")
            
            response = requests.post(
                self.api_url,
                json=consumption.to_dict(),
                headers=headers,
                timeout=5  # 5秒超时
            )
            
            # 打印API返回结果
            print(f"📈 Token统计API响应:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 200:
                        print("✅ Token统计发送成功")
                        logger.debug(f"Token统计发送成功: {consumption.to_dict()}")
                        return True
                    else:
                        print(f"❌ Token统计API返回错误: {result}")
                        logger.warning(f"Token统计API返回错误: {result}")
                        return False
                except json.JSONDecodeError:
                    print("⚠️  API响应不是有效的JSON格式")
                    logger.warning("Token统计API响应不是有效的JSON格式")
                    return False
            else:
                print(f"❌ Token统计发送失败，HTTP状态码: {response.status_code}")
                logger.warning(f"Token统计发送失败，状态码: {response.status_code}, 响应: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            logger.warning("Token统计请求超时")
            return False
        except requests.exceptions.RequestException as e:
            logger.warning(f"Token统计请求异常: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Token统计发送异常: {str(e)}")
            return False
    
    async def send_token_consumption_async(
        self, 
        user_id: str,
        session_id: str, 
        model_name: str,
        provider: str,
        input_tokens: int, 
        output_tokens: int
    ) -> bool:
        """发送token消费统计（异步版本）
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            model_name: 模型名称
            provider: 提供商
            input_tokens: 输入token数
            output_tokens: 输出token数
            
        Returns:
            bool: 是否发送成功
        """
        if not user_id or not session_id:
            logger.warning("用户ID或会话ID为空，跳过token统计")
            return False
            
        try:
            # 映射提供商和模型名称
            platform = self.map_provider_to_platform(provider)
            llm_name = self.map_model_to_llm_name(model_name, provider)
            
            # 创建消费数据
            consumption = TokenConsumption(
                session_id=session_id,
                user_id=user_id,
                llm_name=llm_name,
                plat_form=platform,
                input_token=input_tokens,
                output_token=output_tokens
            )
            
            # 发送异步请求
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            # 打印发送的数据
            print(f"📊 异步发送Token统计数据: {json.dumps(consumption.to_dict(), ensure_ascii=False, indent=2)}")
            print(f"🌐 API地址: {self.api_url}")
            
            timeout = aiohttp.ClientTimeout(total=5)  # 5秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    self.api_url,
                    json=consumption.to_dict(),
                    headers=headers
                ) as response:
                    text = await response.text()
                    
                    # 打印API返回结果
                    print(f"📈 Token统计异步API响应:")
                    print(f"   状态码: {response.status}")
                    print(f"   响应内容: {text}")
                    
                    if response.status == 200:
                        try:
                            result = json.loads(text)
                            if result.get("code") == 200:
                                print("✅ Token统计异步发送成功")
                                logger.debug(f"Token统计异步发送成功: {consumption.to_dict()}")
                                return True
                            else:
                                print(f"❌ Token统计异步API返回错误: {result}")
                                logger.warning(f"Token统计异步API返回错误: {result}")
                                return False
                        except json.JSONDecodeError:
                            print("⚠️  异步API响应不是有效的JSON格式")
                            logger.warning("Token统计异步API响应不是有效的JSON格式")
                            return False
                    else:
                        print(f"❌ Token统计异步发送失败，HTTP状态码: {response.status}")
                        logger.warning(f"Token统计异步发送失败，状态码: {response.status}, 响应: {text}")
                        return False
                        
        except asyncio.TimeoutError:
            logger.warning("Token统计异步请求超时")
            return False
        except aiohttp.ClientError as e:
            logger.warning(f"Token统计异步请求异常: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Token统计异步发送异常: {str(e)}")
            return False
    
    def send_token_consumption_background(
        self, 
        user_id: str,
        session_id: str, 
        model_name: str,
        provider: str,
        input_tokens: int, 
        output_tokens: int
    ) -> None:
        """在后台线程中发送token消费统计
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            model_name: 模型名称
            provider: 提供商
            input_tokens: 输入token数
            output_tokens: 输出token数
        """
        import threading
        
        def _background_send():
            self.send_token_consumption(
                user_id, session_id, model_name, provider, input_tokens, output_tokens
            )
        
        thread = threading.Thread(target=_background_send, daemon=True)
        thread.start()

def create_token_service(config: LessonPlanConfiguration) -> TokenStatService:
    """创建Token统计服务实例"""
    return TokenStatService(config) 
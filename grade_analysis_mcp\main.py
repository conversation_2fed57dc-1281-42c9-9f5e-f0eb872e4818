"""Grade Analysis MCP Server Main File

智能成绩分析 MCP 服务器主文件，参考 picturebook_generator_mcp 的实现模式。
"""
import json
import sys
from datetime import datetime

from fastmcp import FastMCP, Context

from src.grade_analysis.config import GradeAnalysisConfig
from src.grade_analysis.logger import get_logger, GradeAnalysisLogger
from src.grade_analysis.models import AnalysisRequest
from src.grade_analysis.analyzer import GradeAnalyzer

# 初始化MCP服务器
mcp = FastMCP("Grade Analysis MCP Server")

# 全局配置和服务实例
config = None
analysis_service = None
logger = get_logger(__name__)


def initialize_service():
    """初始化服务"""
    global config, analysis_service
    try:
        config = GradeAnalysisConfig.from_env()
        
        # 使用配置中的日志设置重新初始化日志系统
        GradeAnalysisLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console,
            max_file_size=config.log_max_file_size * 1024 * 1024,  # 转换为字节
            backup_count=config.log_backup_count
        )
        
        analysis_service = GradeAnalyzer(config)
        logger.info("成绩分析服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise


@mcp.tool()
async def analyze_grade_data(
    user_request: str,
    data_string: str,
    ctx: Context
) -> str:
    """智能成绩数据分析工具

    根据用户的个性化要求，对提供的成绩数据进行智能分析，并生成详细的分析报告。
    该工具使用双 AI 模型架构：Analyst 模型负责生成分析代码，Reporter 模型负责生成报告。

    Args:
        user_request (str): 用户提出的个性化分析要求，例如："请帮我分析一下一班的数学成绩，找出最高分、最低分和平均分"
        data_string (str): 包含完整成绩数据的字符串，推荐使用 Markdown 表格格式
        ctx (Context): MCP上下文，由框架自动注入，用于实时通信

    Returns:
        str: JSON格式的最终分析结果，包含成功状态、分析报告、元数据等信息
    """
    global analysis_service

    if not analysis_service:
        error_msg = "分析服务未初始化"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "Grade analysis service not initialized"
        }, ensure_ascii=False)

    try:
        logger.info(
            f"开始分析成绩数据: 用户需求='{user_request[:100]}...', "
            f"数据长度={len(data_string)} 字符"
        )
        
        # 创建分析请求对象
        analysis_request = AnalysisRequest(
            user_request=user_request,
            data_string=data_string
        )

        # 执行分析，并传入请求上下文
        result = await analysis_service.analyze_grade_data(analysis_request, ctx)
        
        if result.get("success", False):
            logger.info(f"成绩分析成功: {result.get('analysis_id', '')}")
        else:
            logger.error(f"成绩分析失败: {result.get('message', '')}")

        # analysis_service 已经返回了结构化的结果，直接返回即可
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_msg = f"成绩分析过程中发生错误: {str(e)}"
        logger.exception(error_msg)  # 使用exception记录完整的错误堆栈
        
        # 通过 MCP 协议发送错误信息
        try:
            await ctx.error(json.dumps({
                "error_type": "unexpected_server_error",
                "error_message": "服务内部发生未知错误",
                "error_details": str(e),
                "stage": "server_execution",
                "retry_suggested": True
            }))
        except:
            pass  # 如果发送错误信息失败，不要影响主要的错误处理流程
        
        return json.dumps({
            "success": False,
            "message": "成绩分析过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)


@mcp.tool()
async def check_service_status(ctx: Context) -> str:
    """检查服务状态工具

    检查成绩分析服务的运行状态，包括配置信息、Docker 可用性等。

    Args:
        ctx (Context): MCP上下文，由框架自动注入

    Returns:
        str: JSON格式的服务状态信息
    """
    global config, analysis_service
    
    try:
        status_info = {
            "service_initialized": analysis_service is not None,
            "config_loaded": config is not None,
            "timestamp": str(datetime.now())
        }
        
        if config:
            status_info.update({
                "openai_configured": bool(config.openai_api_key),
                "analyst_model": config.analyst_model,
                "reporter_model": config.reporter_model,
                "docker_image": config.docker_image,
                "output_dir": config.output_dir
            })
        
        if analysis_service:
            # 检查 Docker 可用性
            docker_available = analysis_service.code_executor.check_docker_availability()
            status_info["docker_available"] = docker_available
        
        logger.info("服务状态检查完成")
        return json.dumps(status_info, ensure_ascii=False, indent=2)
        
    except Exception as e:
        error_msg = f"检查服务状态时发生错误: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "error": error_msg
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 初始化服务
    try:
        initialize_service()
        logger.info("启动成绩分析 MCP 服务器...")
        # 运行MCP服务器（HTTP流模式）
        mcp.run(transport='streamable-http')
    except Exception as e:
        logger.critical(f"服务器启动失败: {str(e)}")
        sys.exit(1)

[project]
name = "TeacherAssistantAgent"
version = "3.9.2"
authors = [
  { name="Example Author", email="<EMAIL>" },
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "langgraph>=0.0.15",
    "langchain>=0.0.350",
    "langchain-community>=0.0.10",
    "zhipuai>=1.0.7",
    "python-dotenv>=1.0.0",
    "PyMuPDF>=1.23.8",
    "typing-extensions>=4.8.0",
    "openai>=1.0.0",
    "boto3>=1.34.0",
    "md2xmind>=1.0.0",
    "oss2>=2.18.1",
    "langchain_openai>=0.0.1"
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}
packages = [
    "lessonplan_graph",
    "shared",
    "mindmap_graph",
    "transcript_graph",
    "plananalysis_graph"
]

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
line-length = 100
target-version = "py39"
src = ["src"]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "B",    # flake8-bugbear
]
ignore = [
    "D417",  # 不要求文档中包含所有参数
    "E501",  # 行长度限制
]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-ra -q"

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.poetry.dependencies]
oss2 = "^2.18.1" 
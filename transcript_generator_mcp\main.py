"""教学逐字稿生成器MCP服务器主文件"""
import json
import sys

from fastmcp import FastMCP, Context

from src.config import TranscriptGeneratorConfig
from src.logger import get_logger, TranscriptGeneratorLogger
from src.models import TranscriptRequest
from src.generators import TranscriptGeneratorService

# 初始化MCP服务器
mcp = FastMCP("Transcript Generator MCP Server")

# 全局配置和服务实例
config = None
transcript_service = None
logger = get_logger(__name__)


def initialize_service():
    """初始化服务"""
    global config, transcript_service
    try:
        config = TranscriptGeneratorConfig.from_env()

        # 使用配置中的日志设置重新初始化日志系统
        TranscriptGeneratorLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console
        )

        transcript_service = TranscriptGeneratorService(config)
        logger.info("教学逐字稿生成服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise


@mcp.tool()
async def generate_transcript(
    course_basic_info: str,
    teaching_info: str,
    ctx: Context,
    personal_requirements: str = ""
) -> str:
    """根据课程信息生成完整的教学逐字稿

    Args:
        course_basic_info (str): 课程基本信息（如：年级、科目、课题）
        teaching_info (str): 教学信息（如：教学目标、重点难点）
        ctx (Context): MCP上下文，由框架自动注入
        personal_requirements (str, optional): 个性化要求（如：特殊教学风格、注意事项）

    Returns:
        str: JSON格式的最终生成结果
    """
    global transcript_service

    if not transcript_service:
        error_msg = "教学逐字稿生成服务未初始化"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "Transcript generation service not initialized"
        }, ensure_ascii=False)

    try:
        logger.info(
            f"开始生成教学逐字稿: 课程='{course_basic_info}', "
            f"教学信息='{teaching_info[:50]}...'"
        )

        # 创建逐字稿生成请求对象
        request = TranscriptRequest(
            course_basic_info=course_basic_info,
            teaching_info=teaching_info,
            personal_requirements=personal_requirements if personal_requirements else None
        )

        # 生成逐字稿，并传入MCP上下文
        result = await transcript_service.generate_transcript(request, ctx)

        # 构建成功响应
        response = {
            "success": True,
            "message": "教学逐字稿生成成功",
            "data": {
                "final_transcript": result.final_transcript,
                "teaching_sections": [
                    {
                        "section_id": section.section_id,
                        "title": section.title,
                        "content": section.content,
                        "transcript": section.transcript,
                        "duration_minutes": section.duration_minutes
                    }
                    for section in result.teaching_sections
                ],
                "total_sections": result.total_sections,
                "estimated_duration": result.estimated_duration,
                "generation_time": result.generation_time,
                "created_at": result.created_at.isoformat()
            }
        }

        logger.info(f"教学逐字稿生成成功: {result.total_sections}个环节, "
                   f"预计时长{result.estimated_duration}分钟, "
                   f"耗时{result.generation_time:.2f}秒")

        return json.dumps(response, ensure_ascii=False, indent=2)

    except ValueError as e:
        # 输入验证错误
        error_msg = f"输入参数验证失败: {str(e)}"
        logger.warning(error_msg)
        return json.dumps({
            "success": False,
            "message": "输入参数验证失败",
            "error": str(e)
        }, ensure_ascii=False)

    except Exception as e:
        # 其他错误
        error_msg = f"教学逐字稿生成过程中发生错误: {str(e)}"
        logger.exception(error_msg)  # 记录完整的错误堆栈
        return json.dumps({
            "success": False,
            "message": "教学逐字稿生成过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)





if __name__ == "__main__":
    # 初始化服务
    try:
        initialize_service()
        logger.info("启动教学逐字稿生成器MCP服务器...")
        # 运行MCP服务器（HTTP流模式）
        mcp.run(transport='streamable-http')
    except Exception as e:
        logger.critical(f"服务器启动失败: {str(e)}")
        sys.exit(1)
"""Grade Analysis MCP Server Test Module

测试 MCP 服务器的基本功能，参考 picturebook_generator_mcp 的测试模式。
"""
import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastmcp.testing import MCPTestClient
from src.grade_analysis.config import GradeAnalysisConfig
from src.grade_analysis.logger import get_logger


class TestGradeAnalysisMCP:
    """Grade Analysis MCP 服务器测试类"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.test_data = self._create_test_data()
    
    def _create_test_data(self) -> str:
        """创建测试用的成绩数据"""
        return """
| 学生姓名 | 数学 | 语文 | 英语 | 物理 | 化学 |
|---------|------|------|------|------|------|
| 张三    | 85   | 92   | 78   | 88   | 90   |
| 李四    | 92   | 85   | 95   | 91   | 87   |
| 王五    | 78   | 88   | 82   | 85   | 83   |
| 赵六    | 95   | 90   | 88   | 92   | 94   |
| 钱七    | 82   | 78   | 90   | 80   | 85   |
"""
    
    async def test_analyze_grade_data(self):
        """测试成绩分析功能"""
        self.logger.info("开始测试成绩分析功能")
        
        try:
            # 创建 MCP 测试客户端
            async with MCPTestClient("python", [str(project_root / "main.py")]) as client:
                # 调用分析工具
                result = await client.call_tool(
                    "analyze_grade_data",
                    {
                        "user_request": "请分析这些学生的成绩，计算每科的平均分、最高分、最低分，并找出总分最高的学生",
                        "data_string": self.test_data
                    }
                )
                
                # 解析结果
                if isinstance(result, str):
                    result_data = json.loads(result)
                else:
                    result_data = result
                
                # 验证结果结构
                assert "success" in result_data, "结果中缺少 success 字段"
                assert "analysis_id" in result_data, "结果中缺少 analysis_id 字段"
                
                if result_data["success"]:
                    assert "report" in result_data, "成功结果中缺少 report 字段"
                    assert "metadata" in result_data, "成功结果中缺少 metadata 字段"
                    self.logger.info("✅ 成绩分析测试通过")
                    self.logger.info(f"分析报告预览: {result_data['report'][:200]}...")
                else:
                    self.logger.warning(f"⚠️ 分析失败: {result_data.get('message', '未知错误')}")
                
                return result_data
                
        except Exception as e:
            self.logger.error(f"❌ 测试失败: {str(e)}")
            raise
    
    async def test_service_status(self):
        """测试服务状态检查功能"""
        self.logger.info("开始测试服务状态检查")
        
        try:
            async with MCPTestClient("python", [str(project_root / "main.py")]) as client:
                result = await client.call_tool("check_service_status", {})
                
                if isinstance(result, str):
                    status_data = json.loads(result)
                else:
                    status_data = result
                
                # 验证状态信息
                assert "service_initialized" in status_data, "状态中缺少 service_initialized 字段"
                assert "config_loaded" in status_data, "状态中缺少 config_loaded 字段"
                
                self.logger.info("✅ 服务状态检查测试通过")
                self.logger.info(f"服务状态: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
                
                return status_data
                
        except Exception as e:
            self.logger.error(f"❌ 服务状态测试失败: {str(e)}")
            raise
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始运行 Grade Analysis MCP 服务器测试")
        
        try:
            # 检查环境变量
            if not os.getenv("OPENAI_API_KEY"):
                self.logger.warning("⚠️ 未设置 OPENAI_API_KEY 环境变量，某些测试可能失败")
            
            # 运行测试
            await self.test_service_status()
            await self.test_analyze_grade_data()
            
            self.logger.info("🎉 所有测试完成")
            
        except Exception as e:
            self.logger.error(f"💥 测试运行失败: {str(e)}")
            raise


async def main():
    """主测试函数"""
    tester = TestGradeAnalysisMCP()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())

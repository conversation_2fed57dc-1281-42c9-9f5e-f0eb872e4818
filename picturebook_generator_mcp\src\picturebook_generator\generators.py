"""独立的绘本图片生成模块"""
import os
import re
import json
import httpx
from datetime import datetime
from typing import List, Dict, Any, Optional, AsyncGenerator, Tuple

from fastmcp import Context
from volcenginesdkarkruntime import Ark

from .config import PictureBookGeneratorConfig
from .logger import get_logger
from .models import PictureBookInfo, BookPage, ModelUsageInfo

class BaseArkAgent:
    """封装与火山引擎Ark平台交互的通用逻辑"""
    
    def __init__(self, config: PictureBookGeneratorConfig):
        """
        初始化Ark Agent。

        Args:
            config (PictureBookGeneratorConfig): 应用配置实例。
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.ark_client = None
        self.init_ark_client()
    
    def init_ark_client(self):
        """
        根据配置初始化Ark平台的API客户端。
        """
        try:
            self.ark_client = Ark(
                base_url=self.config.doubao_base_url,
                api_key=self.config.doubao_api_key
            )
            self.logger.info("豆包模型客户端初始化成功")
        except Exception as e:
            self.logger.error(f"初始化豆包模型客户端失败: {str(e)}")
            raise

class StoryGenerationAgent(BaseArkAgent):
    """负责生成绘本故事文本的智能体"""
    
    async def generate_story(
        self, book_info: PictureBookInfo, context: Optional[Context] = None
    ) -> Tuple[str, ModelUsageInfo]:
        """
        调用语言大模型，以流式方式生成绘本故事。
        同时，将生成的文本块通过context实时发送给客户端，并返回最终的完整故事和用量信息。

        Args:
            book_info (PictureBookInfo): 包含绘本主题、风格等信息的请求对象。
            context (Optional[Context]): MCP上下文，用于实时发送数据。

        Raises:
            ValueError: 如果API未返回用量信息或客户端未初始化。

        Returns:
            Tuple[str, ModelUsageInfo]: 一个元组，包含完整的故事情节字符串和本次调用的用量信息。
        """
        if not self.ark_client:
            raise ValueError("豆包故事生成模型客户端未初始化")
        
        self.logger.info(
            f"开始流式生成故事: {book_info.book_title} ({book_info.target_pages}页)"
        )
        
        prompt = f"""
请根据以下信息创作一个{book_info.book_style}风格的绘本故事：

绘本标题：{book_info.book_title}
绘本风格：{book_info.book_style}
目标页数：{book_info.target_pages}页
绘本主题：{book_info.book_theme}
用户要求：{book_info.user_requirements}

请创作{book_info.target_pages}页的绘本故事，每页包含：
1. 页面标题
2. 详细的画面描述（用于图片生成，需要包含具体的场景、人物、动作、色彩、情感等细节）
3. 简短的故事文本（适合儿童阅读，20-40字）

格式要求：
第1页：[页面标题]
- 画面：[详细的视觉描述，包含场景、人物、动作、色彩、光线等，适合AI图片生成]
- 文字：[简短的故事文本，朗朗上口]

第2页：[页面标题]
...

请确保：
1. 故事情节连贯，适合儿童
2. 每页画面描述足够详细，适合AI图片生成
3. 文字简洁易懂，富有童趣
4. 整体风格统一，符合{book_info.book_style}特色
"""
        
        try:
            self.logger.debug(f"调用故事生成流式API，模型: {self.config.doubao_story_model}")
            response_stream = self.ark_client.chat.completions.create(
                model=self.config.doubao_story_model,
                messages=[
                    {
                        "role": "system", 
                        "content": "你是一个专业的儿童绘本创作者，擅长创作适合AI图片生成的详细视觉描述和富有童趣的故事文本。"
                    },
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000,
                stream=True,  # 启用流式返回
                stream_options={"include_usage": True} # 请求返回usage
            )
            
            full_story = ""
            final_usage = None
            for chunk in response_stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content_piece = chunk.choices[0].delta.content
                    if context:
                        await context.info(json.dumps({
                            "status": "story_streaming",
                            "chunk": content_piece
                        }))
                    full_story += content_piece
                
                # 在最后一个数据块中获取用量信息
                if chunk.usage:
                    final_usage = chunk.usage
            
            self.logger.info("故事流式生成完成")

            if not final_usage:
                raise ValueError("未能从API响应中获取用量信息")

            usage_info = ModelUsageInfo(
                vendor="doubao",
                model_name=self.config.doubao_story_model,
                input_tokens=final_usage.prompt_tokens,
                output_tokens=final_usage.completion_tokens
            )
            return full_story, usage_info
            
        except Exception as e:
            self.logger.error(f"生成故事失败: {str(e)}")
            raise ValueError(f"生成故事失败: {str(e)}")
    
    def parse_pages(self, story: str) -> List[Dict[str, Any]]:
        """
        使用正则表达式解析由LLM生成的、包含多个页面的完整故事文本。
        设计了备用解析逻辑以提高容错性。

        Args:
            story (str): 包含所有页面内容的原始故事文本。

        Returns:
            List[Dict[str, Any]]: 一个字典列表，每个字典代表一页，包含ID、标题和内容。
        """
        self.logger.debug("开始解析故事页面内容")
        pages = []
        
        # 正则表达式，用于匹配 "第X页：[标题]\n- 画面：[内容]" 的结构
        pattern = r'第(\d+)页：([^\n]+)\n- 画面：([^-]+?)(?=\n- 文字：|\n第|\Z)'
        matches = re.findall(pattern, story, re.DOTALL)
        
        for i, match in enumerate(matches):
            page_id = match[0]
            title = match[1].strip()
            description = match[2].strip()
            
            pages.append({
                'id': page_id,
                'title': title,
                'content': description
            })
        
        # 如果正则匹配失败，尝试使用更简单的基于行的分割方法作为备用
        if not pages:
            self.logger.warning("正则匹配失败，尝试简单分割")
            lines = story.split('\n')
            current_page = None
            
            for line in lines:
                line = line.strip()
                if line.startswith('第') and '页：' in line:
                    if current_page:
                        pages.append(current_page)
                    
                    parts = line.split('：', 1)
                    page_id = re.search(r'\d+', parts[0])
                    page_id = page_id.group() if page_id else str(len(pages) + 1)
                    
                    current_page = {
                        'id': page_id,
                        'title': (
                            parts[1].strip() if len(parts) > 1 
                            else f"第{page_id}页"
                        ),
                        'content': ""
                    }
                elif line.startswith('- 画面：') and current_page:
                    current_page['content'] = line.replace('- 画面：', '').strip()
            
            if current_page:
                pages.append(current_page)
        
        self.logger.info(f"成功解析 {len(pages)} 个页面")
        return pages

class ImageGenerationAgent(BaseArkAgent):
    """负责生成绘本插图并处理相关文件的智能体"""
    
    def _create_book_directory(self, book_info: PictureBookInfo) -> str:
        """
        为每一次绘本生成任务创建一个唯一的、带时间戳的专属目录。
        这可以防止不同任务的产出文件互相覆盖。

        Args:
            book_info (PictureBookInfo): 绘本信息，用于从标题生成目录名。

        Returns:
            str: 创建好的专属目录的绝对路径。
        """
        # 生成安全的文件名（移除Windows/Linux非法字符）
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', book_info.book_title)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建目录结构: outputs/绘本标题_时间戳/
        book_dir = os.path.join(
            self.config.output_dir, 
            f"{safe_title}_{timestamp}"
        )
        os.makedirs(book_dir, exist_ok=True)
        self.logger.debug(f"创建绘本目录: {book_dir}")
        
        # 创建images子目录
        images_dir = os.path.join(book_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        self.logger.debug(f"创建图片目录: {images_dir}")
        
        return book_dir
    
    def _save_book_metadata(
        self, 
        book_dir: str, 
        book_info: PictureBookInfo, 
        story: str, 
        pages_count: int
    ):
        """
        将本次生成任务的元数据保存为metadata.json文件。
        元数据包括原始请求、生成时间、故事剧本等，便于问题排查和复现。

        Args:
            book_dir (str): 绘本的专属目录。
            book_info (PictureBookInfo): 原始请求信息。
            story (str): 生成的完整故事文本。
            pages_count (int): 最终解析出的页面数量。
        """
        metadata = {
            "book_info": {
                "title": book_info.book_title,
                "style": book_info.book_style,
                "target_pages": book_info.target_pages,
                "theme": book_info.book_theme,
                "requirements": book_info.user_requirements
            },
            "generation_info": {
                "timestamp": datetime.now().isoformat(),
                "story_length": len(story),
                "actual_pages": pages_count
            },
            "story_script": story
        }
        
        metadata_file = os.path.join(book_dir, "metadata.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"绘本元数据已保存至: {metadata_file}")
    
    async def generate_images(
        self, 
        page: BookPage, 
        book_info: PictureBookInfo, 
        book_dir: str
    ) -> Tuple[List[str], List[str], ModelUsageInfo]:
        """
        调用文生图大模型为单个页面生成插图。
        使用httpx异步下载图片以提高I/O效率，并返回图片路径列表、URL列表和用量信息。

        Args:
            page (BookPage): 需要生成图片的页面对象。
            book_info (PictureBookInfo): 绘本的整体信息，用于构建提示词。
            book_dir (str): 保存图片的专属目录。

        Raises:
            ValueError: 如果客户端未初始化或图片生成失败。

        Returns:
            Tuple[List[str], List[str], ModelUsageInfo]: 一个元组，包含生成的本地图片路径列表、
            图片URL列表和本次调用的用量信息。
        """
        if not self.ark_client:
            raise ValueError("豆包图像生成模型客户端未初始化")
        
        # 设置图片保存目录
        images_dir = os.path.join(book_dir, "images")
        
        try:
            # 构建提示词
            prompt = f"{book_info.book_style}风格的绘本插画，{page.page_content}"
            self.logger.debug(f"生成第{page.page_id}页图片，提示词: {prompt[:100]}...")
            
            # 调用豆包图像生成API
            response = self.ark_client.images.generate(
                model=self.config.doubao_image_model,
                prompt=prompt,
                size=self.config.image_size,
                guidance_scale=self.config.guidance_scale,
                watermark=self.config.watermark
            )
            
            image_paths = []
            image_urls = []
            
            # 处理响应
            if hasattr(response, 'data') and response.data:
                async with httpx.AsyncClient() as client:
                    for i, image_data in enumerate(response.data):
                        image_url = image_data.url
                        if not image_url:
                            self.logger.warning("API返回的图片数据中缺少URL")
                            continue
                        
                        image_urls.append(image_url)
                        # 优化文件命名：绘本标题_页码_序号.jpg
                        safe_title = re.sub(
                            r'[<>:"/\\|?*]', '_', book_info.book_title
                        )
                        image_filename = os.path.join(
                            images_dir, 
                            f"{safe_title}_page_{page.page_id}_{i+1}.jpg"
                        )
                        
                        # 异步下载图像
                        img_response = await client.get(image_url, timeout=60)
                        if img_response.status_code == 200:
                            with open(image_filename, 'wb') as f:
                                f.write(img_response.content)
                            self.logger.info(f"图片已保存至: {image_filename}，图片URL: {image_url}")
                            image_paths.append(image_filename)
                        else:
                            self.logger.error(
                                f"下载图片失败，URL: {image_url}, "
                                f"状态码: {img_response.status_code}"
                            )
            
            # 从响应中提取用量
            if hasattr(response, 'usage') and response.usage:
                # 图像API可能只提供total_tokens，这里我们将其归为输出
                # 注意：这部分依赖于具体API的返回，可能需要调整
                total_tokens = response.usage.total_tokens or 0
                usage_info = ModelUsageInfo(
                    vendor="doubao",
                    model_name=self.config.doubao_image_model,
                    input_tokens=0, # 图像API通常不明确区分输入/输出token
                    output_tokens=total_tokens
                )
            else:
                 # 如果API不返回usage，则提供一个默认值
                usage_info = ModelUsageInfo(
                    vendor="doubao",
                    model_name=self.config.doubao_image_model,
                    input_tokens=0,
                    output_tokens=0
                )

            return image_paths, image_urls, usage_info

        except Exception as e:
            self.logger.error(f"为第{page.page_id}页生成图片失败: {e}")
            raise ValueError(f"为第{page.page_id}页生成图片失败")

class PictureBookGeneratorService:
    """
    绘本生成服务的总调度中心（Orchestrator）。
    负责协调StoryGenerationAgent和ImageGenerationAgent，
    并管理整个端到端的绘本生成流程。
    """
    
    def __init__(self, config: PictureBookGeneratorConfig):
        """
        初始化服务，并创建所需的Agent实例。

        Args:
            config (PictureBookGeneratorConfig): 应用配置实例。
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.story_agent = StoryGenerationAgent(config)
        self.image_agent = ImageGenerationAgent(config)
        self.logger.info("绘本生成服务已初始化")

    async def generate_picture_book(
        self, book_info: PictureBookInfo, context: Context
    ) -> Dict[str, Any]:
        """
        执行完整的绘本生成流程，包括创建目录、生成故事、解析页面、生成图片，
        并通过context实时向客户端发送进度和用量更新。

        Args:
            book_info (PictureBookInfo): 来自用户请求的绘本信息。
            context (Context): MCP上下文，用于与客户端实时通信。

        Returns:
            Dict[str, Any]: 一个包含最终结果的字典，成功时包含文件路径等信息，失败时包含错误信息。
        """
        
        async def send_progress(status: str, message: str, extra: Optional[Dict] = None):
            """便捷函数，用于通过日志通道发送结构化的进度更新。"""
            if context:
                payload = {"status": status, "message": message}
                if extra:
                    payload.update(extra)
                # 将结构化数据作为JSON字符串通过info日志发送
                await context.info(json.dumps(payload))
            self.logger.info(f"[进度] {status}: {message}")

        try:
            # 1. 创建绘本目录
            book_dir = self.image_agent._create_book_directory(book_info)
            await send_progress(
                "directory_created", 
                "已创建绘本工作目录", 
                {"book_directory": book_dir}
            )

            # 2. 流式生成故事文本，并接收用量
            await send_progress("story_generation_started", "正在生成故事剧本...")

            full_story, story_usage = await self.story_agent.generate_story(
                book_info, context
            )
            await context.info(json.dumps({
                "status": "model_usage",
                "usage": story_usage.model_dump()
            }))
            
            if not full_story:
                raise ValueError("故事生成模型未能返回有效内容。")
            
            await send_progress("story_generation_completed", "故事剧本生成完毕")

            # 3. 解析页面
            await send_progress("parsing_started", "正在解析故事页面...")
            parsed_pages = self.story_agent.parse_pages(full_story)
            if not parsed_pages:
                raise ValueError("无法从生成的故事中解析出任何页面。")
            await send_progress(
                "parsing_completed", 
                f"成功解析出 {len(parsed_pages)} 个页面",
                {"page_count": len(parsed_pages)}
            )

            # 4. 保存元数据
            self.image_agent._save_book_metadata(
                book_dir, book_info, full_story, len(parsed_pages)
            )

            # 5. 逐页生成图片
            final_pages = []
            total_images_generated = 0
            for i, page_data in enumerate(parsed_pages):
                page_num = i + 1
                await send_progress(
                    "image_generation_started", 
                    f"正在为第 {page_num} 页生成图片...",
                    {"page": page_num}
                )

                book_page = BookPage(
                    page_id=page_data.get('id', str(page_num)),
                    page_title=page_data.get('title', f"第{page_num}页"),
                    page_content=page_data.get('content', '')
                    # page_text 可以在此或之后填充
                )

                image_paths, image_urls, image_usage = await self.image_agent.generate_images(
                    book_page, book_info, book_dir
                )
                await context.info(json.dumps({
                    "status": "model_usage",
                    "usage": image_usage.model_dump()
                }))

                book_page.page_images = image_paths
                book_page.page_image_urls = image_urls
                total_images_generated += len(image_paths)
                
                final_pages.append(book_page.model_dump())
                
                await send_progress(
                    "image_generation_completed",
                    f"第 {page_num} 页图片生成完毕",
                    {
                        "page": page_num, 
                        "image_count": len(image_paths),
                        "image_urls": image_urls
                    }
                )

            # 6. 返回最终结果
            success_message = f"绘本《{book_info.book_title}》生成完成！"
            self.logger.info(success_message)
            return {
                "success": True,
                "message": success_message,
                "book_directory": book_dir,
                "total_images": total_images_generated,
                "pages": final_pages
            }

        except Exception as e:
            error_message = f"生成绘本时发生错误: {str(e)}"
            self.logger.exception(error_message)
            if context:
                # 尝试在发生错误时也通知客户端
                await context.error(error_message)
            return {
                "success": False,
                "message": "生成绘本时发生错误",
                "error": str(e)
            }

### **项目需求文档：`grade_analysis_mcp` 智能成绩分析服务 (v2.0)**

#### **1. 任务总体目标 (Overall Objective)**

* **核心目标：** 创建一个基于 **MCP (Model Context Protocol)** 的智能成绩分析服务。该服务通过 MCP 工具接口，让用户能够与大语言模型（LLM）对话，完成对学生成绩数据（以字符串形式提供）的个性化分析，并自动生成一份完整的分析报告。
* **项目价值：**
  * **降低分析门槛：** 使用户无需编程或数据分析知识，即可完成个性化的数据分析。
  * **自动化与智能化：** 实现从理解需求、执行分析到生成报告的端到端自动化。
  * **实时交互体验：** 通过 MCP 协议提供实时进度反馈和状态更新。
  * **模块化与内聚性：** 采用服务化的思想，核心逻辑内聚，易于维护。

#### **2. 核心需求与功能点 (Key Requirements & Functionalities)**

* **功能点 1: MCP 工具接口**
  * 服务基于 **FastMCP 框架**，通过 MCP 协议提供工具接口。
  * 提供 `analyze_grade_data` MCP 工具，接收以下参数：
    1. `user_request` (字符串): 用户提出的个性化分析要求。
    2. `data_string` (字符串): 包含完整成绩数据的字符串，推荐使用Markdown表格格式。
    3. `ctx` (Context): MCP上下文对象，由框架自动注入，用于实时通信。
* **功能点 2: 实时进度反馈**
  * 通过 MCP 的 `context.info()` 机制，实时向客户端报告分析进度。
  * 关键状态包括：数据解析、代码生成、代码执行、报告生成、任务完成。
  * 每次 AI 模型调用后，立即通过 `context.info()` 返回详细的用量信息（Token消耗、成本等）。
* **功能点 3: 智能分析规划 (AI as Analyst)**
  * 服务内部调用 **DashScope deepseek-v3 模型**（`Analyst-LLM`）。
  * `Analyst-LLM`接收`user_request`和`data_string`作为输入。
  * 它的任务是理解数据和用户意图，然后生成可执行的Python代码（使用Pandas等库）来完成分析。
  * **Prompt工程要求：** 需设计系统提示（System Prompt）引导`Analyst-LLM`：1) 优先生成安全、高效的代码；2) 主动考虑并处理常见数据问题，如缺失值。
* **功能点 4: 代码安全执行与结果捕获**
  * 服务必须在一个安全的沙箱环境（如Docker容器）中执行`Analyst-LLM`生成的代码。
  * 执行代码，并捕获其标准输出或特定的计算结果。
  * 将所有捕获的结果整合成一个结构化的JSON对象。
  * **实时反馈：** 代码执行过程中通过 `context.info()` 报告执行状态。
* **功能点 5: 结果解读与报告生成 (AI as Reporter)**
  * 服务调用另一个 **DashScope deepseek-v3 模型**（`Reporter-LLM`）。
  * `Reporter-LLM`接收原始的`user_request`和上一步生成的JSON结果。
  * 它的任务是解读数据结果，并用通俗易懂的语言撰写一份分析报告。
  * 输出的报告内容为Markdown格式的字符串。

#### **3. 输入信息/数据 (Input Information/Data)**

* **个性化要求 (user\_request):** 字符串，例如 `"请帮我分析一下一班的数学成绩，找出最高分、最低分和平均分，并列出不及格（低于60分）的学生名单。"`
* **学生成绩数据 (data\_string):** 包含完整数据的多行字符串，例如：
  ```
  "| 学生ID | 姓名 | 班级 | 数学 |
  |---|---|---|---|\n
  | S001 | 张三 | 一班 | 85 |\n
  | S002 | 李四 | 一班 | 58 |\n
  | S003 | 王五 | 二班 | 92 |"
  ```

#### **4. 输出结果要求 (Output Requirements)**

* **成功时:** MCP 工具返回 JSON 格式的结构化结果，包含：
  * `success`: 布尔值，表示分析是否成功
  * `analysis_id`: 分析任务的唯一标识符
  * `report`: `Reporter-LLM`生成的Markdown格式报告字符串
  * `metadata`: 包含分析统计信息、用量数据、生成时间等元数据
  * `files`: 生成的文件列表（报告文件、数据文件等）
* **失败时:** 通过 MCP 的 `context.error()` 机制返回结构化错误信息，包含：
  * `error_type`: 错误类型（如 "data_parsing_error", "code_execution_error"）
  * `error_message`: 用户友好的错误描述
  * `error_details`: 详细的技术错误信息
  * `stage`: 发生错误的阶段
  * `retry_suggested`: 是否建议重试

#### **5. 关键约束与限制 (Key Constraints & Limitations)**

* **技术架构:**
  * 基于 **FastMCP 框架**，严格遵循 MCP 协议规范。
  * 采用多模型、责任分离的设计 (`Analyst-LLM`负责编码, `Reporter-LLM`负责写作)。
  * 服务是无状态的，每次请求都处理独立的输入，不保存历史数据。
  * 必须遵循标准的 MCP 项目结构和编码规范。
* **安全性:**
  * 代码执行环境必须是与主机隔离的沙箱，并严格限制其文件系统和网络权限。
  * API 密钥等敏感信息必须通过环境变量管理，严禁硬编码。
  * 输入数据必须进行验证和清理，防止注入攻击。
* **可观测性要求:**
  * 必须使用 Python `logging` 模块，严禁使用 `print()` 函数。
  * 日志系统必须支持文件持久化和轮转。
  * 每次 AI 模型调用后必须立即报告详细的用量信息（厂商、模型、Token消耗等）。
  * 所有关键操作步骤必须通过 `context.info()` 实时反馈给客户端。
* **错误处理策略:**
  * 必须通过 MCP 的 `context.error()` 提供结构化的错误报告。
  * 错误信息必须包含错误类型、用户友好的描述和技术细节。
  * **采纳策略A:** 当代码执行失败时，系统记录错误日志，并向用户报告失败，不进行自动修复或重试。

#### **6. 验收标准 (Acceptance Criteria)**

* **场景1 - 成功路径 (基本统计):**
  * **输入:**`user_request`="计算数学平均分", `data_string`包含有效的学生数学成绩。
  * **标准:**
    - MCP 工具返回成功的 JSON 结果，包含正确的数学平均分数值。
    - 客户端能够接收到完整的实时进度反馈。
    - 生成的 Markdown 报告内容准确、格式规范。
* **场景2 - 成功路径 (条件筛选):**
  * **输入:**`user_request`="找出数学不及格的学生", `data_string`包含及格和不及格的学生。
  * **标准:**
    - MCP 工具返回成功的 JSON 结果，报告中列出的学生名单必须准确无误。
    - 实时进度反馈正常工作。
    - 模型用量信息准确报告。
* **场景3 - 失败路径 (错误处理):**
  * **输入:**`user_request`="计算化学成绩", `data_string`中不包含“化学”列。
  * **标准:**
    - 通过 `context.error()` 返回结构化错误信息。
    - 错误信息包含明确的错误类型和用户友好的描述。
    - 日志文件中记录详细的技术错误信息。
* **场景4 - 实时通信验证:**
  * **标准:**
    - 客户端能够接收到所有关键阶段的进度更新。
    - 模型用量报告及时、准确。
    - 长时间分析任务中用户体验良好。

---

## **7. 技术架构要求 (Technical Architecture Requirements)**

#### **7.1 项目结构**
必须遵循标准的 MCP 项目结构：
```
grade_analysis_mcp/
├── README.md
├── pyproject.toml
├── .env.example
├── main.py                          # MCP服务器入口
├── src/
│   └── grade_analysis/
│       ├── __init__.py
│       ├── config.py                # 配置管理
│       ├── models.py                # Pydantic数据模型
│       ├── analyzer.py              # 核心分析服务
│       ├── code_executor.py         # 安全代码执行器
│       ├── ai_client.py             # AI客户端
│       └── logger.py                # 日志系统
├── tests/
│   └── test_mcp_server.py
└── outputs/                         # 分析结果输出
```

#### **7.2 核心数据模型**
必须定义以下 Pydantic 模型（参考 `picturebook_generator_mcp/src/picturebook_generator/models.py`）：
- `AnalysisRequest`: 分析请求数据模型
- `AnalysisResult`: 分析结果数据模型
- `ModelUsageInfo`: 模型用量信息（复用参考项目的实现）
- `ProgressUpdate`: 进度更新消息
- `ErrorInfo`: 错误信息模型

#### **7.3 MCP 工具接口规范**
必须严格按照 `picturebook_generator_mcp/main.py` 的模式实现：
```python
@mcp.tool()
async def analyze_grade_data(
    user_request: str,
    data_string: str,
    ctx: Context
) -> str:
    """智能成绩数据分析工具"""
```

#### **7.4 实时通信协议**
必须参考 `picturebook_generator_mcp/src/picturebook_generator/generators.py` 中 `context.info()` 的使用方式，实现以下状态的实时反馈：
- `analysis_started`: 分析任务开始
- `data_parsing`: 正在解析数据
- `code_generation`: AI正在生成分析代码
- `code_execution`: 正在执行分析代码
- `report_generation`: 正在生成分析报告
- `analysis_completed`: 分析完成
- `model_usage`: 模型用量报告（使用参考项目的 ModelUsageInfo 格式）

#### **7.5 参考项目要求**
**强制要求**：在开始每个涉及 MCP 协议实现的任务前，必须详细回顾 `picturebook_generator_mcp` 项目的相关实现：
- 项目结构和模块组织方式
- MCP 服务器入口的实现模式
- Context 注入和实时通信的使用方法
- 错误处理和日志记录的实现方式
- 配置管理和数据模型的设计模式

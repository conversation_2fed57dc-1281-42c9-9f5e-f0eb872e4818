# =============================================================================
# 教学逐字稿生成器 MCP 服务 - 环境配置示例
# =============================================================================
# 复制此文件为 .env 并填入你的实际配置值

# =============================================================================
# AI 模型配置
# =============================================================================

# AI服务提供商 (dashscope/zhipuai/siliconflow)
AI_PROVIDER=dashscope

# API密钥 (必填)
# DashScope: https://help.aliyun.com/zh/model-studio/developer-reference/get-api-key
# 智谱AI: https://open.bigmodel.cn/usercenter/apikeys
# SiliconFlow: https://cloud.siliconflow.cn/account/ak
AI_API_KEY=your_api_key_here

# 模型名称
# DashScope: deepseek-v3, deepseek-r1, deepseek-r1-0528
# 智谱AI: glm-4, glm-4-flash, glm-4-air
# SiliconFlow: Qwen/Qwen2.5-72B-Instruct
AI_MODEL_NAME=deepseek-v3




# =============================================================================
# 生成参数配置
# =============================================================================

# 最大教学环节数量
MAX_SECTIONS=6

# 并行处理限制 (同时处理的环节数)
PARALLEL_LIMIT=5

# =============================================================================
# 输出配置
# =============================================================================

# 输出文件目录
OUTPUT_DIR=outputs

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件目录
LOG_DIR=logs

# 是否启用控制台日志 (true/false)
LOG_CONSOLE=true

# =============================================================================
# 使用说明
# =============================================================================
# 1. 将此文件复制为 .env
# 2. 根据你选择的AI服务提供商填入相应的API密钥
# 3. 根据需要调整其他配置参数
# 4. 启动服务: python main.py
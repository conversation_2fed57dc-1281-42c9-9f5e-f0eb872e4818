[project]
name = "picture-book-generator-mcp"
version = "0.1.0"
description = "MCP server for AI picture book generation using Doubao API"
readme = "README.md"
requires-python = ">=3.10, <3.13"
dependencies = [
    "fastmcp>=2.0.0",
    "mcp[cli]>=1.2.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
    "volcengine-python-sdk[ark]>=1.0.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.24.0",
]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
per-file-ignores = [
    "__init__.py:F401",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

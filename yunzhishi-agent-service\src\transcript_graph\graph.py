"""教学逐字稿生成的工作流图定义。"""
import os
from typing import Dict, Any, Optional, List, Generator, Callable, Literal
from typing_extensions import TypedDict
from contextlib import contextmanager
import re
import json

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, END, START
from langgraph.constants import Send
from langgraph.types import interrupt, Command

from transcript_graph.state import (TranscriptState, TranscriptInput, TranscriptOutput,
    TeachingProcessInfo, Router, CourseInfo, TeachingProcess, create_initial_state)
from shared.utils import get_message_content, FileIO
from shared.configuration import LessonPlanConfiguration 
from transcript_graph.processors import (
    TeachingProcessTxtGenerateAgent,
    TeachingProcessVisionGenerateAgent,
    TranscriptSectionGenerateAgent, 
    TranscriptSectionsMergeAgent,
    FileProcessAgent
)

# 加载环境变量配置
load_dotenv()

class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]   # 文本模型提供商
    vision_model_provider: Optional[str]   # 视觉模型提供商
    text_model: Optional[str]   # 文本模型名称
    vision_model: Optional[str]   # 视觉模型名称
    temp_dir: Optional[str]   # 临时文件目录
    output_dir: Optional[str]   # 输出目录

# 全局智能体实例
teaching_process_txt_generate_agent = None
teaching_process_vision_generate_agent = None
transcript_section_generate_agent = None
transcript_sections_merge_agent = None
file_process_agent = None
lesson_plan_config = None  # 全局配置实例
file_io = None  # 全局文件IO实例

def create_graph(config: Optional[Any] = None) -> StateGraph:
    """创建工作流程图
    
    Args:
        config: 来自LangGraph Studio的配置
    """
    # 如果提供了配置，使用LessonPlanConfiguration.from_runnable_config创建配置对象
    global lesson_plan_config, file_io
    if config is not None:
        lesson_plan_config = LessonPlanConfiguration.from_runnable_config(config)
    else:
        lesson_plan_config = LessonPlanConfiguration()
    
    file_io = FileIO(lesson_plan_config)
    
    # 初始化全局代理
    global teaching_process_txt_generate_agent, teaching_process_vision_generate_agent
    global transcript_section_generate_agent, transcript_sections_merge_agent, file_process_agent
    
    teaching_process_txt_generate_agent = TeachingProcessTxtGenerateAgent(lesson_plan_config)
    teaching_process_vision_generate_agent = TeachingProcessVisionGenerateAgent(lesson_plan_config)
    transcript_section_generate_agent = TranscriptSectionGenerateAgent(lesson_plan_config)
    transcript_sections_merge_agent = TranscriptSectionsMergeAgent(lesson_plan_config)
    file_process_agent = FileProcessAgent(lesson_plan_config)
        
    # 创建工作流程图，传入配置模式
    workflow = StateGraph(TranscriptState, ConfigSchema, input=TranscriptInput, output=TranscriptOutput)
    
    # 添加工作节点
    workflow.add_node("process_info", process_info)
    workflow.add_node("generate_txt_teaching_process", generate_txt_teaching_process)
    workflow.add_node("generate_vision_teaching_process", generate_vision_teaching_process)
    workflow.add_node("STR_00_generate_transcript_section", generate_transcript_section)
    workflow.add_node("merge_transcript_sections", merge_transcript_sections)
    
    # 设置工作流入口点
    workflow.add_edge(START, "process_info")
    
    # 使用条件边批量下发任务
    workflow.add_conditional_edges(
        "generate_txt_teaching_process",
        send_expand_task,
        [
            "STR_00_generate_transcript_section"
        ]
    )

    workflow.add_conditional_edges(
        "generate_vision_teaching_process",
        send_expand_task,
        [
            "STR_00_generate_transcript_section"
        ]
    )
    
    # 所有节点处理完成后进入合并节点
    workflow.add_edge("STR_00_generate_transcript_section", "merge_transcript_sections")
    workflow.add_edge("merge_transcript_sections", END)
    
    return workflow.compile()

@contextmanager
def manage_state_update(stage: str, initial_status: str) -> Generator[Dict[str, Any], None, None]:
    """管理状态更新的上下文"""
    state_update = {}
    try:
        yield state_update
        # 只有在没有设置router的情况下才设置默认状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 设置错误状态
        error_status = f"{initial_status}失败"
        state_update["router"] = Router(
            stage=stage,
            status=error_status,
            error=str(e)
        )
        print(f"Error in {stage}: {str(e)}")
        raise

def process_info(state: TranscriptState) -> Command[Literal["generate_txt_teaching_process", "generate_vision_teaching_process"]]:
    """处理用户信息
    
    Args:
        state: 当前状态
        
    Returns:
        Command: 包含状态更新和下一个节点的命令
    """
    with manage_state_update("process_info", "处理用户信息") as state_update:
        # 从消息中提取最后一条
        if not state.get("messages"):
            raise ValueError("无有效的用户输入消息")
        
        latest_message = state["messages"][-1]
        # 直接使用get_message_content提取参数
        params = get_message_content(latest_message)
        
        # 更新课程信息
        course_info = CourseInfo(
            course_basic_info=params.get("course_basic_info", ""),
            teaching_info=params.get("teaching_info", ""),
            personal_requirements=params.get("personal_requirements", ""),
            pdf_path=params.get("pdf_path", None),
            pdf_start_page=params.get("pdf_start_page", None),
            pdf_end_page=params.get("pdf_end_page", None),
            pdf_type="text"  # 默认为文本类型
        )
        
        # 处理PDF文件（如果有）
        if course_info.pdf_path:
            try:
                # 处理PDF文件
                pdf_result = file_process_agent.process_pdf(
                    course_info.pdf_path,
                    course_info.pdf_start_page,
                    course_info.pdf_end_page
                )
                
                # 更新课程信息
                course_info.pdf_content = pdf_result.get("pdf_content", "")
                course_info.pdf_type = pdf_result.get("pdf_type", "text")
                course_info.pdf_path = pdf_result.get("pdf_path", course_info.pdf_path)
                
                # 如果是视觉类型的PDF且有临时路径，更新临时路径
                if course_info.pdf_type == "vision" and "pdf_temp_path" in pdf_result:
                    course_info.pdf_temp_path = pdf_result.get("pdf_temp_path")
                    print(f"已更新PDF临时文件路径: {course_info.pdf_temp_path}")
            except Exception as e:
                print(f"处理PDF文件失败: {str(e)}")
                # 即使PDF处理失败，也继续执行后续流程
        
        # 创建状态更新
        state_update["course_info"] = course_info
        state_update["router"] = Router(
            stage="process_info",
            status="处理用户信息成功"
        )
        
        # 根据PDF类型确定下一个节点
        if course_info.pdf_path and course_info.pdf_type == "vision":
            goto = "generate_vision_teaching_process"
        else:
            goto = "generate_txt_teaching_process"
            
        # 返回命令，包含状态更新和下一个节点
        return Command(
            update=state_update,
            goto=goto
        )

def generate_txt_teaching_process(state: TranscriptState) -> Dict[str, Any]:
    """生成文本教学流程
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("generate_txt_teaching_process", "生成文本教学流程") as state_update:
        # 确保课程信息存在
        if not state.get("course_info"):
            raise ValueError("缺少课程信息，无法生成教学流程")
        
        # 调用智能体生成教学流程
        teaching_process_content = teaching_process_txt_generate_agent.generate_teaching_process(
            state["course_info"]
        )
        
        # 更新状态
        state_update["teaching_process"] = TeachingProcess(content=teaching_process_content)
        state_update["router"] = Router(
            stage="generate_txt_teaching_process",
            status="生成文本教学流程成功"
        )
    
    return state_update

def generate_vision_teaching_process(state: TranscriptState) -> Dict[str, Any]:
    """生成视觉教学流程
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("generate_vision_teaching_process", "生成视觉教学流程") as state_update:
        # 确保课程信息存在
        if not state.get("course_info"):
            raise ValueError("缺少课程信息，无法生成教学流程")
        
        # 调用视觉智能体生成教学流程
        teaching_process_content = teaching_process_vision_generate_agent.generate_teaching_process(
            state["course_info"]
        )
        
        # 更新状态
        state_update["teaching_process"] = TeachingProcess(content=teaching_process_content)
        state_update["router"] = Router(
            stage="generate_vision_teaching_process",
            status="生成视觉教学流程成功"
        )
    
    return state_update

def send_expand_task(state: TranscriptState) :
    """根据状态决定下一步任务，一次性分发所有环节的生成任务"""

    # 获取课程信息和教学流程
    course_info = state["course_info"]
    teaching_process_content = state["teaching_process"].content
    
    # 解析教学流程，获取所有部分
    sections = teaching_process_txt_generate_agent.parse_teaching_process(teaching_process_content)
    
    # 准备共享状态
    shared_state = {
        "course_info": course_info,
        "teaching_process": state["teaching_process"]
    }
    
    # 为每个环节创建单独的任务
    tasks = []
    for section in sections:
        process_id = section["id"]
        process_title = section["title"]
        process_content = section["content"]
        
        # 创建包含当前部分信息的状态
        section_state = shared_state.copy()
        section_state["current_section"] = TeachingProcessInfo(
            process_id=process_id,
            process_title=process_title,
            content=process_content
        )
        
        # 添加到任务列表
        tasks.append(Send("STR_00_generate_transcript_section", section_state))
    
    return tasks

def generate_transcript_section(state: TranscriptState) -> Dict[str, Any]:
    """生成单个教学环节的逐字稿
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("generate_transcript_section", "生成逐字稿环节") as state_update:
        # 确保教学流程存在
        if not state.get("teaching_process") or not state["teaching_process"].content:
            raise ValueError("缺少教学流程，无法生成逐字稿")
        
        # 获取当前需要处理的环节
        current_section = state.get("current_section")
        if not current_section:
            raise ValueError("未提供要生成逐字稿的环节信息")
        
        process_id = current_section.process_id
        process_title = current_section.process_title
        process_content = current_section.content
        
        # 从process_id中提取数字部分
        process_number = int(''.join(filter(str.isdigit, process_id)))
        
        # 生成当前环节的逐字稿
        transcript = transcript_section_generate_agent.generate_transcript_section(
            state["course_info"],
            state["teaching_process"].content,
            process_id,
            process_title,
            process_content
        )
        
        # 更新当前环节信息
        updated_section = TeachingProcessInfo(
            process_id=process_id,
            process_title=process_title,
            content=process_content,
            transcript=transcript
        )
        
        # 更新状态
        state_update["teaching_processes"] = {
            process_id: updated_section
        }
        
        state_update["router"] = Router(
            stage="generate_transcript_section",
            status=f"FIN_00_{process_id}_逐字稿成功"
        )
    
    return state_update

def merge_transcript_sections(state: TranscriptState) -> Dict[str, Any]:
    """合并所有环节的逐字稿
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("merge_transcript_sections", "合并逐字稿") as state_update:
        # 从状态中收集所有环节的逐字稿
        teaching_processes = {}
        
        # 合并当前状态中的所有教学流程信息
        if state.get("teaching_processes"):
            teaching_processes.update(state["teaching_processes"])
            
        # 如果没有任何逐字稿，返回错误信息
        if not teaching_processes:
            state_update["final_transcript"] = "未生成任何逐字稿环节。"
            state_update["router"] = Router(
                stage="merge_transcript_sections",
                status="无逐字稿可合并"
            )
            return state_update
        
        # 调用智能体合并逐字稿
        final_transcript = transcript_sections_merge_agent.merge_transcript_sections(teaching_processes)
        
        # 更新状态
        state_update["teaching_processes"] = teaching_processes
        state_update["final_transcript"] = final_transcript
        state_update["router"] = Router(
            stage="merge_transcript_sections",
            status="FIN_ALL_合并逐字稿成功"
        )
    
    return state_update 
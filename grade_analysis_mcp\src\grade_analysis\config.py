"""Grade Analysis MCP Server Configuration Module

基于 Pydantic 的配置管理系统，参考 picturebook_generator_mcp 的设计模式。
"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class GradeAnalysisConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- OpenAI 兼容 API 配置（DashScope） ---
    openai_api_key: str = Field(description="DashScope API 密钥（作为 OpenAI API Key 使用）")
    openai_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        description="DashScope OpenAI 兼容接口的基地址"
    )
    analyst_model: str = Field(
        default="deepseek-v3",
        description="用于代码生成分析的模型名称"
    )
    reporter_model: str = Field(
        default="deepseek-v3",
        description="用于报告生成的模型名称"
    )
    
    # --- 分析参数配置 ---
    max_tokens: int = Field(
        default=4000,
        description="模型调用的最大 Token 数量"
    )
    temperature: float = Field(
        default=0.3,
        description="模型调用的温度参数，控制输出的随机性"
    )
    analysis_timeout: int = Field(
        default=300,
        description="分析任务的超时时间（秒）"
    )
    
    # --- 安全执行配置 ---
    docker_image: str = Field(
        default="python:3.11-slim",
        description="用于安全代码执行的 Docker 镜像"
    )
    code_execution_timeout: int = Field(
        default=60,
        description="代码执行的超时时间（秒）"
    )
    memory_limit: int = Field(
        default=512,
        description="代码执行的内存限制（MB）"
    )
    
    # --- 输出路径配置 ---
    output_dir: str = Field(
        default="outputs",
        description="分析结果文件存放的根目录"
    )
    
    # --- 日志系统配置 ---
    log_dir: str = Field(
        default="logs",
        description="日志文件的存放目录"
    )
    log_level: str = Field(
        default="INFO",
        description="日志记录的最低级别"
    )
    log_console: bool = Field(
        default=True,
        description="是否同时将日志输出到控制台"
    )
    log_max_file_size: int = Field(
        default=10,
        description="单个日志文件的最大大小（MB）"
    )
    log_backup_count: int = Field(
        default=5,
        description="保留的日志备份文件数量"
    )
    
    @classmethod
    def from_env(cls) -> "GradeAnalysisConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如OPENAI_API_KEY）。

        Returns:
            GradeAnalysisConfig: 一个包含所有最终配置值的实例。
        """
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("请设置环境变量 OPENAI_API_KEY（DashScope API Key）")
        
        # 获取项目根目录（从当前文件位置向上两级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            openai_api_key=openai_api_key,
            openai_base_url=os.getenv(
                "OPENAI_BASE_URL",
                "https://dashscope.aliyuncs.com/compatible-mode/v1"
            ),
            analyst_model=os.getenv(
                "ANALYST_MODEL",
                "deepseek-v3"
            ),
            reporter_model=os.getenv(
                "REPORTER_MODEL",
                "deepseek-v3"
            ),
            max_tokens=int(os.getenv("MAX_TOKENS", "4000")),
            temperature=float(os.getenv("TEMPERATURE", "0.3")),
            analysis_timeout=int(os.getenv("ANALYSIS_TIMEOUT", "300")),
            docker_image=os.getenv("DOCKER_IMAGE", "python:3.11-slim"),
            code_execution_timeout=int(os.getenv("CODE_EXECUTION_TIMEOUT", "60")),
            memory_limit=int(os.getenv("MEMORY_LIMIT", "512")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true",
            log_max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", "10")),
            log_backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )

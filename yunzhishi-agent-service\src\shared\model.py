"""统一管理大模型调用的模块。"""
from typing import Dict, List, Optional, TypedDict, Generator, Any, Union, Iterator, AsyncIterator
from langchain_core.messages import BaseMessage
from langchain_core.callbacks import CallbackManagerForLLMRun, AsyncCallbackManagerForLLMRun
from langchain_core.outputs import ChatGenerationChunk
from langchain_core.messages import AIMessageChunk
from httpx_sse import connect_sse, aconnect_sse
from langchain_community.chat_models import ChatZhipuAI
from zhipuai import ZhipuAI
from openai import OpenAI
from pathlib import Path
import json
from shared.utils import FileIO, OSSStorageManager
from shared.configuration import LessonPlanConfiguration 
from shared.token_service import create_token_service
from contextlib import contextmanager
from tenacity import retry, stop_after_attempt, wait_exponential
from langchain_core.messages import AIMessageChunk
import uuid
from shared.zhipuai import _truncate_params, _convert_delta_to_message_chunk, _get_jwt_token
from langchain_openai import ChatOpenAI

class CustomChatZhipuAI(ChatZhipuAI):
    """自定义的ChatZhipuAI类，添加中文跟踪字段"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._trace_info_stack = []
        self._title_message = None
        
    def push_trace_info(self, trace_id: str, trace_desc: str) -> None:
        """压入新的跟踪信息"""
        self._trace_info_stack.append({
            "trace_id": trace_id,
            "trace_desc": trace_desc
        })
        
    def pop_trace_info(self) -> None:
        """弹出最近的跟踪信息"""
        if self._trace_info_stack:
            self._trace_info_stack.pop()
            
    @property
    def current_trace_info(self) -> dict:
        """获取当前的跟踪信息"""
        if self._trace_info_stack:
            return self._trace_info_stack[-1]
        return None

    def set_title_message(self, title: str) -> None:
        """设置标题消息"""
        if self.current_trace_info:
            self._title_message = AIMessageChunk(
                content=title,
                additional_kwargs={
                    "trace_metadata": {
                        "trace_id": self.current_trace_info["trace_id"],
                        "chinese_desc": self.current_trace_info["trace_desc"]
                    }
                }
            )
    
    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        """重写流式处理方法"""
        if self.zhipuai_api_key is None:
            raise ValueError("Did not find zhipuai_api_key.")
        if self.zhipuai_api_base is None:
            raise ValueError("Did not find zhipu_api_base.")
        message_dicts, params = self._create_message_dicts(messages, stop)
        payload = {**params, **kwargs, "messages": message_dicts, "stream": True}
        _truncate_params(payload)
        headers = {
            "Authorization": _get_jwt_token(self.zhipuai_api_key),
            "Accept": "application/json",
        }

        default_chunk_class = AIMessageChunk
        import httpx

        # 在开始流式处理前保存当前的跟踪信息
        current_trace_info = self.current_trace_info

        with httpx.Client(headers=headers, timeout=60) as client:
            with connect_sse(
                client, "POST", self.zhipuai_api_base, json=payload
            ) as event_source:
                # 如果有标题消息，先yield标题
                if self._title_message:
                    title_chunk = ChatGenerationChunk(message=self._title_message)
                    if run_manager:
                        run_manager.on_llm_new_token(title_chunk.text, chunk=title_chunk)
                    yield title_chunk
                    self._title_message = None  # 清除标题，避免重复发送
                    
                for sse in event_source.iter_sse():
                    chunk = json.loads(sse.data)
                    if len(chunk["choices"]) == 0:
                        continue
                    choice = chunk["choices"][0]
                    usage = chunk.get("usage", None)
                    model_name = chunk.get("model", "")
                    
                    # 创建消息块并添加跟踪信息
                    message_chunk = _convert_delta_to_message_chunk(
                        choice["delta"], default_chunk_class
                    )
                    
                    # 添加跟踪信息到消息块
                    if current_trace_info and not message_chunk.additional_kwargs:
                        message_chunk.additional_kwargs = {}
                    if current_trace_info:
                        message_chunk.additional_kwargs["trace_metadata"] = {
                            "trace_id": current_trace_info["trace_id"],
                            "chinese_desc": current_trace_info["trace_desc"]
                        }
                    
                    finish_reason = choice.get("finish_reason", None)
                    generation_info = (
                        {
                            "finish_reason": finish_reason,
                            "token_usage": usage,
                            "model_name": model_name,
                        }
                        if finish_reason is not None
                        else None
                    )
                    chunk = ChatGenerationChunk(
                        message=message_chunk, generation_info=generation_info
                    )
                    if run_manager:
                        run_manager.on_llm_new_token(chunk.text, chunk=chunk)
                    yield chunk

                    if finish_reason is not None:
                        break
                    
# 修改后的OpenAI兼容类，基于LangChain的ChatOpenAI
class CustomChatOpenAI(ChatOpenAI):
    """基于LangChain的ChatOpenAI，添加标题和跟踪信息"""
    
    def __init__(self, model_name: str, api_key: str, base_url: str, streaming: bool = True):
        super().__init__(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base=base_url,
            streaming=streaming
        )
        self._trace_info_stack = []
        self._title_message = None
        self.streaming = streaming
    
    def push_trace_info(self, trace_id: str, trace_desc: str) -> None:
        """压入新的跟踪信息"""
        self._trace_info_stack.append({
            "trace_id": trace_id,
            "trace_desc": trace_desc
        })
        
    def pop_trace_info(self) -> None:
        """弹出最近的跟踪信息"""
        if self._trace_info_stack:
            self._trace_info_stack.pop()
    
    @property
    def current_trace_info(self) -> dict:
        """获取当前的跟踪信息"""
        if self._trace_info_stack:
            return self._trace_info_stack[-1]
        return None
    
    def set_title_message(self, title: str) -> None:
        """设置标题消息"""
        if self.current_trace_info:
            # 直接创建AIMessageChunk对象
            self._title_message = AIMessageChunk(
                content=title,
                additional_kwargs={
                    "trace_metadata": {
                        "trace_id": self.current_trace_info["trace_id"],
                        "chinese_desc": self.current_trace_info["trace_desc"]
                    }
                }
            )
    
    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        """重写流式处理方法，在父类的基础上添加跟踪信息"""
        # 保存当前的跟踪信息
        current_trace_info = self.current_trace_info
        
        # 如果有标题消息，先发送标题
        if self._title_message:
            title_chunk = ChatGenerationChunk(message=self._title_message)
            if run_manager:
                run_manager.on_llm_new_token(title_chunk.text, chunk=title_chunk)
            yield title_chunk
            self._title_message = None
        
        try:
            # 使用父类的_stream方法获取流式输出
            for chunk in super()._stream(messages, stop, run_manager, **kwargs):
                # 获取原始消息块
                message = chunk.message
                
                # 添加跟踪信息到消息块（如果没有）
                if current_trace_info:
                    if not message.additional_kwargs:
                        message.additional_kwargs = {}
                    
                    # 添加或更新跟踪元数据
                    message.additional_kwargs["trace_metadata"] = {
                        "trace_id": current_trace_info["trace_id"],
                        "chinese_desc": current_trace_info["trace_desc"]
                    }
                
                # 返回修改后的块
                yield chunk
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"流式调用失败: {str(e)}\n{error_details}")
            
            # 创建错误消息块
            error_message = AIMessageChunk(
                content=f"模型调用失败: {str(e)}",
                additional_kwargs={
                    "trace_metadata": {
                        "trace_id": current_trace_info["trace_id"] if current_trace_info else "",
                        "chinese_desc": current_trace_info["trace_desc"] if current_trace_info else ""
                    }
                } if current_trace_info else {}
            )
            yield ChatGenerationChunk(message=error_message)

@contextmanager
def manage_txt_llm_call(llm: Union[CustomChatZhipuAI, CustomChatOpenAI], prompt: str, config: LessonPlanConfiguration, user_id: Optional[str] = None, session_id: Optional[str] = None) -> Generator[str, None, None]:
    """管理文本LLM调用的上下文，包含重试机制和错误处理"""
    # 保存当前的跟踪信息
    current_trace_info = None
    if isinstance(llm, (CustomChatZhipuAI, CustomChatOpenAI)):
        current_trace_info = llm.current_trace_info

    # 创建token统计服务
    token_service = create_token_service(config)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    def _call_with_retry():
        try:
            trace_desc = "未知节点"
            if current_trace_info:
                trace_desc = current_trace_info.get("trace_desc", "未知节点")
            print(f"文本模型调用 - 节点: {trace_desc} | 模型: {config.text_model} | 提供商: {config.txt_model_provider}")
            
            # 处理跟踪信息堆栈
            if current_trace_info:
                llm._trace_info_stack.append(current_trace_info)
            try:
                # 统一调用接口
                response = llm.invoke(prompt)
                
                # 获取token使用信息并发送统计
                if hasattr(response, 'response_metadata') and response.response_metadata:
                    token_usage = response.response_metadata.get('token_usage', {})
                    input_tokens = token_usage.get('prompt_tokens', 0)
                    output_tokens = token_usage.get('completion_tokens', 0)
                    
                    # 在后台发送token统计
                    if user_id and session_id and (input_tokens > 0 or output_tokens > 0):
                        token_service.send_token_consumption_background(
                            user_id=user_id,
                            session_id=session_id,
                            model_name=config.get_text_model_name(),
                            provider=config.txt_model_provider,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens
                        )
                
                return response.content
            finally:
                # 清理跟踪信息堆栈
                if current_trace_info:
                    llm._trace_info_stack.pop()
                    
        except Exception as e:
            print(f"文本模型调用失败 - 节点: {trace_desc} | 错误: {str(e)}, 准备重试...")
            raise
    
    try:
        content = _call_with_retry()
        yield content
    except Exception as e:
        trace_desc = "未知节点"
        if current_trace_info:
            trace_desc = current_trace_info.get("trace_desc", "未知节点")
        print(f"文本模型调用最终失败 - 节点: {trace_desc} | 错误: {str(e)}")
        raise ValueError(f"节点 {trace_desc} 的文本模型调用失败，请稍后重试") from e

@contextmanager
def manage_vision_llm_call(client: Union[ZhipuAI, OpenAI], messages: List[Dict], config: LessonPlanConfiguration, trace_desc: str = "未知节点", user_id: Optional[str] = None, session_id: Optional[str] = None) -> Generator[str, None, None]:
    """管理视觉LLM调用的上下文，包含重试机制和错误处理"""
    # 创建token统计服务
    token_service = create_token_service(config)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True
    )
    def _call_with_retry():
        try:
            print(f"视觉模型调用 - 节点: {trace_desc} | 模型: {config.vision_model} | 提供商: {config.vision_model_provider}")
            
            if config.vision_model_provider == "zhipuai":
                # 智谱 AI 调用
                response = client.chat.completions.create(
                    model=config.vision_model.split('/')[-1],
                    messages=messages
                )
                
                # 获取token使用信息并发送统计
                if hasattr(response, 'usage') and response.usage:
                    input_tokens = getattr(response.usage, 'prompt_tokens', 0)
                    output_tokens = getattr(response.usage, 'completion_tokens', 0)
                    
                    # 在后台发送token统计
                    if user_id and session_id and (input_tokens > 0 or output_tokens > 0):
                        # 获取实际的视觉模型名称
                        vision_model_name = config.vision_model.split('/')[-1] if '/' in config.vision_model else config.vision_model
                        token_service.send_token_consumption_background(
                            user_id=user_id,
                            session_id=session_id,
                            model_name=vision_model_name,
                            provider=config.vision_model_provider,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens
                        )
                
                return response.choices[0].message.content
            else:
                # OpenAI兼容接口调用（SiliconFlow和DashScope）
                response = client.chat.completions.create(
                    model=config.vision_model,
                    messages=messages,
                    stream=True,
                    modalities=["text"]  # DashScope需要这个参数
                )
                # 收集流式响应和token统计
                content = ""
                total_input_tokens = 0
                total_output_tokens = 0
                
                for chunk in response:
                    if chunk.choices:
                        if chunk.choices[0].delta.content:
                            content += chunk.choices[0].delta.content
                    
                    # 收集token使用信息
                    if hasattr(chunk, 'usage') and chunk.usage:
                        if hasattr(chunk.usage, 'prompt_tokens'):
                            total_input_tokens = chunk.usage.prompt_tokens
                        if hasattr(chunk.usage, 'completion_tokens'):
                            total_output_tokens = chunk.usage.completion_tokens
                
                # 发送token统计
                if user_id and session_id and (total_input_tokens > 0 or total_output_tokens > 0):
                    token_service.send_token_consumption_background(
                        user_id=user_id,
                        session_id=session_id,
                        model_name=config.vision_model,
                        provider=config.vision_model_provider,
                        input_tokens=total_input_tokens,
                        output_tokens=total_output_tokens
                    )
                
                return content
 
        except Exception as e:
            print(f"视觉模型调用失败 - 节点: {trace_desc} | 错误: {str(e)}, 准备重试...")
            raise
    
    try:
        content = _call_with_retry()
        yield content
    except Exception as e:
        print(f"视觉模型调用最终失败 - 节点: {trace_desc} | 错误: {str(e)}")
        raise ValueError(f"节点 {trace_desc} 的视觉模型调用失败，请稍后重试") from e 
    

def create_llm_client(config: LessonPlanConfiguration, streaming: bool = False) -> Union[CustomChatZhipuAI, CustomChatOpenAI]:
    """创建语言模型客户端"""
    if config.txt_model_provider == "zhipuai":
        return CustomChatZhipuAI(
            model_name=config.text_model.split('/')[-1],
            zhipuai_api_key=config.txt_api_key,
            streaming=streaming
        )
    elif config.txt_model_provider in ["siliconflow", "dashscope"]:
        return CustomChatOpenAI(
            model_name=config.text_model,
            api_key=config.txt_api_key,
            base_url=config.model_base_url
        )
    else:
        raise ValueError(f"不支持的文本模型提供商: {config.txt_model_provider}")
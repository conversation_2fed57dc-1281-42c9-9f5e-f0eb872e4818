# AI绘本生成MCP服务器

这是一个基于 **MCP (Model Context Protocol)** 的 **AI绘本生成服务器**，专门用于根据用户需求自动生成完整的图文绘本。

## 🎯 项目功能

- **智能故事创作**：根据绘本主题自动生成适合儿童的故事内容
- **精美图片生成**：为每个故事页面生成对应的插画
- **多样化风格**：支持卡通、水彩、油画、简笔画等多种绘本风格
- **MCP标准接口**：提供标准化的工具接口供AI助手调用

## 🏗️ 项目结构

```
picturebook-generator-mcp/
├── README.md                   # 项目说明
├── pyproject.toml              # 项目配置
├── main.py                     # MCP服务器入口
├── src/                        # 核心源码目录
│   └── picturebook_generator/
│       ├── __init__.py         # 模块初始化
│       ├── config.py           # 配置管理
│       ├── models.py           # 数据模型
│       ├── generators.py       # 核心生成逻辑
│       └── logger.py           # 日志配置
├── tests/                      # 测试目录
│   ├── __init__.py
│   └── test_mcp_server.py      # 完整测试套件
└── outputs/                    # 生成的绘本输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`generate_picture_book`工具函数
- **`src/picturebook_generator/generators.py`** - 核心绘本生成模块
  - `BaseArkAgent`: 基础Ark客户端类
  - `StoryGenerationAgent`: 故事生成智能体
  - `ImageGenerationAgent`: 图像生成智能体
  - `PictureBookGeneratorService`: 主服务类
- **`src/picturebook_generator/models.py`** - 数据模型定义
- **`src/picturebook_generator/config.py`** - 配置管理
- **`src/picturebook_generator/logger.py`** - 日志系统配置

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**: 豆包API (火山引擎)
  - 故事生成模型: `doubao-1-5-pro-32k-250115`
  - 图像生成模型: `doubao-seedream-3-0-t2i-250415`
- **数据验证**: Pydantic
- **环境配置**: python-dotenv

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export DOUBAO_API_KEY="your_doubao_api_key"
```

### 2. 配置选项

环境变量配置：
- `DOUBAO_API_KEY`: 豆包API密钥（必需）
- `DOUBAO_BASE_URL`: 豆包API基础URL（可选）
- `DOUBAO_STORY_MODEL`: 故事生成模型（可选）
- `DOUBAO_IMAGE_MODEL`: 图像生成模型（可选）
- `OUTPUT_DIR`: 图片输出根目录（可选，默认: 项目目录下的outputs文件夹）

### 3. 运行服务

```bash
# 启动MCP服务器（HTTP流模式）
python main.py
# 服务器将在 http://localhost:8080 启动

# 运行测试
python tests/test_mcp_server.py
```

## 📚 使用示例

### HTTP API访问

服务器启动后，可通过HTTP接口访问：
- **服务地址**: `http://localhost:8080`
- **传输模式**: HTTP Streamable
- **协议**: MCP (Model Context Protocol)

### MCP工具调用

```python
# 生成绘本
result = await generate_picture_book(
    book_title="小猫钓鱼",
    book_style="卡通",
    target_pages=5,
    book_theme="一个关于小猫学会钓鱼的温馨故事",
    user_requirements="画面要温馨可爱，适合3-6岁儿童"
)
```

### 参数说明

- `book_title`: 绘本标题
- `book_style`: 绘本风格（卡通、水彩、油画、简笔画等）
- `target_pages`: 目标页数
- `book_theme`: 绘本主题和描述（可选）
- `user_requirements`: 用户特殊要求（可选）

## 🎨 工作流程

项目的工作流程被设计为高度透明和实时，确保调用方能随时了解绘本的生成进度。

1.  **接收绘本需求** - MCP工具`generate_picture_book`接收绘本标题、风格、页数等信息。
2.  **启动实时通信** - 通过`fastmcp`的`Context`对象，开始向客户端进行实时状态推送。
3.  **生成故事脚本** - 调用`StoryGenerationAgent`与大语言模型交互。
    - **实时文本流**：故事文本会以数据流的形式实时返回 (`status: "story_generation_streaming"`)。
    - **用量报告**：文本生成后，立刻返回该次模型调用的Token用量信息 (`status: "model_usage"`)。
4.  **解析与循环生成** - 故事脚本被解析为独立的页面，服务开始循环为每一页生成插图。
5.  **生成并交付配图** - 对每一页，调用`ImageGenerationAgent`：
    - 图片被异步下载到本地服务器。
    - **同时提取图片的远程URL**。
    - **URL实时返回**：图片生成成功后，立刻通过`context.info`将包含**远程URL**的消息推送给客户端 (`status: "image_generation_completed"`)。
    - **用量报告**：同样，每次图片生成后都会立刻报告其模型用量。
6.  **输出最终结果** - 所有页面处理完毕后，返回一个完整的、结构化的图文绘本JSON对象。

## 📢 实时通知

在调用工具后，客户端可以通过监听服务端的SSE (Server-Sent Events) 来获取实时进度。所有的通知都通过`context.info()`以JSON格式发送。

关键的`status`类型包括：
- `story_generation_started`: 故事生成开始。
- `story_generation_streaming`: 正在流式传输故事文本。
- `story_generation_completed`: 故事生成完成。
- `image_generation_started`: 开始为某一页生成图片。
- `image_generation_completed`: 某一页的图片生成完成，消息体中会包含`image_urls`。
- `model_usage`: 一次模型调用完成，消息体中包含详细的用量数据。

## 📁 输出格式

### 生成内容

生成的绘本包含：
- **故事脚本**: 完整的故事文本。
- **页面列表**: 每个页面对象包含：
  - `page_id`: 页面ID
  - `page_title`: 页面标题
  - `page_content`: 用于生成图片的描述
  - `page_images`: **[本地路径]** 保存在服务器上的图片文件路径列表。
  - `page_image_urls`: **[远程URL]** 图片的公开访问URL列表。
  - `page_text`: 页面上的简短文字。
- **图片文件**: 保存在绘本专用的images目录中。
- **元数据文件**: 包含绘本信息、生成时间、故事脚本等完整信息。

## 🧪 测试

```bash
# 运行完整测试套件（包含直接服务测试和MCP客户端测试）
python tests/test_mcp_server.py
```

测试套件包含：
- **直接服务测试**: 直接测试绘本生成服务功能
- **MCP服务器测试**: 测试MCP协议交互

## 🌟 特色功能

- **智能分页**: 自动将故事分解为适合的页面数量
- **风格一致性**: 确保所有插画风格统一
- **儿童友好**: 专门针对儿童阅读优化的文本和画面
- **高度定制**: 支持用户自定义风格和要求
- **批量生成**: 一次调用生成完整绘本


"""AI绘本生成服务器数据模型"""
from typing import List, Optional
from pydantic import BaseModel, Field


class PictureBookInfo(BaseModel):
    """封装用户请求的绘本基本信息"""
    book_title: str = Field(description="绘本标题")
    book_style: str = Field(description="绘本的艺术风格，例如：卡通、水彩")
    target_pages: int = Field(description="期望生成的绘本总页数")
    book_theme: Optional[str] = Field(
        default="", 
        description="绘本的整体主题或核心思想"
    )
    user_requirements: Optional[str] = Field(
        default="", 
        description="用户提出的其他具体或特殊要求"
    )


class BookPage(BaseModel):
    """代表单个绘本页面的数据结构"""
    page_id: str = Field(description="页面的唯一标识符，通常是页码")
    page_title: str = Field(description="本页的小标题")
    page_content: str = Field(description="用于指导AI绘画的详细画面描述")
    page_images: Optional[List[str]] = Field(
        default=None, 
        description="页面图片本地路径列表"
    )
    page_image_urls: Optional[List[str]] = Field(
        default=None, 
        description="页面图片URL列表"
    )
    page_text: Optional[str] = Field(
        default=None, 
        description="最终呈现在本页上的简短故事文字"
    )

class ModelUsageInfo(BaseModel):
    """记录单次模型调用的用量信息"""
    vendor: str = Field(description="提供模型的厂商，例如：doubao, zhipu")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")

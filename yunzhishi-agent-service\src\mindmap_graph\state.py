"""思维导图生成图的状态管理。

本模块定义了思维导图生成图中使用的状态结构。
包括输入状态、处理状态、输出状态和路由分类模式的定义。
"""
from typing import Dict, List, Optional, Any, TypedDict, Annotated
import operator
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

class MindMapInfo(BaseModel):
    """思维导图信息"""
    mindmap_topic: str = Field(description="主题")
    mindmap_description: Optional[str] = Field(default=None, description="详细描述")
    mindmap_requirements: Optional[str] = Field(default=None, description="生成要求")
    pdf_content: Optional[str] = Field(default=None, description="PDF内容")
    pdf_path: Optional[str] = Field(default=None, description="PDF文件路径")
    pdf_temp_path: Optional[str] = Field(default=None, description="PDF临时文件路径")
    pdf_start_page: Optional[int] = Field(default=None, description="PDF起始页码")
    pdf_end_page: Optional[int] = Field(default=None, description="PDF结束页码")
    pdf_type: str = Field(default="text", description="PDF处理类型，text或vision")
    
    def __or__(self, other: 'MindMapInfo') -> 'MindMapInfo':
        """实现思维导图信息的合并操作
        
        合并规则：保留最新的非空值
        """
        return MindMapInfo(
            mindmap_topic=other.mindmap_topic or self.mindmap_topic,
            mindmap_description=other.mindmap_description or self.mindmap_description,
            mindmap_requirements=other.mindmap_requirements or self.mindmap_requirements,
            pdf_content=other.pdf_content or self.pdf_content,
            pdf_path=other.pdf_path or self.pdf_path,
            pdf_temp_path=other.pdf_temp_path or self.pdf_temp_path,
            pdf_start_page=other.pdf_start_page or self.pdf_start_page,
            pdf_end_page=other.pdf_end_page or self.pdf_end_page,
            pdf_type=other.pdf_type or self.pdf_type
        )

class Router(BaseModel):
    """路由状态模型"""
    stage: str = Field(description="当前阶段")
    status: str = Field(description="状态描述")
    error: Optional[str] = Field(None, description="错误信息")
    
    def __or__(self, other: 'Router') -> 'Router':
        """实现路由状态的合并操作
        
        合并规则：
        1. 如果任一状态有错误，保留错误状态
        2. 否则保留最新的状态
        """
        if self.error:
            return self
        if other.error:
            return other
        return other

class MindMapInput(TypedDict):
    """思维导图输入状态"""
    messages: List[HumanMessage]

class MindMapOutput(TypedDict):
    """思维导图输出状态"""
    mindmap_markdown: str  # Markdown格式的思维导图
    mindmap_xmind: Optional[str]  # XMind格式的思维导图文件路径

class MindMapState(TypedDict):
    """思维导图状态"""
    messages: List[HumanMessage]
    user_id: Optional[str]
    session_id: Optional[str]
    router: Annotated[Router, operator.or_]
    mindmap_info: Annotated[Optional[MindMapInfo], operator.or_]
    mindmap_markdown: Optional[str]  # Markdown格式的思维导图
    mindmap_xmind: Optional[str]  # XMind格式的思维导图文件路径

def create_initial_state() -> MindMapState:
    """创建初始状态
    
    Returns:
        MindMapState: 包含所有必要字段的初始状态字典
    """
    return {
        "messages": [],
        "user_id": None,
        "session_id": None,
        "router": Router(
            stage="preprocess_info",
            status="开始预处理信息"
        ),
        "mindmap_info": MindMapInfo(
            mindmap_topic="",
            mindmap_description=None,
            mindmap_requirements=None,
            pdf_content=None,
            pdf_path=None,
            pdf_temp_path=None,
            pdf_start_page=None,
            pdf_end_page=None,
            pdf_type="text"
        ),
        "mindmap_markdown": None,
        "mindmap_xmind": None
    } 
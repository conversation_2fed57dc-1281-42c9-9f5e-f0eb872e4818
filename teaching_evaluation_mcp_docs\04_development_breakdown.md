# 开发任务分解（更新版）

## 1. 开发阶段概述

基于用户反馈，对开发计划进行以下调整：
- **简化提示词管理**：移除学科识别功能
- **添加雷达图生成**：集成Plotly图表功能
- **标准化模型用量**：移除cost_yuan计算

### 阶段一：基础框架搭建（预计2-3天）
- 项目结构创建
- 基础配置和依赖管理
- 日志系统实现
- 基础数据模型定义（更新ModelUsageInfo）

### 阶段二：AI集成与提示词管理（预计2天，简化）
- DashScope客户端集成
- **简化的**提示词管理系统（无学科识别）
- AI服务封装
- 标准模型用量统计（无成本计算）

### 阶段三：核心业务逻辑（预计2-3天）
- 教学分析服务实现
- **新增**：雷达图生成模块（Plotly）
- 输出管理和报告生成
- 错误处理和异常管理
- 实时进度报告

### 阶段四：MCP服务器实现（预计2天）
- FastMCP服务器配置
- 工具函数实现
- 接口测试和调试

### 阶段五：测试与优化（预计2-3天）
- 单元测试编写
- 集成测试（包含雷达图生成测试）
- 性能优化
- 文档完善

## 2. 详细任务分解

### 2.1 阶段一：基础框架搭建

#### 任务1.1：项目结构创建（0.5天）
**目标**：建立标准的项目目录结构
**具体任务**：
- 创建主要目录结构
- 初始化 `__init__.py` 文件
- 创建 `pyproject.toml` 配置文件
- 设置 `.env.example` 环境变量模板
- 创建 `.gitignore` 文件

**验收标准**：
- 项目结构符合设计规范
- 所有必要的目录和文件已创建
- 环境变量配置正确

#### 任务1.2：依赖管理配置（0.5天）
**目标**：配置项目依赖和开发环境
**具体任务**：
- 配置 `pyproject.toml` 中的依赖项
- 安装核心依赖：`fastmcp`, `pydantic`, `python-dotenv`, `httpx`
- **新增**：安装 `plotly` 和 `kaleido`（用于雷达图生成）
- 安装开发依赖：`pytest`, `black`, `ruff`
- 验证依赖安装正确性

**验收标准**：
- 所有依赖正确安装
- 开发环境可正常运行
- 代码格式化工具配置完成

#### 任务1.3：日志系统实现（1天）
**目标**：实现完整的日志记录系统
**具体任务**：
- 创建 `src/teaching_evaluation/logger.py`
- 实现 `TeachingEvaluationLogger` 类
- 配置文件和控制台双重输出
- 实现日志轮转和级别控制
- 编写日志系统测试

**验收标准**：
- 日志系统正常工作
- 支持多级别日志输出
- 文件轮转功能正常
- 测试覆盖率达到90%以上

#### 任务1.4：基础数据模型定义（1天）
**目标**：定义核心的Pydantic数据模型
**具体任务**：
- 创建 `src/teaching_evaluation/models.py`
- 实现 `TeachingAnalysisRequest` 模型
- 实现 `TeachingAnalysisResponse` 模型
- 实现 `ProgressUpdate` 模型
- 实现 `ModelUsageInfo` 模型（**更新**：移除cost_yuan字段，遵循参考项目标准）
- 编写模型验证测试

**验收标准**：
- 所有数据模型定义完整
- 字段验证规则正确
- 模型序列化/反序列化正常
- 测试覆盖率达到95%以上

### 2.2 阶段二：AI集成与提示词管理

#### 任务2.1：配置管理实现（0.5天）
**目标**：实现应用配置管理系统
**具体任务**：
- 创建 `src/teaching_evaluation/config.py`
- 实现 `TeachingEvaluationConfig` 类
- 配置环境变量读取
- 实现配置验证和默认值
- 编写配置测试

**验收标准**：
- 配置系统正常工作
- 环境变量正确读取
- 配置验证功能完整
- 测试覆盖率达到90%以上

#### 任务2.2：DashScope客户端集成（1天）
**目标**：集成DashScope API客户端
**具体任务**：
- 创建 `src/teaching_evaluation/ai_client.py`
- 实现 `DashScopeClient` 类
- 配置API认证和请求处理
- 实现错误处理和重试机制
- 编写客户端测试（使用Mock）

**验收标准**：
- API客户端正常工作
- 错误处理机制完善
- 支持异步请求
- 测试覆盖率达到85%以上

#### 任务2.3：提示词管理系统（1天）
**目标**：实现提示词模板管理
**具体任务**：
- 创建 `src/teaching_evaluation/prompt_manager.py`
- 实现 `PromptManager` 类
- 实现提示词模板加载和格式化
- 实现学科识别功能
- 编写提示词管理测试

**验收标准**：
- 提示词模板正确加载
- 动态参数替换正常
- 学科识别准确率高
- 测试覆盖率达到90%以上

#### 任务2.4：教学分析服务实现（1天）
**目标**：实现完整的教学分析服务（与设计文档保持一致）
**具体任务**：
- 创建 `src/teaching_evaluation/analyzer.py`
- 实现 `TeachingAnalyzer` 类
- 集成AI客户端、提示词管理和雷达图生成
- 实现完整的分析流程和实时进度报告
- 实现用量统计和错误处理功能
- 编写完整的服务测试

**验收标准**：
- 完整分析流程正常（包含图表生成）
- 实时进度报告正常
- 用量统计准确
- 错误处理机制完善
- 测试覆盖率达到90%以上

### 2.3 阶段三：图表生成与MCP接口

#### 任务3.1：雷达图生成模块（1天）
**目标**：实现基于Plotly的雷达图生成功能
**具体任务**：
- 创建 `src/teaching_evaluation/chart_generator.py`
- 实现 `RadarChartGenerator` 类
- 实现评分提取算法（从AI分析文本中提取五维度评分）
- 实现Plotly雷达图生成（HTML和PNG格式）
- 实现图表样式配置和优化
- 编写雷达图生成测试

**验收标准**：
- 能够正确提取五维度评分
- 生成的雷达图样式美观、数据准确
- 支持HTML和PNG两种输出格式
- 测试覆盖率达到90%以上

#### 任务3.2：MCP工具接口实现（0.5天）
**目标**：实现MCP服务器的工具接口
**具体任务**：
- 创建 `main.py` MCP服务器入口
- 实现 `analyze_teaching_transcript` MCP工具
- 集成 `TeachingAnalyzer` 服务
- 实现完整的MCP通信流程
- 编写MCP客户端测试

**验收标准**：
- MCP工具接口正常工作
- 实时通信功能正常
- 错误处理通过MCP协议正确传递
- 客户端测试通过

#### 任务3.3：错误处理和异常管理（0.5天）
**目标**：完善全局错误处理机制
**具体任务**：
- 创建 `src/teaching_evaluation/exceptions.py`
- 定义自定义异常类
- 实现全局异常处理器
- 完善错误日志记录
- 编写异常处理测试

**验收标准**：
- 异常分类清晰
- 错误信息详细准确
- 日志记录完整
- 测试覆盖率达到95%以上

#### 任务3.4：输出格式化和元数据生成（0.5天）
**目标**：实现结构化输出和元数据管理
**具体任务**：
- 创建 `src/teaching_evaluation/output_formatter.py`
- 实现 `OutputFormatter` 类
- 实现Markdown和JSON格式输出
- 实现元数据文件生成
- 编写格式化测试

**验收标准**：
- 输出格式规范正确
- 元数据信息完整
- 支持多种输出格式
- 测试覆盖率达到90%以上

### 2.4 阶段四：MCP服务器实现

#### 任务4.1：MCP服务器配置（0.5天）
**目标**：配置FastMCP服务器
**具体任务**：
- 创建 `main.py` 入口文件
- 配置FastMCP应用
- 实现服务器启动逻辑
- 配置日志和错误处理
- 编写服务器配置测试

**验收标准**：
- 服务器正常启动
- 配置参数正确
- 日志输出正常
- 测试覆盖率达到85%以上

#### 任务4.2：工具函数实现（1天）
**目标**：实现MCP工具函数
**具体任务**：
- 实现 `analyze_teaching_transcript` 工具
- 集成核心业务逻辑
- 实现参数验证和错误处理
- 实现实时状态报告
- 编写工具函数测试

**验收标准**：
- 工具函数正常工作
- 参数验证完整
- 实时状态报告正常
- 测试覆盖率达到90%以上

#### 任务4.3：接口测试和调试（0.5天）
**目标**：测试MCP接口功能
**具体任务**：
- 编写MCP客户端测试脚本
- 测试完整的调用流程
- 验证实时通信功能
- 调试和修复问题
- 编写集成测试

**验收标准**：
- 接口调用正常
- 实时通信稳定
- 错误处理正确
- 集成测试通过

### 2.5 阶段五：测试与优化

#### 任务5.1：单元测试完善（1天）
**目标**：完善所有模块的单元测试
**具体任务**：
- 补充缺失的单元测试
- 提高测试覆盖率到90%以上
- 实现测试数据管理
- 配置测试自动化
- 编写测试文档

**验收标准**：
- 测试覆盖率达到90%以上
- 所有测试用例通过
- 测试数据管理规范
- 测试文档完整

#### 任务5.2：集成测试（1天）
**目标**：实现端到端的集成测试
**具体任务**：
- 编写完整流程测试
- 测试多种输入场景
- 验证输出质量（包含雷达图生成验证）
- **新增**：测试雷达图生成的准确性和格式
- **新增**：验证HTML和PNG文件的生成
- 测试错误处理场景
- 编写性能测试

**验收标准**：
- 集成测试全部通过
- 覆盖主要使用场景（包含图表生成场景）
- 雷达图生成功能正常
- 性能指标达标
- 错误处理验证完成

#### 任务5.3：性能优化（0.5天）
**目标**：优化系统性能
**具体任务**：
- 分析性能瓶颈
- 优化AI调用效率
- 优化文本处理速度
- 优化内存使用
- 编写性能测试报告

**验收标准**：
- 响应时间符合要求
- 内存使用合理
- 并发处理能力达标
- 性能测试报告完整

#### 任务5.4：文档完善（0.5天）
**目标**：完善项目文档
**具体任务**：
- 更新README.md
- 编写API文档
- 编写部署指南
- 编写用户手册
- 编写开发者指南

**验收标准**：
- 文档内容完整准确
- 示例代码可运行
- 部署指南清晰
- 用户手册易懂

## 3. 开发规范和质量控制

### 3.1 代码规范
- 严格遵循PEP 8编码规范
- 使用black进行代码格式化
- 使用ruff进行代码检查
- 所有函数和类必须有完整的docstring

### 3.2 测试规范
- 每个模块的测试覆盖率不低于85%
- 核心业务逻辑测试覆盖率不低于90%
- 所有测试用例必须可重复执行
- 使用pytest作为测试框架

### 3.3 版本控制
- 使用语义化版本控制
- 每个功能完成后提交代码
- 重要节点创建标签
- 编写清晰的提交信息

### 3.4 质量检查点
- 每个阶段结束后进行代码审查
- 运行完整的测试套件
- 检查文档完整性
- 验证功能正确性

## 4. 风险控制和应对策略

### 4.1 技术风险
**风险**：DashScope API调用失败或限流
**应对策略**：
- 实现重试机制和指数退避
- 添加API调用监控和告警
- 准备备用AI服务方案

**风险**：长文本处理性能问题
**应对策略**：
- 实现文本分块处理机制
- 添加进度条和取消功能
- 优化内存使用策略

### 4.2 进度风险
**风险**：开发进度延期
**应对策略**：
- 每日进度跟踪和调整
- 优先实现核心功能
- 预留缓冲时间

**风险**：测试时间不足
**应对策略**：
- 开发过程中同步编写测试
- 使用自动化测试工具
- 重点测试核心功能

### 4.3 质量风险
**风险**：AI分析质量不稳定
**应对策略**：
- 多轮提示词优化
- 实现结果质量评估
- 添加人工审核机制

**风险**：系统稳定性问题
**应对策略**：
- 完善错误处理机制
- 实现健康检查功能
- 添加监控和日志

## 5. 交付物清单

### 5.1 代码交付物
- [x] 完整的源代码（src/teaching_evaluation/目录）
  - [x] config.py - 配置管理
  - [x] models.py - 数据模型
  - [x] logger.py - 日志系统
  - [x] ai_client.py - AI客户端
  - [x] prompt_manager.py - 提示词管理
  - [x] chart_generator.py - 雷达图生成
  - [x] analyzer.py - 教学分析服务（重命名后）
- [x] 配置文件（pyproject.toml, .env.example）
- [ ] 入口文件（main.py）
- [x] 测试代码（tests/目录）

### 5.2 文档交付物
- [ ] 项目README.md
- [ ] API文档
- [ ] 部署指南
- [ ] 用户手册
- [ ] 开发者指南

### 5.3 测试交付物
- [ ] 单元测试报告
- [ ] 集成测试报告
- [ ] 性能测试报告
- [ ] 测试覆盖率报告

### 5.4 部署交付物
- [ ] Docker配置文件（可选）
- [ ] 部署脚本
- [ ] 环境配置说明
- [ ] 运维手册

## 6. 后续维护计划

### 6.1 短期维护（1-3个月）
- 修复发现的bug
- 优化性能问题
- 完善文档和示例
- 收集用户反馈

### 6.2 中期优化（3-6个月）
- 添加新的分析维度
- 支持更多文件格式
- 实现批量处理功能
- 添加可视化报告

### 6.3 长期发展（6个月以上）
- 支持多语言分析
- 集成更多AI模型
- 实现Web界面
- 添加协作功能

"""教案分析生成的处理器实现。"""
from typing import Dict, List, Optional, TypedDict, Any, Union
import os
import time
import re
import base64
from shared.utils import File<PERSON>, OSSStorageManager
from shared.decorators import traceable
from shared.configuration import LessonPlanConfiguration
from plananalysis_graph.state import PlanInfo
from shared.model import create_llm_client, manage_txt_llm_call, manage_vision_llm_call
from contextlib import contextmanager
from typing import Generator
import fitz  # PyMuPDF
from pathlib import Path
from plananalysis_graph.prompts import (
    PLAN_ANALYSIS_SYSTEM_PROMPT,
    PLAN_VISION_ANALYSIS_SYSTEM_PROMPT
)

# 全局变量，用于存储文件夹路径
_analysis_folder = None

def get_analysis_folder(lesson_plan_short: str, config: LessonPlanConfiguration = None) -> str:
    """获取或创建存储教案分析的文件夹路径。
    
    Args:
        lesson_plan_short: 教案简短描述，用于命名文件夹
        config: 配置对象，用于获取output_dir
        
    Returns:
        str: 文件夹路径
    """
    global _analysis_folder
    if _analysis_folder is None:
        # 截取教案描述的前20个字符作为文件夹名称的一部分
        plan_info_short = lesson_plan_short[:20].replace(" ", "_").replace("/", "_")
        timestamp = int(time.time())
        folder_name = f"analysis_{timestamp}_{plan_info_short}"
        
        # 创建文件夹在output_dir下
        base_dir = os.getcwd()
        if config and config.output_dir:
            base_dir = config.output_dir
            
        _analysis_folder = os.path.join(base_dir, folder_name)
        if not os.path.exists(_analysis_folder):
            os.makedirs(_analysis_folder)
    
    return _analysis_folder

def format_plan_info(plan_info: PlanInfo) -> str:
    """格式化教案信息供提示词使用"""
    personal_requirements = ""
    if plan_info.personal_requirements:
        personal_requirements = f"个性化要求：\n{plan_info.personal_requirements}"
    return personal_requirements


class FileProcessAgent:
    """文件处理智能体，负责处理PDF文件并提取内容"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.storage = OSSStorageManager(config)
        self.pdf_cache = {}  # 用于缓存PDF临时文件路径
    
    @contextmanager
    def _manage_uploaded_file(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Generator[str, None, None]:
        """管理上传的文件生命周期，确保文件被正确删除"""
        # 如果指定了页面范围，创建临时PDF文件
        temp_pdf_path = pdf_path
        if start_page is not None and end_page is not None:
            with fitz.open(pdf_path) as doc:
                # 验证页码范围
                if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                    raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                
                # 创建新的PDF文档
                new_doc = fitz.open()
                for i in range(start_page - 1, end_page):
                    new_doc.insert_pdf(doc, from_page=i, to_page=i)
                
                # 保存临时文件
                temp_pdf_path = pdf_path.replace('.pdf', f'_temp_{start_page}_{end_page}.pdf')
                new_doc.save(temp_pdf_path)
                new_doc.close()
        
        try:
            yield temp_pdf_path
        finally:
            # 如果创建了临时文件，删除它
            if temp_pdf_path != pdf_path:
                try:
                    os.remove(temp_pdf_path)
                except Exception as e:
                    print(f"清理临时文件失败: {str(e)}")
    
    @traceable
    def process_pdf(self, object_name: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Dict[str, str]:
        """处理PDF文件并提取内容
        
        Args:
            object_name: PDF文件名
            start_page: 起始页码（从1开始）
            end_page: 结束页码（包含）
            
        Returns:
            Dict[str, str]: 包含处理后的内容和类型
        """
        # 确保临时目录存在
        if not os.path.exists(self.config.temp_dir):
            os.makedirs(self.config.temp_dir, exist_ok=True)
            print(f"已创建临时目录: {self.config.temp_dir}")
        
        # 生成缓存键
        cache_key = f"{object_name}_{start_page or ''}_{end_page or ''}"
        
        # 检查是否已有缓存的临时文件
        if cache_key in self.pdf_cache and os.path.exists(self.pdf_cache[cache_key]):
            temp_path = self.pdf_cache[cache_key]
            # 使用缓存的临时文件
            with self._manage_uploaded_file(temp_path, start_page, end_page) as temp_file_path:
                # 尝试提取文本内容
                pdf_content = ""
                with fitz.open(temp_file_path) as doc:
                    for page in doc:
                        pdf_content += page.get_text()
                
                result = {
                    "pdf_content": pdf_content.strip(),
                    "pdf_type": "text" if pdf_content.strip() else "vision",
                    "pdf_path": object_name
                }
                
                # 如果是视觉类型的PDF，将临时文件路径添加到结果中
                if result["pdf_type"] == "vision":
                    # 复制临时文件到可持久保存的位置
                    persistent_temp_path = temp_path
                    result["pdf_temp_path"] = persistent_temp_path
                
                return result
        
        # 从OSS下载或使用本地文件
        try:
            with self.storage.get_temp_file(object_name) as temp_path:
                with self._manage_uploaded_file(temp_path, start_page, end_page) as temp_file_path:
                    # 尝试提取文本内容
                    pdf_content = ""
                    with fitz.open(temp_file_path) as doc:
                        for page in doc:
                            pdf_content += page.get_text()
                    
                    result = {
                        "pdf_content": pdf_content.strip(),
                        "pdf_type": "text" if pdf_content.strip() else "vision",
                        "pdf_path": object_name
                    }
                    
                    # 如果是视觉类型的PDF，保留临时文件
                    if result["pdf_type"] == "vision":
                        # 复制临时文件到可持久保存的位置
                        persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                        import shutil
                        shutil.copy2(temp_file_path, persistent_temp_path)
                        # 缓存临时文件路径
                        self.pdf_cache[cache_key] = persistent_temp_path
                        result["pdf_temp_path"] = persistent_temp_path
                    
                    return result
        except Exception as e:
            # 如果是本地文件，直接处理
            if os.path.exists(object_name):
                with self._manage_uploaded_file(object_name, start_page, end_page) as temp_file_path:
                    # 尝试提取文本内容
                    pdf_content = ""
                    with fitz.open(temp_file_path) as doc:
                        for page in doc:
                            pdf_content += page.get_text()
                    
                    result = {
                        "pdf_content": pdf_content.strip(),
                        "pdf_type": "text" if pdf_content.strip() else "vision",
                        "pdf_path": object_name
                    }
                    
                    # 如果是视觉类型的PDF，保留临时文件
                    if result["pdf_type"] == "vision":
                        # 确保临时目录存在
                        if not os.path.exists(self.config.temp_dir):
                            os.makedirs(self.config.temp_dir, exist_ok=True)
                            print(f"已创建临时目录: {self.config.temp_dir}")
                            
                        # 复制临时文件到可持久保存的位置
                        persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                        import shutil
                        shutil.copy2(temp_file_path, persistent_temp_path)
                        # 缓存临时文件路径
                        self.pdf_cache[cache_key] = persistent_temp_path
                        result["pdf_temp_path"] = persistent_temp_path
                    
                    return result
            else:
                raise ValueError(f"无法处理PDF文件: {object_name}, 错误: {str(e)}")


class PlanAnalysisTxtGenerateAgent:
    """教案分析生成智能体，负责生成教案分析"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_plan_analysis(self, plan_info: PlanInfo) -> str:
        """生成教案分析。
        
        Args:
            plan_info: 教案信息对象
            
        Returns:
            str: 生成的教案分析内容
        """
        # 格式化个性化要求
        personal_requirements_formatted = format_plan_info(plan_info)
        
        # 准备提示词，根据是否有PDF进行调整
        lesson_plan_content = plan_info.lesson_plan or ""
        pdf_content = plan_info.pdf_content or ""
        
        # 比较教案内容和PDF内容的长度，选择更长的内容
        if lesson_plan_content and pdf_content:
            if len(pdf_content) > len(lesson_plan_content):
                lesson_plan_content = f"PDF内容：\n{pdf_content}"
            # 如果教案内容更长或相等，则保持使用教案内容
        elif pdf_content:  # 只有PDF内容
            lesson_plan_content = f"PDF内容：\n{pdf_content}"
        # 如果只有教案内容或两者都没有，则使用现有的lesson_plan_content
        
        # 准备提示词
        prompt = PLAN_ANALYSIS_SYSTEM_PROMPT.format(
            lesson_plan=lesson_plan_content,
            personal_requirements_formatted=personal_requirements_formatted
        )
        
        self.llm.push_trace_info("STR_00", "教案分析生成")
        
        with manage_txt_llm_call(self.llm, prompt, self.config, None, None) as plan_analysis:
            # 使用自定义文件夹路径
            # 从教案内容中提取前20个字符作为文件名的一部分
            lesson_plan_short = (plan_info.lesson_plan or plan_info.pdf_path or "")[:20]
            file_path = os.path.join(get_analysis_folder(lesson_plan_short, self.config), "plan_analysis.md")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(plan_analysis)
                
            return plan_analysis


class PlanAnalysisVisionGenerateAgent:
    """视觉教案分析生成智能体，负责处理PDF图像并生成教案分析"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
        # 为视觉处理创建直接的模型客户端
        if config.vision_model_provider == "zhipuai":
            import os
            from zhipuai import ZhipuAI
            zhipuai_key = os.getenv("ZHIPUAI_API_KEY") or config.vision_api_key
            self.vision_client = ZhipuAI(api_key=zhipuai_key)
        elif config.vision_model_provider in ["siliconflow", "dashscope"]:
            from openai import OpenAI
            self.vision_client = OpenAI(
                api_key=config.vision_api_key,
                base_url=config.model_base_url
            )
        else:
            raise ValueError(f"不支持的视觉模型提供商: {config.vision_model_provider}")
    
    def _convert_pdf_to_images(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> List[str]:
        """将PDF转换为图像文件"""
        img_paths = []
        
        with fitz.open(pdf_path) as doc:
            # 设置默认页面范围
            start_idx = 0 if start_page is None else max(0, start_page - 1)
            end_idx = doc.page_count - 1 if end_page is None else min(doc.page_count - 1, end_page - 1)
            
            # 限制最多处理5页
            max_pages = 5
            if end_idx - start_idx + 1 > max_pages:
                end_idx = start_idx + max_pages - 1
                     
            # 创建临时目录
            temp_dir = os.path.join(self.config.temp_dir, f"pdf_images_{int(time.time())}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 转换每页为图像
            for page_idx in range(start_idx, end_idx + 1):
                page = doc[page_idx]
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 使用2倍分辨率
                img_path = os.path.join(temp_dir, f"page_{page_idx+1}.png")
                pix.save(img_path)
                img_paths.append(img_path)
        
        return img_paths
    
    def _encode_image(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    @traceable
    def generate_plan_analysis(self, plan_info: PlanInfo) -> str:
        """从PDF图像生成教案分析。"""
        if not plan_info.pdf_temp_path:
            raise ValueError("没有提供有效的PDF文件路径")
        
        cached_pdf_path = plan_info.pdf_temp_path
        img_paths = []
        
        try:
            # 转换PDF为图像
            img_paths = self._convert_pdf_to_images(
                plan_info.pdf_temp_path,
                plan_info.pdf_start_page,
                plan_info.pdf_end_page
            )
            
            # 格式化个性化要求
            personal_requirements_formatted = format_plan_info(plan_info)
            
            # 准备视觉提示
            messages = [
                {"role": "system", "content": PLAN_VISION_ANALYSIS_SYSTEM_PROMPT.format(
                    personal_requirements_formatted=personal_requirements_formatted,
                    lesson_plan=plan_info.lesson_plan or ""
                )},
                {"role": "user", "content": [
                    {"type": "text", "text": "这是教案资料的图像，请根据这些资料生成教案分析。"},
                    *[{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self._encode_image(img_path)}"}} for img_path in img_paths]
                ]}
            ]
            self.llm.push_trace_info("STR_00", "教案分析")
            # 使用manage_vision_llm_call和直接的视觉客户端
            with manage_vision_llm_call(self.vision_client, messages, self.config, "视觉教案分析生成", None, None) as plan_analysis:
                # 保存结果
                lesson_plan_short = (plan_info.lesson_plan or plan_info.pdf_path or "")[:20]
                file_path = os.path.join(get_analysis_folder(lesson_plan_short, self.config), "plan_analysis_vision.md")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(plan_analysis)
                
                return plan_analysis
        finally:
            # 清理临时图像文件
            if img_paths and os.path.exists(os.path.dirname(img_paths[0])):
                try:
                    import shutil
                    shutil.rmtree(os.path.dirname(img_paths[0]))
                    print(f"已清理临时图像文件夹: {os.path.dirname(img_paths[0])}")
                except Exception as e:
                    print(f"清理临时图像文件夹失败: {str(e)}")
            
            # 清理缓存的临时PDF文件
            if cached_pdf_path and os.path.exists(cached_pdf_path):
                try:
                    # 先关闭所有可能打开的文件句柄
                    import gc
                    gc.collect()  # 强制垃圾回收
                    
                    # 尝试直接删除文件
                    os.remove(cached_pdf_path)
                    print(f"已清理缓存的临时PDF文件: {cached_pdf_path}")
                    
                    # 清理映射信息中的临时路径
                    plan_info.pdf_temp_path = None
                except Exception as e:
                    print(f"清理缓存的临时PDF文件失败: {cached_pdf_path}, 错误: {str(e)}") 
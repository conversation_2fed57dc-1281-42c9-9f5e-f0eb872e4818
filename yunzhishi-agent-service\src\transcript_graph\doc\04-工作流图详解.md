# 工作流图详解

## 概述

Transcript Graph 的工作流图是基于 LangGraph 构建的状态机，它定义了整个教学逐字稿生成的完整流程。工作流采用有向无环图（DAG）结构，支持条件分支、并行处理和错误处理，确保了系统的高效性和可靠性。

## 工作流架构

### 核心组件

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def create_graph(config: Optional[Dict[str, Any]] = None):
    """创建教学逐字稿生成图"""
    # 创建工作流程图，传入配置模式
    workflow = StateGraph(TranscriptState, ConfigSchema, input=TranscriptInput, output=TranscriptOutput)
    
    # 添加工作节点
    workflow.add_node("process_info", process_info)
    workflow.add_node("generate_txt_teaching_process", generate_txt_teaching_process)
    workflow.add_node("generate_vision_teaching_process", generate_vision_teaching_process)
    workflow.add_node("STR_00_generate_transcript_section", generate_transcript_section)
    workflow.add_node("merge_transcript_sections", merge_transcript_sections)
````
</augment_code_snippet>

### 配置模式

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]   # 文本模型提供商
    vision_model_provider: Optional[str]   # 视觉模型提供商
    text_model: Optional[str]   # 文本模型名称
    vision_model: Optional[str]   # 视觉模型名称
    temp_dir: Optional[str]   # 临时文件目录
    output_dir: Optional[str]   # 输出目录
````
</augment_code_snippet>

## 节点详解

### 1. process_info - 信息处理节点

**功能**：解析用户输入，提取课程信息和处理要求

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def process_info(state: TranscriptState) -> Dict[str, Any]:
    """处理输入信息，解析课程信息"""
    with manage_state_update("process_info", "处理输入信息") as state_update:
        # 获取用户消息
        messages = state.get("messages", [])
        if not messages:
            raise ValueError("没有提供输入消息")
        
        # 解析消息内容
        message_content = get_message_content(messages[0])
        
        # 解析课程信息
        course_info = file_process_agent.parse_course_info(message_content)
        
        # 更新状态
        state_update["course_info"] = course_info
````
</augment_code_snippet>

**处理流程**：
1. 验证输入消息的存在性
2. 提取消息内容（支持文本和文件）
3. 调用 `FileProcessAgent` 解析课程信息
4. 处理 PDF 文件（如果存在）
5. 更新状态并设置路由信息

**输出**：更新后的 `CourseInfo` 对象

### 2. generate_txt_teaching_process - 文本教学流程生成节点

**功能**：基于文本信息生成教学流程大纲

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def generate_txt_teaching_process(state: TranscriptState) -> Dict[str, Any]:
    """生成文本教学流程"""
    with manage_state_update("generate_txt_teaching_process", "生成文本教学流程") as state_update:
        # 确保课程信息存在
        if not state.get("course_info"):
            raise ValueError("缺少课程信息，无法生成教学流程")
        
        # 调用智能体生成教学流程
        teaching_process_content = teaching_process_txt_generate_agent.generate_teaching_process(
            state["course_info"]
        )
        
        # 更新状态
        state_update["teaching_process"] = TeachingProcess(content=teaching_process_content)
````
</augment_code_snippet>

**处理流程**：
1. 验证课程信息的完整性
2. 调用 `TeachingProcessTxtGenerateAgent` 生成教学流程
3. 将生成的内容封装为 `TeachingProcess` 对象
4. 更新状态和路由信息

**输出**：包含教学流程内容的 `TeachingProcess` 对象

### 3. generate_vision_teaching_process - 视觉教学流程生成节点

**功能**：基于 PDF 图像生成教学流程大纲

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def generate_vision_teaching_process(state: TranscriptState) -> Dict[str, Any]:
    """生成视觉教学流程"""
    with manage_state_update("generate_vision_teaching_process", "生成视觉教学流程") as state_update:
        # 确保课程信息存在
        if not state.get("course_info"):
            raise ValueError("缺少课程信息，无法生成教学流程")
        
        # 调用视觉智能体生成教学流程
        teaching_process_content = teaching_process_vision_generate_agent.generate_teaching_process(
            state["course_info"]
        )
        
        # 更新状态
        state_update["teaching_process"] = TeachingProcess(content=teaching_process_content)
````
</augment_code_snippet>

**处理流程**：
1. 验证课程信息和 PDF 文件的存在性
2. 调用 `TeachingProcessVisionGenerateAgent` 处理 PDF
3. 将 PDF 转换为图像序列
4. 使用视觉模型分析图像内容
5. 生成教学流程并更新状态

**特色功能**：
- 支持指定页面范围处理
- 自动清理临时图像文件
- 多模态输入处理

### 4. STR_00_generate_transcript_section - 逐字稿片段生成节点

**功能**：并行生成各个教学环节的详细逐字稿

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def generate_transcript_section(state: TranscriptState) -> Dict[str, Any]:
    """生成单个教学环节的逐字稿"""
    with manage_state_update("generate_transcript_section", "生成教学环节逐字稿") as state_update:
        # 获取当前处理的环节信息
        current_section = state.get("current_section")
        if not current_section:
            raise ValueError("缺少当前环节信息")
        
        # 生成逐字稿
        transcript = transcript_section_generate_agent.generate_transcript_section(
            course_info=state["course_info"],
            teaching_process=state["teaching_process"].content,
            process_id=current_section.process_id,
            process_title=current_section.process_title,
            process_content=current_section.content
        )
        
        # 更新环节信息
        updated_section = TeachingProcessInfo(
            process_id=current_section.process_id,
            process_title=current_section.process_title,
            content=current_section.content,
            transcript=transcript
        )
        
        # 更新状态
        state_update["teaching_processes"] = {current_section.process_id: updated_section}
````
</augment_code_snippet>

**处理流程**：
1. 获取当前处理的教学环节信息
2. 调用 `TranscriptSectionGenerateAgent` 生成逐字稿
3. 创建更新后的 `TeachingProcessInfo` 对象
4. 将结果添加到 `teaching_processes` 字典中

**并行处理特性**：
- 支持多个环节同时处理
- 每个环节独立生成逐字稿
- 使用 Send 机制实现任务分发

### 5. merge_transcript_sections - 逐字稿合并节点

**功能**：将所有环节的逐字稿合并为完整文档

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def merge_transcript_sections(state: TranscriptState) -> Dict[str, Any]:
    """合并所有教学环节的逐字稿"""
    with manage_state_update("merge_transcript_sections", "合并教学逐字稿") as state_update:
        # 获取所有教学环节
        teaching_processes = state.get("teaching_processes", {})
        if not teaching_processes:
            raise ValueError("没有找到教学环节信息")
        
        # 合并逐字稿
        final_transcript = transcript_sections_merge_agent.merge_transcript_sections(teaching_processes)
        
        # 更新状态
        state_update["final_transcript"] = final_transcript
````
</augment_code_snippet>

**处理流程**：
1. 收集所有教学环节的逐字稿
2. 调用 `TranscriptSectionsMergeAgent` 进行合并
3. 按环节顺序拼接内容
4. 生成最终的完整逐字稿
5. 保存文件并上传到 OSS

## 边和路由

### 1. 基础边连接

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
# 设置工作流入口点
workflow.add_edge(START, "process_info")

# 所有节点处理完成后进入合并节点
workflow.add_edge("STR_00_generate_transcript_section", "merge_transcript_sections")
workflow.add_edge("merge_transcript_sections", END)
````
</augment_code_snippet>

### 2. 条件边和任务分发

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
# 使用条件边批量下发任务
workflow.add_conditional_edges(
    "generate_txt_teaching_process",
    send_expand_task,
    [
        "STR_00_generate_transcript_section"
    ]
)

workflow.add_conditional_edges(
    "generate_vision_teaching_process",
    send_expand_task,
    [
        "STR_00_generate_transcript_section"
    ]
)
````
</augment_code_snippet>

### 3. 任务分发机制

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
def send_expand_task(state: TranscriptState):
    """根据状态决定下一步任务，一次性分发所有环节的生成任务"""
    # 获取课程信息和教学流程
    course_info = state["course_info"]
    teaching_process_content = state["teaching_process"].content
    
    # 解析教学流程，获取所有部分
    sections = teaching_process_txt_generate_agent.parse_teaching_process(teaching_process_content)
    
    # 准备共享状态
    shared_state = {
        "course_info": course_info,
        "teaching_process": state["teaching_process"]
    }
    
    # 为每个环节创建单独的任务
    tasks = []
    for section in sections:
        process_id = section["id"]
        process_title = section["title"]
        process_content = section["content"]
        
        # 创建包含当前部分信息的状态
        section_state = shared_state.copy()
        section_state["current_section"] = TeachingProcessInfo(
            process_id=process_id,
            process_title=process_title,
            content=process_content
        )
        
        # 添加到任务列表
        tasks.append(Send("STR_00_generate_transcript_section", section_state))
    
    return tasks
````
</augment_code_snippet>

**分发机制特点**：
1. **解析教学流程**：提取所有教学环节
2. **创建共享状态**：包含课程信息和教学流程
3. **生成独立任务**：为每个环节创建单独的处理任务
4. **并行执行**：使用 Send 机制实现并行处理

## 状态管理

### 状态更新上下文管理器

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/graph.py" mode="EXCERPT">
````python
@contextmanager
def manage_state_update(stage: str, initial_status: str) -> Generator[Dict[str, Any], None, None]:
    """管理状态更新的上下文"""
    state_update = {}
    try:
        yield state_update
        # 只有在没有设置router的情况下才设置默认状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 设置错误状态
        error_status = f"{initial_status}失败"
        state_update["router"] = Router(
            stage=stage,
            status=error_status,
            error=str(e)
        )
        print(f"Error in {stage}: {str(e)}")
        raise
````
</augment_code_snippet>

**功能特点**：
1. **统一错误处理**：自动捕获异常并设置错误状态
2. **状态追踪**：记录当前处理阶段和状态
3. **资源管理**：确保状态更新的原子性

## 工作流执行流程

### 完整执行路径

```mermaid
graph TD
    A[START] --> B[process_info]
    B --> C{PDF类型判断}
    C -->|文本| D[generate_txt_teaching_process]
    C -->|视觉| E[generate_vision_teaching_process]
    D --> F[send_expand_task]
    E --> F
    F --> G[STR_00_generate_transcript_section]
    G --> H[merge_transcript_sections]
    H --> I[END]
    
    style F fill:#e1f5fe
    style G fill:#f3e5f5
```

### 执行阶段详解

1. **初始化阶段**
   - 加载配置和环境变量
   - 初始化全局智能体实例
   - 创建工作流图

2. **信息处理阶段**
   - 解析用户输入
   - 提取课程信息
   - 处理 PDF 文件（如果有）

3. **流程生成阶段**
   - 根据输入类型选择处理路径
   - 生成教学流程大纲
   - 解析为多个教学环节

4. **并行处理阶段**
   - 分发任务到多个处理节点
   - 并行生成各环节逐字稿
   - 收集所有处理结果

5. **合并输出阶段**
   - 合并所有环节逐字稿
   - 生成最终文档
   - 保存和上传文件

## 错误处理和监控

### 1. 异常处理机制
- 每个节点都有独立的错误处理
- 使用上下文管理器统一管理异常
- 错误信息记录在 Router 状态中

### 2. 状态追踪
- 实时更新处理阶段信息
- 记录每个步骤的执行状态
- 支持断点续传和错误恢复

### 3. 资源清理
- 自动清理临时文件
- 释放模型资源
- 管理内存使用

## 扩展和优化

### 1. 添加新节点
```python
# 在 create_graph 函数中添加
workflow.add_node("new_node", new_node_function)
workflow.add_edge("previous_node", "new_node")
```

### 2. 修改条件边
```python
# 添加新的条件分支
workflow.add_conditional_edges(
    "source_node",
    condition_function,
    ["target_node_1", "target_node_2"]
)
```

### 3. 性能优化
- 增加并行处理节点
- 优化状态传递机制
- 实现智能缓存策略

这个工作流图设计充分体现了现代 AI 系统的特点：模块化、可扩展、高并发，为教学逐字稿的自动化生成提供了强大而灵活的基础架构。

"""教学逐字稿生成的处理器实现。"""
from typing import Dict, List, Optional, TypedDict, Any, Union
import os
import re
import json
import base64
import time
from shared.utils import File<PERSON>, OSSStorageManager
from shared.decorators import traceable
from shared.configuration import LessonPlanConfiguration
from transcript_graph.state import CourseInfo, TeachingProcessInfo
from shared.model import create_llm_client, manage_txt_llm_call, manage_vision_llm_call
from contextlib import contextmanager
from typing import Generator
import fitz  # PyMuPDF
from pathlib import Path
from transcript_graph.prompts import (
    TEACHING_PROCESS_TXT_SYSTEM_PROMPT,
    TEACHING_PROCESS_VISION_SYSTEM_PROMPT,
    TRANSCRIPT_SECTION_SYSTEM_PROMPT
)

# 全局变量，用于存储文件夹路径
_transcript_folder = None

def get_transcript_folder(course_basic_info: str, config: LessonPlanConfiguration = None) -> str:
    """获取或创建存储逐字稿的文件夹路径。
    
    Args:
        course_basic_info: 课程基本信息，用于命名文件夹
        config: 配置对象，用于获取output_dir
        
    Returns:
        str: 文件夹路径
    """
    global _transcript_folder
    if _transcript_folder is None:
        # 截取课程基本信息的前20个字符作为文件夹名称的一部分
        course_info_short = course_basic_info[:20].replace(" ", "_").replace("/", "_")
        timestamp = int(time.time())
        folder_name = f"transcript_{timestamp}_{course_info_short}"
        
        # 创建文件夹在output_dir下
        base_dir = os.getcwd()
        if config and config.output_dir:
            base_dir = config.output_dir
            
        _transcript_folder = os.path.join(base_dir, folder_name)
        if not os.path.exists(_transcript_folder):
            os.makedirs(_transcript_folder)
    
    return _transcript_folder

def format_course_info(course_info: CourseInfo) -> str:
    info_items = []
    
    if course_info.course_basic_info:
        info_items.append(f"- 课程信息：{course_info.course_basic_info}")
    if course_info.teaching_info:
        info_items.append(f"- 教学信息：{course_info.teaching_info}")
    if course_info.personal_requirements:
        info_items.append(f"- 个性化要求：{course_info.personal_requirements}")
        
    return "\n".join(info_items) if info_items else ""


class FileProcessAgent:
    """文件处理智能体，负责处理PDF文件并提取内容"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.storage = OSSStorageManager(config)
        self.pdf_cache = {}  # 用于缓存PDF临时文件路径
    
    @contextmanager
    def _manage_uploaded_file(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Generator[str, None, None]:
        """管理上传的文件生命周期，确保文件被正确删除"""
        # 如果指定了页面范围，创建临时PDF文件
        temp_pdf_path = pdf_path
        if start_page is not None and end_page is not None:
            with fitz.open(pdf_path) as doc:
                # 验证页码范围
                if start_page < 1 or end_page > doc.page_count or start_page > end_page:
                    raise ValueError(f"无效的页码范围：{start_page}-{end_page}，PDF总页数：{doc.page_count}")
                
                # 创建新的PDF文档
                new_doc = fitz.open()
                for i in range(start_page - 1, end_page):
                    new_doc.insert_pdf(doc, from_page=i, to_page=i)
                
                # 保存临时文件
                temp_pdf_path = pdf_path.replace('.pdf', f'_temp_{start_page}_{end_page}.pdf')
                new_doc.save(temp_pdf_path)
                new_doc.close()
        
        try:
            yield temp_pdf_path
        finally:
            # 如果创建了临时文件，删除它
            if temp_pdf_path != pdf_path:
                try:
                    os.remove(temp_pdf_path)
                except Exception as e:
                    print(f"清理临时文件失败: {str(e)}")
    
    @traceable
    def process_pdf(self, object_name: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> Dict[str, str]:
        """处理PDF文件并提取内容
        
        Args:
            object_name: PDF文件名
            start_page: 起始页码（从1开始）
            end_page: 结束页码（包含）
            
        Returns:
            Dict[str, str]: 包含处理后的内容和类型
        """
        # 确保临时目录存在
        if not os.path.exists(self.config.temp_dir):
            os.makedirs(self.config.temp_dir, exist_ok=True)
            print(f"已创建临时目录: {self.config.temp_dir}")
        
        # 生成缓存键
        cache_key = f"{object_name}_{start_page or ''}_{end_page or ''}"
        
        # 检查是否已有缓存的临时文件
        if cache_key in self.pdf_cache and os.path.exists(self.pdf_cache[cache_key]):
            temp_path = self.pdf_cache[cache_key]
            # 使用缓存的临时文件
            with self._manage_uploaded_file(temp_path, start_page, end_page) as temp_file_path:
                # 尝试提取文本内容
                pdf_content = ""
                with fitz.open(temp_file_path) as doc:
                    for page in doc:
                        pdf_content += page.get_text()
                
                result = {
                    "pdf_content": pdf_content.strip(),
                    "pdf_type": "text" if pdf_content.strip() else "vision",
                    "pdf_path": object_name
                }
                
                # 如果是视觉类型的PDF，将临时文件路径添加到结果中
                if result["pdf_type"] == "vision":
                    # 复制临时文件到可持久保存的位置
                    persistent_temp_path = temp_path
                    result["pdf_temp_path"] = persistent_temp_path
                
                return result
        
        # 从OSS下载或使用本地文件
        try:
            with self.storage.get_temp_file(object_name) as temp_path:
                with self._manage_uploaded_file(temp_path, start_page, end_page) as temp_file_path:
                    # 尝试提取文本内容
                    pdf_content = ""
                    with fitz.open(temp_file_path) as doc:
                        for page in doc:
                            pdf_content += page.get_text()
                    
                    result = {
                        "pdf_content": pdf_content.strip(),
                        "pdf_type": "text" if pdf_content.strip() else "vision",
                        "pdf_path": object_name
                    }
                    
                    # 如果是视觉类型的PDF，保留临时文件
                    if result["pdf_type"] == "vision":
                        # 复制临时文件到可持久保存的位置
                        persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                        import shutil
                        shutil.copy2(temp_file_path, persistent_temp_path)
                        # 缓存临时文件路径
                        self.pdf_cache[cache_key] = persistent_temp_path
                        result["pdf_temp_path"] = persistent_temp_path
                    
                    return result
        except Exception as e:
            # 如果是本地文件，直接处理
            if os.path.exists(object_name):
                with self._manage_uploaded_file(object_name, start_page, end_page) as temp_file_path:
                    # 尝试提取文本内容
                    pdf_content = ""
                    with fitz.open(temp_file_path) as doc:
                        for page in doc:
                            pdf_content += page.get_text()
                    
                    result = {
                        "pdf_content": pdf_content.strip(),
                        "pdf_type": "text" if pdf_content.strip() else "vision",
                        "pdf_path": object_name
                    }
                    
                    # 如果是视觉类型的PDF，保留临时文件
                    if result["pdf_type"] == "vision":
                        # 确保临时目录存在
                        if not os.path.exists(self.config.temp_dir):
                            os.makedirs(self.config.temp_dir, exist_ok=True)
                            print(f"已创建临时目录: {self.config.temp_dir}")
                            
                        # 复制临时文件到可持久保存的位置
                        persistent_temp_path = os.path.join(self.config.temp_dir, f"pdf_cache_{os.path.basename(object_name)}")
                        import shutil
                        shutil.copy2(temp_file_path, persistent_temp_path)
                        # 缓存临时文件路径
                        self.pdf_cache[cache_key] = persistent_temp_path
                        result["pdf_temp_path"] = persistent_temp_path
                    
                    return result
            else:
                raise ValueError(f"无法处理PDF文件: {object_name}, 错误: {str(e)}")


class TeachingProcessTxtGenerateAgent:
    """教学流程生成智能体，负责生成教学流程"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_teaching_process(self, course_info: CourseInfo) -> str:
        """生成教学流程。"""
        # 格式化课程信息
        course_info_formatted=format_course_info(course_info)
        
        # 准备提示词
        prompt = TEACHING_PROCESS_TXT_SYSTEM_PROMPT.format(
            course_info_formatted=course_info_formatted
        )
        
        # 如果有PDF内容，添加到提示词中
        if course_info.pdf_content:
            prompt += f"\n\n参考PDF内容：\n{course_info.pdf_content}"
        
        self.llm.push_trace_info("", "教学流程生成")
        
        with manage_txt_llm_call(self.llm, prompt, self.config, None, None) as teaching_process:
            # 使用自定义文件夹路径
            file_path = os.path.join(get_transcript_folder(course_info.course_basic_info, self.config), "teaching_process.md")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(teaching_process)
                
            return teaching_process
    
    @traceable
    def parse_teaching_process(self, teaching_process: str) -> List[Dict[str, str]]:
        """解析教学流程内容，提取各个环节
        
        Args:
            teaching_process: 教学流程内容
            
        Returns:
            List[Dict[str, str]]: 解析后的教学环节列表
        """
        # 使用正则表达式匹配一级标题及其内容
        pattern = r'# (.*?)\n(.*?)(?=\n# |$)'
        matches = re.findall(pattern, teaching_process, re.DOTALL)
        
        sections = []
        for i, (title, content) in enumerate(matches):
            # 生成两位数的process_id
            process_id = f"process_{i+1:02d}"
            sections.append({
                "id": process_id,
                "title": title.strip(),
                "content": content.strip()
            })
        
        return sections


class TeachingProcessVisionGenerateAgent:
    """视觉教学流程生成智能体，负责处理PDF图像并生成教学流程"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
        # 为视觉处理创建直接的模型客户端
        if config.vision_model_provider == "zhipuai":
            import os
            from zhipuai import ZhipuAI
            zhipuai_key = os.getenv("ZHIPUAI_API_KEY") or config.vision_api_key
            self.vision_client = ZhipuAI(api_key=zhipuai_key)
        elif config.vision_model_provider in ["siliconflow", "dashscope"]:
            from openai import OpenAI
            self.vision_client = OpenAI(
                api_key=config.vision_api_key,
                base_url=config.model_base_url
            )
        else:
            raise ValueError(f"不支持的视觉模型提供商: {config.vision_model_provider}")
    
    def _convert_pdf_to_images(self, pdf_path: str, start_page: Optional[int] = None, end_page: Optional[int] = None) -> List[str]:
        """将PDF转换为图像文件"""
        img_paths = []
        
        with fitz.open(pdf_path) as doc:
            # 设置默认页面范围
            start_idx = 0 if start_page is None else max(0, start_page - 1)
            end_idx = doc.page_count - 1 if end_page is None else min(doc.page_count - 1, end_page - 1)
            
            # 限制最多处理5页
            max_pages = 5
            if end_idx - start_idx + 1 > max_pages:
                end_idx = start_idx + max_pages - 1
                     
            # 创建临时目录
            temp_dir = os.path.join(self.config.temp_dir, f"pdf_images_{int(time.time())}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 转换每页为图像
            for page_idx in range(start_idx, end_idx + 1):
                page = doc[page_idx]
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 使用2倍分辨率
                img_path = os.path.join(temp_dir, f"page_{page_idx+1}.png")
                pix.save(img_path)
                img_paths.append(img_path)
        
        return img_paths
    
    def _encode_image(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    @traceable
    def generate_teaching_process(self, course_info: CourseInfo) -> str:
        """从PDF图像生成教学流程。"""
        if not course_info.pdf_temp_path:
            raise ValueError("没有提供有效的PDF文件路径")
        
        cached_pdf_path = course_info.pdf_temp_path
        img_paths = []
        
        try:
            # 转换PDF为图像
            img_paths = self._convert_pdf_to_images(
                course_info.pdf_temp_path,
                course_info.pdf_start_page,
                course_info.pdf_end_page
            )
            
            # 格式化课程信息
            course_info_formatted = format_course_info(course_info)
            
            # 准备视觉提示
            messages = [
                {"role": "system", "content": TEACHING_PROCESS_VISION_SYSTEM_PROMPT.format(course_info_formatted=course_info_formatted)},
                {"role": "user", "content": [
                    {"type": "text", "text": "这是课程资料的图像，请根据这些资料生成教学流程。"},
                    *[{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self._encode_image(img_path)}"}} for img_path in img_paths]
                ]}
            ]
            
            # 使用manage_vision_llm_call和直接的视觉客户端
            from shared.model import manage_vision_llm_call
            
            with manage_vision_llm_call(self.vision_client, messages, self.config, "视觉教学流程生成", None, None) as teaching_process:
                # 保存结果
                file_path = os.path.join(get_transcript_folder(course_info.course_basic_info, self.config), "teaching_process_vision.md")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(teaching_process)
                
                return teaching_process
        finally:
            # 清理临时图像文件
            if img_paths and os.path.exists(os.path.dirname(img_paths[0])):
                try:
                    import shutil
                    shutil.rmtree(os.path.dirname(img_paths[0]))
                    print(f"已清理临时图像文件夹: {os.path.dirname(img_paths[0])}")
                except Exception as e:
                    print(f"清理临时图像文件夹失败: {str(e)}")
            
            # 清理缓存的临时PDF文件
            if cached_pdf_path and os.path.exists(cached_pdf_path):
                try:
                    # 先关闭所有可能打开的文件句柄
                    import gc
                    gc.collect()  # 强制垃圾回收
                    
                    # 尝试直接删除文件
                    os.remove(cached_pdf_path)
                    print(f"已清理缓存的临时PDF文件: {cached_pdf_path}")
                    
                    # 清理映射信息中的临时路径
                    course_info.pdf_temp_path = None
                except Exception as e:
                    print(f"清理缓存的临时PDF文件失败: {cached_pdf_path}, 错误: {str(e)}")

class TranscriptSectionGenerateAgent:
    """教学逐字稿生成智能体，负责生成每个教学环节的逐字稿"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
        self.llm = create_llm_client(config)
    
    @traceable
    def generate_transcript_section(self, course_info: CourseInfo, teaching_process: str, 
                                   process_id: str, process_title: str, process_content: str) -> str:
        """生成教学环节的逐字稿。"""
        # 格式化课程信息
        course_basic_info=course_info.course_basic_info
        
        # 准备提示词
        prompt = TRANSCRIPT_SECTION_SYSTEM_PROMPT.format(
            course_basic_info=course_basic_info,
            teaching_process=teaching_process,
            process_title=process_title,
        )
        
        self.llm.push_trace_info(f"STR_00_{process_id}", f"教学逐字稿生成")
        
        with manage_txt_llm_call(self.llm, prompt, self.config, None, None) as transcript:
            # 保存每个环节的逐字稿到单独的文件
            file_path = os.path.join(get_transcript_folder(course_info.course_basic_info, self.config), f"transcript_{process_id}.md")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(transcript)
                
            return transcript

class TranscriptSectionsMergeAgent:
    """教学逐字稿合并智能体，负责合并所有环节的逐字稿"""
    
    def __init__(self, config: LessonPlanConfiguration):
        self.config = config
        self.file_io = FileIO(config)
    
    @traceable
    def merge_transcript_sections(self, teaching_processes: Dict[str, TeachingProcessInfo]) -> str:
        """合并所有教学环节的逐字稿。

        Args:
            teaching_processes: 包含所有教学环节及其逐字稿的字典

        Returns:
            str: 合并后的完整逐字稿
        """
        # 按照process_id排序
        sorted_processes = sorted(
            teaching_processes.items(),
            key=lambda x: int(x[0].split('_')[1])
        )
        
        # 使用全局变量中的文件夹路径，如果未初始化则使用默认值
        global _transcript_folder
        folder_path = _transcript_folder if _transcript_folder else os.path.join(self.config.output_dir, "transcript_output")
        
        # 直接拼接所有环节的逐字稿，不添加标题或空行
        merged_transcript = []
        for process_id, process_info in sorted_processes:
            transcript = process_info.transcript
            if transcript:
                merged_transcript.append(transcript)
        
        final_transcript = "\n\n".join(merged_transcript)
        
        # 保存完整逐字稿到文件
        file_path = os.path.join(folder_path, "transcript_complete.md")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(final_transcript)
        
        return final_transcript 
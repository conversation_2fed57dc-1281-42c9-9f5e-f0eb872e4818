"""AI演示文稿生成器模块"""
import os
import re
import json
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from openai import OpenAI

from .config import PPTGeneratorConfig
from .logger import get_logger
from .models import PPTInfo, PPTSlide, PPTOutline, PPTGenerationState

class HTMLSpecLoader:
    """HTML规范加载器"""
    
    def __init__(self, config: PPTGeneratorConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
    
    def load_html_spec(self) -> str:
        """加载HTML规范文件内容"""
        try:
            if os.path.exists(self.config.html_spec_file_path):
                with open(
                    self.config.html_spec_file_path, 
                    'r', 
                    encoding='utf-8'
                ) as f:
                    content = f.read()
                self.logger.info(
                    f"成功加载HTML规范: {self.config.html_spec_file_path}"
                )
                return content
            else:
                self.logger.warning(
                    f"HTML规范文件不存在: {self.config.html_spec_file_path}"
                )
                return ""
        except Exception as e:
            self.logger.error(f"加载HTML规范文件时出错: {e}")
            return ""

class ContentAnalyzer:
    """内容分析器"""
    
    def __init__(self, config: PPTGeneratorConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.client = OpenAI(
            api_key=config.dashscope_api_key,
            base_url=config.dashscope_base_url
        )
    
    def analyze_reference_structure(
        self, 
        state: PPTGenerationState
    ) -> PPTOutline:
        """分析参考资料结构，生成PPT大纲"""
        self.logger.info("开始分析参考资料结构")
        
        analysis_prompt = f"""
请分析以下参考资料，并根据内容生成一个适合制作HTML演示文稿的大纲结构。

参考资料内容：
{state.reference_content}

请按照以下要求生成PPT大纲：
1. 确定合适的幻灯片数量（建议10-20张）
2. 为每张幻灯片确定标题和主要内容要点
3. 识别需要数据可视化的内容
4. 确定适合的页面类型（封面页、数据页、分析页、对比页等）
5. 根据参考资料的类型和内容，智能提取关键信息组织成逻辑清晰的演示结构

请以JSON格式返回大纲：
{{
    "title": "演示文稿标题",
    "slides": [
        {{
            "slide_number": 1,
            "slide_type": "cover",
            "title": "幻灯片标题",
            "content": ["内容要点1", "内容要点2"],
            "notes": "设计备注"
        }}
    ]
}}
"""
        
        formatted_messages = [
            {
                "role": "system", 
                "content": "你是一个专业的PPT设计分析师，擅长将各种参考资料转化为清晰的演示文稿结构。"
            },
            {"role": "user", "content": analysis_prompt}
        ]
        
        try:
            self.logger.debug(f"调用AI分析API，模型: {self.config.dashscope_model}")
            response = self.client.chat.completions.create(
                model=self.config.dashscope_model,
                messages=formatted_messages,
                temperature=self.config.temperature,
                stream=False
            )
            
            # 解析响应内容
            content = response.choices[0].message.content
            self.logger.debug(f"AI响应内容长度: {len(content)}")
            
            try:
                # 提取JSON部分
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    outline_data = json.loads(json_match.group())
                    slides = []
                    
                    for slide_data in outline_data.get('slides', []):
                        # 处理content字段，确保它是字符串列表格式
                        content_data = slide_data.get('content', [])
                        
                        def flatten_content(data):
                            """递归处理content数据，确保返回字符串列表"""
                            if isinstance(data, dict):
                                # 如果是字典，提取所有键值对信息
                                items = []
                                for key, value in data.items():
                                    if isinstance(value, (list, tuple)):
                                        # 如果值是列表，格式化为字符串
                                        items.append(
                                            f"{key}: {', '.join(map(str, value))}"
                                        )
                                    elif isinstance(value, dict):
                                        # 如果值是字典，递归处理
                                        items.extend(flatten_content(value))
                                    else:
                                        items.append(f"{key}: {str(value)}")
                                return items
                            elif isinstance(data, (list, tuple)):
                                # 如果是列表，处理每个元素
                                items = []
                                for item in data:
                                    if isinstance(item, dict):
                                        items.extend(flatten_content(item))
                                    else:
                                        items.append(str(item))
                                return items
                            else:
                                # 其他类型直接转为字符串
                                return [str(data)] if data else []
                        
                        content_data = flatten_content(content_data)
                        
                        slide = PPTSlide(
                            slide_number=slide_data.get('slide_number', 1),
                            slide_type=slide_data.get('slide_type', 'content'),
                            title=slide_data.get('title', ''),
                            content=content_data,
                            notes=slide_data.get('notes', '')
                        )
                        slides.append(slide)
                    
                    outline = PPTOutline(
                        title=outline_data.get('title', '演示文稿'),
                        slides=slides
                    )
                    
                    self.logger.info(
                        f"成功解析PPT大纲: {outline.title}, "
                        f"共{len(slides)}张幻灯片"
                    )
                    return outline
                    
            except json.JSONDecodeError as e:
                self.logger.error(f"解析大纲JSON时出错: {e}")
            except Exception as e:
                self.logger.error(f"处理大纲数据时出错: {e}")
                
        except Exception as e:
            self.logger.error(f"调用AI分析API时出错: {e}")
        
        # 如果解析失败，返回默认大纲
        self.logger.warning("使用默认大纲")
        return self._create_default_outline()
    
    def _create_default_outline(self) -> PPTOutline:
        """创建默认大纲"""
        slides = [
            PPTSlide(
                slide_number=1,
                slide_type="cover",
                title="演示文稿",
                content=["概览", "主要内容", "总结"],
                notes="封面页"
            )
        ]
        self.logger.info("创建默认PPT大纲")
        return PPTOutline(title="演示文稿", slides=slides)

class HTMLSlideGenerator:
    """HTML幻灯片生成器"""
    
    def __init__(self, config: PPTGeneratorConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.client = OpenAI(
            api_key=config.dashscope_api_key,
            base_url=config.dashscope_base_url
        )
    
    def generate_single_slide(
        self, 
        slide: PPTSlide, 
        state: PPTGenerationState, 
        outline: PPTOutline
    ) -> str:
        """生成单张幻灯片的HTML内容"""
        self.logger.debug(
            f"开始生成第{slide.slide_number}张幻灯片: {slide.title}"
        )
        
        generation_prompt = f"""
根据以下PPT大纲和HTML演示文稿创建规范，生成第{slide.slide_number}张幻灯片的完整HTML代码。

PPT大纲信息：
演示文稿标题：{outline.title}
当前幻灯片：
- 编号：{slide.slide_number}
- 类型：{slide.slide_type}
- 标题：{slide.title}
- 内容：{', '.join(slide.content)}
- 备注：{slide.notes}

HTML演示文稿创建规范（请严格遵循）：
{state.html_specification}

请为第{slide.slide_number}张幻灯片生成完整的HTML代码，要求：
1. 严格遵循提供的HTML规范
2. 使用适当的Tailwind CSS类和样式
3. 包含必要的图标和装饰元素
4. 确保内容布局美观、专业
5. 根据内容类型选择合适的页面模板（封面、数据、分析、对比等）
6. 确保HTML文件是完整的，包含完整的</html>结束标签

技术要求：
- 保持响应式设计
- 使用现代化的UI设计
- 确保在不同设备上的显示效果

请直接返回完整的HTML代码，不要包含任何其他内容，包括任何解释或说明。
"""
        
        formatted_messages = [
            {
                "role": "system", 
                "content": "你是一个专业的前端开发工程师和设计师，擅长根据规范创建美观的HTML演示文稿。请确保生成的HTML文件是完整的。"
            },
            {"role": "user", "content": generation_prompt}
        ]
        
        try:
            self.logger.debug(f"调用AI生成API生成幻灯片{slide.slide_number}")
            response = self.client.chat.completions.create(
                model=self.config.dashscope_model,
                messages=formatted_messages,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                stream=False
            )
            
            content = response.choices[0].message.content
            if content and len(content.strip()) > 100:
                self.logger.debug(
                    f"成功生成幻灯片{slide.slide_number}，内容长度: {len(content)}"
                )
            else:
                self.logger.warning(
                    f"幻灯片{slide.slide_number}生成内容过短或为空"
                )
            
            return content
            
        except Exception as e:
            self.logger.error(
                f"生成幻灯片{slide.slide_number}时出错: {e}"
            )
            return ""

    def generate_slides_parallel(
        self, 
        outline: PPTOutline, 
        state: PPTGenerationState, 
        max_workers: int = 3
    ) -> Dict[int, str]:
        """并行生成所有幻灯片的HTML内容"""
        slide_contents = {}
        
        def generate_slide_wrapper(slide: PPTSlide) -> Tuple[int, str]:
            """包装函数，用于线程池"""
            try:
                self.logger.info(f"开始生成幻灯片 {slide.slide_number}: {slide.title}")
                content = self.generate_single_slide(slide, state, outline)
                if content and len(content.strip()) > 100:  # 确保内容有效
                    self.logger.info(
                        f"完成生成幻灯片 {slide.slide_number}: {slide.title}"
                    )
                    return slide.slide_number, content
                else:
                    self.logger.warning(
                        f"生成幻灯片 {slide.slide_number} 内容为空或太短"
                    )
                    return slide.slide_number, ""
            except Exception as e:
                self.logger.error(
                    f"生成幻灯片 {slide.slide_number} 时出错: {e}",
                    exc_info=True
                )
                return slide.slide_number, ""
        
        self.logger.info(f"开始并行生成 {len(outline.slides)} 张幻灯片")
        
        # 使用线程池并行生成
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers
        ) as executor:
            # 提交所有任务
            future_to_slide = {
                executor.submit(generate_slide_wrapper, slide): slide 
                for slide in outline.slides
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_slide):
                slide_number, content = future.result()
                if content:
                    slide_contents[slide_number] = content
        
        self.logger.info(
            f"并行生成完成，成功生成 {len(slide_contents)} / "
            f"{len(outline.slides)} 张幻灯片"
        )
        return slide_contents


class HTMLFileSaver:
    """HTML文件保存器"""
    
    def __init__(self, config: PPTGeneratorConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
    
    def create_output_directory(self, ppt_info: PPTInfo) -> str:
        """创建输出目录"""
        # 生成安全的目录名
        safe_topic = re.sub(r'[<>:"/\\|?*]', '_', ppt_info.topic)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 使用配置中的输出目录
        output_dir = os.path.join(
            self.config.output_dir, 
            f"{safe_topic}_{timestamp}"
        )
        os.makedirs(output_dir, exist_ok=True)
        
        self.logger.info(f"创建输出目录: {output_dir}")
        return output_dir
    

    
    def save_parallel_slides(
        self, 
        slide_contents: Dict[int, str], 
        output_dir: str
    ) -> List[str]:
        """保存并行生成的幻灯片内容"""
        saved_files = []
        
        # 按幻灯片编号排序
        sorted_slides = sorted(slide_contents.items())
        
        self.logger.info(f"开始保存 {len(sorted_slides)} 张幻灯片")
        
        for slide_number, slide_html in sorted_slides:
            slide_html = slide_html.strip()
            
            # 确保HTML是完整的
            if slide_html and not slide_html.endswith("</html>"):
                if "</body>" not in slide_html:
                    slide_html += "\n</body>"
                if "</html>" not in slide_html:
                    slide_html += "\n</html>"
            
            if slide_html and len(slide_html) > 100:  # 确保内容足够长
                filename = os.path.join(output_dir, f"slide_{slide_number}.html")
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(slide_html)
                    saved_files.append(filename)
                    self.logger.debug(
                        f"已保存幻灯片: {filename} (长度: {len(slide_html)})"
                    )
                except Exception as e:
                    self.logger.error(f"保存文件 {filename} 时出错: {e}")
            else:
                self.logger.warning(
                    f"跳过幻灯片 {slide_number}，内容太短或为空"
                )
        
        self.logger.info(f"保存完成，成功保存 {len(saved_files)} 个文件")
        return saved_files
    
    def create_index_page(self, saved_files: List[str], output_dir: str) -> str:
        """创建索引页面"""
        slides_list = []
        for i, file_path in enumerate(saved_files, 1):
            filename = os.path.basename(file_path)
            slides_list.append(f'"{filename}"')
        
        index_html = f"""<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>HTML演示文稿</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
    <style>
        body {{ font-family: "Microsoft YaHei", "SimHei", sans-serif; }}
        .slide-container {{ width: 100vw; height: 100vh; }}
    </style>
</head>
<body class="bg-gray-100">
    <div class="slide-container flex flex-col">
        <!-- 幻灯片显示区域 -->
        <div class="flex-1 flex items-center justify-center">
            <iframe id="slideFrame" 
                    src="{os.path.basename(saved_files[0]) if saved_files else ''}" 
                    class="w-full h-full border-0">
            </iframe>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="bg-white shadow-lg p-4 flex items-center justify-between">
            <button id="prevBtn" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
                <i class="fas fa-chevron-left mr-2"></i>上一页
            </button>
            
            <div class="flex items-center space-x-4">
                <span id="slideInfo" class="text-gray-600">第 1 页 / 共 {len(saved_files)} 页</span>
                <select id="slideSelect" 
                        class="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    {chr(10).join([f'<option value="{i}">{i + 1}. 幻灯片 {i + 1}</option>' for i in range(len(saved_files))])}
                </select>
            </div>
            
            <button id="nextBtn" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
                下一页<i class="fas fa-chevron-right ml-2"></i>
            </button>
        </div>
    </div>

    <script>
        const slides = [{', '.join(slides_list)}];
        let currentSlide = 0;
        
        const slideFrame = document.getElementById('slideFrame');
        const slideInfo = document.getElementById('slideInfo');
        const slideSelect = document.getElementById('slideSelect');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        function updateSlide() {{
            slideFrame.src = slides[currentSlide];
            slideInfo.textContent = `第 ${{currentSlide + 1}} 页 / 共 ${{slides.length}} 页`;
            slideSelect.value = currentSlide;
            
            prevBtn.disabled = currentSlide === 0;
            nextBtn.disabled = currentSlide === slides.length - 1;
        }}
        
        function goToSlide(index) {{
            if (index >= 0 && index < slides.length) {{
                currentSlide = index;
                updateSlide();
            }}
        }}
        
        // 事件监听
        prevBtn.addEventListener('click', () => {{
            if (currentSlide > 0) {{
                goToSlide(currentSlide - 1);
            }}
        }});
        
        nextBtn.addEventListener('click', () => {{
            if (currentSlide < slides.length - 1) {{
                goToSlide(currentSlide + 1);
            }}
        }});
        
        slideSelect.addEventListener('change', (e) => {{
            goToSlide(parseInt(e.target.value));
        }});
        
        // 键盘导航
        document.addEventListener('keydown', (e) => {{
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {{
                e.preventDefault();
                if (currentSlide > 0) {{
                    goToSlide(currentSlide - 1);
                }}
            }} else if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === ' ') {{
                e.preventDefault();
                if (currentSlide < slides.length - 1) {{
                    goToSlide(currentSlide + 1);
                }}
            }}
        }});
        
        // 初始化
        updateSlide();
    </script>
</body>
</html>
"""
        
        index_path = os.path.join(output_dir, "index.html")
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(index_html)
            self.logger.info(f"已创建索引页面: {index_path}")
            return index_path
        except Exception as e:
            self.logger.error(f"创建索引页面时出错: {e}")
            return ""

class PPTGeneratorService:
    """PPT生成服务主类"""
    
    def __init__(self, config: PPTGeneratorConfig):
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.html_spec_loader = HTMLSpecLoader(config)
        self.analyzer = ContentAnalyzer(config)
        self.html_generator = HTMLSlideGenerator(config)
        self.file_saver = HTMLFileSaver(config)
    
    def generate_ppt(self, ppt_info: PPTInfo) -> Dict[str, Any]:
        """生成PPT的主要方法"""
        try:
            self.logger.info(f"开始生成HTML PPT，主题: {ppt_info.topic}")
            
            # 1. 初始化状态
            state = PPTGenerationState()
            
            # 2. 设置参考资料内容
            state.reference_content = ppt_info.reference_content
            self.logger.debug(
                f"参考资料内容长度: {len(ppt_info.reference_content)}"
            )
            
            # 3. 加载HTML规范
            state.html_specification = self.html_spec_loader.load_html_spec()
            # state.editable_guidelines = self.content_loader.load_file_content(
            #     self.config.editable_guidelines_file_path, "可编辑指导"
            # )  # 暂时不需要可编辑功能
            
            # 4. 分析结构生成大纲
            self.logger.info("正在分析参考资料...")
            state.outline = self.analyzer.analyze_reference_structure(state)
            
            # 5. 创建输出目录
            output_dir = self.file_saver.create_output_directory(ppt_info)
            
            # 6. 生成HTML幻灯片 (仅使用并行模式)
            self.logger.info(
                f"计划并行生成 {len(state.outline.slides)} 张幻灯片"
            )
            
            # 设置并行工作线程数
            max_workers = min(
                self.config.max_parallel_workers, 
                len(state.outline.slides)
            )
            self.logger.info(f"使用 {max_workers} 个并行工作线程")
            
            slide_contents = self.html_generator.generate_slides_parallel(
                state.outline, state, max_workers=max_workers
            )
            
            # 检查并行生成的结果
            total_slides = len(state.outline.slides)
            successful_slides = len(slide_contents)
            success_rate = (
                successful_slides / total_slides if total_slides > 0 else 0
            )
            
            self.logger.info(
                f"并行生成结果: {successful_slides}/{total_slides} 张幻灯片成功 "
                f"(成功率: {success_rate:.1%})"
            )
            
            if successful_slides == 0:
                raise Exception("所有幻灯片生成失败，请检查配置和网络连接")
            
            self.logger.info(
                f"✅ 并行生成完成，成功生成 {successful_slides} 张幻灯片"
            )
            
            # 7. 保存并行生成的HTML文件
            saved_files = self.file_saver.save_parallel_slides(
                slide_contents, output_dir
            )
            
            # 8. 创建索引页面
            index_page_path = self.file_saver.create_index_page(
                saved_files, output_dir
            )
            
            # 9. 返回结果
            result = {
                "success": True,
                "message": "HTML PPT生成完成！",
                "topic": ppt_info.topic,
                "generated_files_count": len(saved_files),
                "generated_files": saved_files,
                "index_page": index_page_path,
                "output_directory": output_dir
            }
            
            self.logger.info(f"PPT生成完成！生成了 {len(saved_files)} 个文件")
            self.logger.info(f"索引页面: {index_page_path}")
            
            return result
            
        except Exception as e:
            self.logger.error(
                f"PPT生成过程中出现错误: {str(e)}", 
                exc_info=True
            )
            return {
                "success": False,
                "message": f"PPT生成过程中出现错误: {str(e)}",
                "topic": ppt_info.topic,
                "error": str(e)
            } 
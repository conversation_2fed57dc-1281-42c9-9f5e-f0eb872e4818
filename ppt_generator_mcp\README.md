# AI演示文稿生成MCP服务器

这是一个基于 **MCP (Model Context Protocol)** 的 **AI演示文稿生成服务器**，专门用于根据行业报告内容自动生成专业的HTML演示文稿。

## 🎯 项目功能

- **智能内容分析**：自动分析行业报告内容，提取关键信息点
- **专业PPT结构**：生成符合商业标准的演示文稿大纲和结构
- **美观HTML演示**：创建现代化、响应式的HTML演示文稿
- **可编辑界面**：支持实时编辑文本内容和样式调整
- **MCP标准接口**：提供标准化的工具接口供AI助手调用

## 🏗️ 项目结构

```
ppt_generator_mcp/
├── README.md                           # 项目说明
├── pyproject.toml                     # 项目配置
├── main.py                            # MCP服务器入口
├── src/                               # 核心源码目录
│   └── ppt_generator/
│       ├── __init__.py                # 模块初始化
│       ├── config.py                  # 配置管理
│       ├── models.py                  # 数据模型
│       └── generators.py              # 核心生成逻辑
├── specs/                             # 规范文档目录
│   ├── README.md                      # 规范文档说明
│   ├── spec_html_presentation_standards.md    # HTML演示文稿创建规范
│   └── spec_interactive_editing_standards.md  # 交互编辑功能规范
├── AI_Agent_行业报告_20250508.md       # 示例行业报告
└── outputs/                           # 生成的演示文稿输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`generate_editable_ppt`工具函数
- **`src/ppt_generator/generators.py`** - 核心演示文稿生成模块
  - `BaseContentLoader`: 基础内容加载器类
  - `ContentAnalyzer`: 内容分析和大纲生成智能体
  - `HTMLSlideGenerator`: HTML幻灯片生成智能体
  - `HTMLFileSaver`: 文件保存和索引页面生成器
  - `PPTGeneratorService`: 主服务类
- **`src/ppt_generator/models.py`** - 数据模型定义
- **`src/ppt_generator/config.py`** - 配置管理

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**: 阿里云百炼 DashScope API
  - 默认模型: `deepseek-v3`
- **前端技术**: HTML5 + Tailwind CSS + JavaScript
- **数据验证**: Pydantic
- **环境配置**: python-dotenv
- **并发处理**: asyncio + concurrent.futures

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export DASHSCOPE_API_KEY="your_dashscope_api_key"
```

### 2. 配置选项

环境变量配置：
- `DASHSCOPE_API_KEY`: 阿里云百炼DashScope API密钥（必需）
- `DASHSCOPE_BASE_URL`: API基础URL（可选，默认: https://dashscope.aliyuncs.com/compatible-mode/v1）
- `DASHSCOPE_MODEL`: 使用的模型（可选，默认: deepseek-v3）
- `OUTPUT_DIR`: 演示文稿输出目录（可选，默认: 项目目录下的outputs文件夹）
- `MAX_TOKENS`: 最大token数量（可选，默认: 6000）
- `TEMPERATURE`: 生成温度（可选，默认: 0.7）
- `BATCH_SIZE`: 批量生成幻灯片数量（可选，默认: 3）
- `MAX_PARALLEL_WORKERS`: 并行生成的最大工作线程数（可选，默认: 2）
- `ENABLE_PARALLEL`: 是否启用并行生成（可选，默认: true）

### 3. 运行服务

```bash
# 启动MCP服务器（HTTP流模式）
python main.py
# 服务器将在默认端口启动
```

## 📚 使用示例

### HTTP API访问

服务器启动后，可通过HTTP接口访问：
- **传输模式**: HTTP Streamable
- **协议**: MCP (Model Context Protocol)

### MCP工具调用

```python
# 生成演示文稿
result = await generate_editable_ppt(
    topic="AI Agent行业报告",
    output_dir=""  # 可选，指定输出目录
)
```

### 参数说明

- `topic`: 演示文稿主题（默认为"AI Agent行业报告"）
- `output_dir`: 输出目录路径（可选，默认使用预设路径）

## 🎨 工作流程

1. **内容加载** - 加载行业报告、HTML规范和可编辑指导文档
2. **内容分析** - 使用AI分析报告结构，生成演示文稿大纲
3. **幻灯片生成** - 为每张幻灯片生成专业的HTML内容
4. **样式美化** - 应用Tailwind CSS样式和现代化设计
5. **可编辑集成** - 添加实时编辑功能和样式调整面板
6. **文件保存** - 保存HTML文件并生成导航索引页面

## 📁 输出格式

### 生成内容

生成的演示文稿包含：
- **HTML幻灯片**: 每页独立的HTML文件（`editable_page_N.html`）
- **索引页面**: 导航和预览页面（`index.html`）
- **响应式设计**: 支持不同屏幕尺寸
- **可编辑功能**: 实时文本编辑和样式调整
- **专业样式**: 基于Tailwind CSS的现代化设计

### 文件命名规则

```
outputs/
└── {topic}_{timestamp}/
    ├── index.html              # 主索引页面
    ├── editable_page_1.html    # 第1页幻灯片
    ├── editable_page_2.html    # 第2页幻灯片
    └── ...                     # 更多幻灯片页面
```

## 🌟 特色功能

### 智能内容分析
- **结构化解析**: 自动识别报告的章节结构和关键信息
- **大纲生成**: 智能生成适合演示的PPT大纲结构
- **内容要点提取**: 自动提炼每页的核心内容

### 专业设计
- **多种页面类型**: 支持封面页、数据页、分析页、对比页等
- **现代化UI**: 使用Tailwind CSS构建美观界面
- **响应式布局**: 适配不同设备和屏幕尺寸

### 可编辑功能
- **实时编辑**: 直接在浏览器中编辑文本内容
- **样式调整**: 提供样式编辑面板调整字体、颜色等
- **即时预览**: 编辑后立即看到效果

### 高性能生成
- **并行处理**: 支持多线程并行生成多张幻灯片
- **批量生成**: 可配置批量生成策略提高效率
- **错误恢复**: 具备错误处理和重试机制

## 📋 依赖要求

### 主要依赖
- **Python**: >=3.10, <3.13
- **MCP**: >=1.2.0（模型上下文协议）
- **OpenAI**: >=1.0.0（兼容性AI API客户端，用于连接DashScope）
- **Pydantic**: >=2.0.0（数据验证）
- **FastAPI**: >=0.104.0（Web框架）
- **Uvicorn**: >=0.24.0（ASGI服务器）

### 可选依赖
- **LangGraph**: >=0.1.0（工作流管理）
- **HTTPX**: >=0.24.0（HTTP客户端）
- **python-dotenv**: >=1.0.0（环境变量管理）

## 🔧 配置文件

项目支持多种配置方式：

### 环境变量配置
通过设置环境变量来配置DashScope API密钥、模型参数等。

**获取DashScope API密钥：**
1. 访问阿里云百炼控制台：https://bailian.console.aliyun.com/
2. 在API Key管理页面创建新的API密钥
3. 将API密钥设置为环境变量：`DASHSCOPE_API_KEY`

### 默认文件路径
- 报告文件: `AI_Agent_行业报告_20250508.md`
- HTML规范: `specs/spec_html_presentation_standards.md`
- 可编辑指导: `specs/spec_interactive_editing_standards.md`
 
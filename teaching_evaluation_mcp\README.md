# AI课堂教学分析MCP服务器

这是一个基于 **MCP (Model Context Protocol)** 的 **AI课堂教学分析服务器**，专门用于对课堂教学转录文本进行智能分析，生成专业的五维度教学评估报告。

## 🎯 项目功能

- **智能教学分析**：基于 DashScope DeepSeek-V3 模型的专业课堂教学分析
- **五维度评价体系**：学生学习、教师教学、课程性质、课堂文化、社会情感全面评估
- **实时进度反馈**：MCP 协议支持的实时分析状态和进度报告
- **可视化雷达图**：基于 Plotly 的交互式五维度评分雷达图（HTML + PNG）
- **结构化报告**：生成 Markdown 格式的详细分析报告和 JSON 元数据
- **MCP标准接口**：提供标准化的工具接口供AI助手调用

## 🏗️ 项目结构

```
teaching_evaluation_mcp/
├── README.md                   # 项目说明
├── pyproject.toml              # 项目配置
├── .env.example                # 环境变量示例
├── main.py                     # MCP服务器入口
├── src/                        # 核心源码目录
│   └── teaching_evaluation/
│       ├── __init__.py         # 模块初始化
│       ├── config.py           # 配置管理
│       ├── models.py           # 数据模型
│       ├── logger.py           # 日志系统
│       ├── ai_client.py        # DashScope AI客户端
│       ├── prompt_manager.py   # 提示词管理
│       ├── chart_generator.py  # 雷达图生成器
│       └── analyzer.py         # 教学分析服务
├── logs/                       # 日志文件（运行时创建）
└── outputs/                    # 分析报告输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`analyze_teaching_transcript`工具函数
- **`src/teaching_evaluation/analyzer.py`** - 核心教学分析模块
  - `TeachingAnalyzer`: 主分析服务类
  - 集成AI客户端、提示词管理器和雷达图生成器
- **`src/teaching_evaluation/ai_client.py`** - DashScope AI客户端
- **`src/teaching_evaluation/prompt_manager.py`** - 专业教学分析提示词管理
- **`src/teaching_evaluation/chart_generator.py`** - 五维度雷达图生成器
- **`src/teaching_evaluation/models.py`** - 数据模型定义
- **`src/teaching_evaluation/config.py`** - 配置管理
- **`src/teaching_evaluation/logger.py`** - 日志系统配置

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**: 阿里云 DashScope
  - 分析模型: `deepseek-v3`
- **数据验证**: Pydantic
- **可视化**: Plotly + Kaleido
- **环境配置**: python-dotenv
- **日志**: Python logging with rotation

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -e .

# 设置环境变量
export DASHSCOPE_API_KEY="your_dashscope_api_key"
```

### 2. 配置选项

环境变量配置：
- `DASHSCOPE_API_KEY`: DashScope API密钥（必需）
- `DASHSCOPE_BASE_URL`: DashScope API基础URL（可选）
- `DASHSCOPE_MODEL_NAME`: 使用的模型名称（可选，默认: deepseek-v3）
- `OUTPUT_DIR`: 分析报告输出目录（可选，默认: outputs）
- `LOG_LEVEL`: 日志级别（可选，默认: INFO）

### 3. 运行服务

```bash
# 启动MCP服务器（HTTP流模式）
python main.py
# 服务器将在 http://localhost:8080 启动

# 测试服务器连接（可选）
curl http://localhost:8080/health
```

## 📚 使用示例

### HTTP API访问

服务器启动后，可通过HTTP接口访问：
- **服务地址**: `http://localhost:8080`
- **传输模式**: HTTP Streamable
- **协议**: MCP (Model Context Protocol)

### MCP工具调用

```python
# 分析课堂教学转录文本
result = await analyze_teaching_transcript(
    transcript="""
    老师：同学们，今天我们来学习数学中的分数概念...
    学生A：老师，分数是什么意思？
    老师：很好的问题！分数表示的是部分与整体的关系...
    """
)
```

### 参数说明

- `transcript`: 课堂教学转录文本（必需）
  - 包含师生对话内容
  - 支持时间戳和发言人标识
  - 建议长度：1000-50000字符

## 🎨 工作流程

项目的工作流程被设计为高度透明和实时，确保调用方能随时了解分析进度。

1. **接收转录文本** - MCP工具`analyze_teaching_transcript`接收课堂教学转录文本。
2. **启动实时通信** - 通过`fastmcp`的`Context`对象，开始向客户端进行实时状态推送。
3. **构建分析提示词** - 调用`PromptManager`构建专业的教学分析提示词。
4. **AI模型分析** - 调用`DashScopeClient`与DeepSeek-V3模型交互：
   - **实时进度报告**：分析过程中的各个阶段实时反馈 (`status: "ai_processing"`)。
   - **用量报告**：分析完成后，立刻返回该次模型调用的Token用量信息 (`status: "model_usage"`)。
5. **内容解析与评分** - 解析AI响应，提取五维度分析内容和评分数据。
6. **生成可视化图表** - 调用`RadarChartGenerator`生成五维度雷达图：
   - 生成HTML交互式图表和PNG静态图片。
   - **图表完成通知**：图表生成成功后实时推送 (`status: "chart_generated"`)。
7. **保存分析报告** - 生成并保存完整的分析报告：
   - Markdown格式的详细报告
   - JSON格式的结构化元数据
8. **输出最终结果** - 返回一个完整的、结构化的教学分析JSON对象。

## 📢 实时通知

在调用工具后，客户端可以通过监听服务端的SSE (Server-Sent Events) 来获取实时进度。所有的通知都通过`context.info()`以JSON格式发送。

关键的`status`类型包括：
- `analysis_started`: 分析任务开始。
- `prompt_building`: 正在构建分析提示词。
- `ai_processing`: AI模型分析中。
- `score_extraction`: 正在提取五维度评分数据。
- `parsing_response`: 正在解析AI响应内容。
- `chart_generation`: 正在生成雷达图。
- `reports_saved`: 分析报告文件保存完成。
- `model_usage`: 一次模型调用完成，消息体中包含详细的用量数据。

## 📁 输出格式

### 生成内容

生成的教学分析报告包含：
- **分析报告**: 完整的Markdown格式教学分析报告
  - 总体评价（200-300字）
  - 五维度详细分析（每维度150-200字）
  - 总结与建议（200-300字）
- **雷达图**: 五维度评分可视化
  - **HTML格式**: 交互式雷达图，支持缩放和悬停
  - **PNG格式**: 静态图片，适合报告嵌入
- **元数据文件**: 包含完整的分析信息和统计数据
  - 分析基本信息（ID、时间、耗时等）
  - 五维度分析内容和评分
  - 模型用量统计
  - 生成文件列表

### 文件结构

```
outputs/analysis_YYYYMMDD_HHMMSS_analysis_TIMESTAMP/
├── analysis_report.md                    # Markdown分析报告
├── metadata.json                         # 结构化元数据
└── charts/
    ├── radar_chart_TIMESTAMP.html        # 交互式雷达图
    └── radar_chart_TIMESTAMP.png         # 静态雷达图
```

## 🌟 特色功能

- **专业教学分析**：基于教育学理论的五维度评价体系
- **智能内容解析**：自动识别和过滤AI响应中的无关内容
- **多格式输出**：支持Markdown、JSON、HTML、PNG多种格式
- **实时进度跟踪**：完整的分析流程实时反馈
- **详细用量统计**：精确的Token消耗和成本统计
- **高度可配置**：丰富的环境变量配置选项

## 🧪 验证和测试

### 服务器状态检查

```bash
# 检查服务器是否正常启动
curl http://localhost:8080/health

# 或使用任何支持MCP协议的客户端进行测试
```

### 功能验证

项目已通过以下方面的验证：
- **核心分析功能**: 教学转录文本的智能分析
- **五维度评价**: 准确的评分提取和内容解析
- **雷达图生成**: HTML和PNG格式的可视化图表
- **实时通信**: MCP协议的实时进度反馈
- **文件输出**: Markdown报告和JSON元数据生成

## 🔧 开发状态

本项目已完成开发，当前状态：

- [x] 项目结构创建
- [x] 基础配置和依赖管理
- [x] 日志系统实现
- [x] 数据模型定义
- [x] DashScope AI客户端集成
- [x] 专业教学分析提示词系统
- [x] 五维度雷达图生成模块
- [x] 核心教学分析服务
- [x] MCP服务器实现
- [x] 实时进度反馈系统
- [x] 结构化报告输出
- [x] 完整的错误处理和日志记录

## 📋 五维度评价体系

本项目采用专业的教学评价框架，从以下五个维度全面分析课堂教学质量：

### 1. 学生学习 (Student Learning)
- 学习参与度和积极性
- 知识理解和掌握程度
- 学习方法和策略运用
- 学习效果和成果展示

### 2. 教师教学 (Teacher Teaching)
- 教学方法和策略选择
- 课堂组织和管理能力
- 师生互动和引导技巧
- 教学内容的传达效果

### 3. 课程性质 (Curriculum Nature)
- 教学内容的科学性和准确性
- 课程目标的明确性和达成度
- 教学重点和难点的处理
- 知识体系的完整性和逻辑性

### 4. 课堂文化 (Classroom Culture)
- 课堂氛围和学习环境
- 师生关系和同伴关系
- 课堂纪律和秩序维护
- 多元化和包容性体现

### 5. 社会情感 (Social Emotion)
- 情感态度和价值观培养
- 社会责任感和公民意识
- 合作精神和团队意识
- 个性发展和自信心建立

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 📞 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 邮箱：<EMAIL>

---

**版本**: v1.0.0
**最后更新**: 2025-08-02
**状态**: 生产就绪

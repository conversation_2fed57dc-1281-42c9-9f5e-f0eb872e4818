"""教案分析生成的工作流图定义。"""
import os
from typing import Dict, Any, Optional, List, Generator, Callable, Literal
from typing_extensions import TypedDict
from contextlib import contextmanager

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, END, START
from langgraph.types import Command

from plananalysis_graph.state import (PlanAnalysisState, PlanAnalysisInput, PlanAnalysisOutput,
    Router, PlanInfo, PlanAnalysis, create_initial_state)
from shared.utils import get_message_content, FileIO
from shared.configuration import LessonPlanConfiguration 
from plananalysis_graph.processors import (
    PlanAnalysisTxtGenerateAgent, 
    PlanAnalysisVisionGenerateAgent,
    FileProcessAgent
)

# 加载环境变量配置
load_dotenv()

class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]   # 文本模型提供商
    vision_model_provider: Optional[str]   # 视觉模型提供商
    text_model: Optional[str]   # 文本模型名称
    vision_model: Optional[str]   # 视觉模型名称
    temp_dir: Optional[str]   # 临时文件目录
    output_dir: Optional[str]   # 输出目录

# 全局智能体实例
plan_analysis_txt_generate_agent = None
plan_analysis_vision_generate_agent = None
file_process_agent = None
lesson_plan_config = None  # 全局配置实例
file_io = None  # 全局文件IO实例

def create_graph(config: Optional[Any] = None) -> StateGraph:
    """创建工作流程图
    
    Args:
        config: 来自LangGraph Studio的配置
    """
    # 如果提供了配置，使用LessonPlanConfiguration.from_runnable_config创建配置对象
    global lesson_plan_config, file_io
    if config is not None:
        lesson_plan_config = LessonPlanConfiguration.from_runnable_config(config)
    else:
        lesson_plan_config = LessonPlanConfiguration()
    
    file_io = FileIO(lesson_plan_config)
    
    # 初始化全局代理
    global plan_analysis_txt_generate_agent, plan_analysis_vision_generate_agent, file_process_agent
    plan_analysis_txt_generate_agent = PlanAnalysisTxtGenerateAgent(lesson_plan_config)
    plan_analysis_vision_generate_agent = PlanAnalysisVisionGenerateAgent(lesson_plan_config)
    file_process_agent = FileProcessAgent(lesson_plan_config)
        
    # 创建工作流程图，传入配置模式
    workflow = StateGraph(PlanAnalysisState, ConfigSchema, input=PlanAnalysisInput, output=PlanAnalysisOutput)
    
    # 添加工作节点
    workflow.add_node("process_info", process_info)
    workflow.add_node("STR_00_generate_txt_plan_analysis", generate_txt_plan_analysis)
    workflow.add_node("STR_00_generate_vision_plan_analysis", generate_vision_plan_analysis)
    
    # 设置工作流入口点
    workflow.add_edge(START, "process_info")
    
    # 根据PDF类型选择不同的处理路径
    workflow.add_edge("STR_00_generate_txt_plan_analysis", END)
    workflow.add_edge("STR_00_generate_vision_plan_analysis", END)
    
    return workflow.compile()

@contextmanager
def manage_state_update(stage: str, initial_status: str) -> Generator[Dict[str, Any], None, None]:
    """管理状态更新的上下文"""
    state_update = {}
    try:
        yield state_update
        # 只有在没有设置router的情况下才设置默认状态
        if "router" not in state_update:
            state_update["router"] = Router(
                stage=stage,
                status=f"{initial_status}成功"
            )
    except Exception as e:
        # 设置错误状态
        error_status = f"{initial_status}失败"
        state_update["router"] = Router(
            stage=stage,
            status=error_status,
            error=str(e)
        )
        print(f"Error in {stage}: {str(e)}")
        raise

def process_info(state: PlanAnalysisState) -> Command[Literal["STR_00_generate_txt_plan_analysis", "STR_00_generate_vision_plan_analysis"]]:
    """处理用户信息
    
    Args:
        state: 当前状态
        
    Returns:
        Command: 包含状态更新和下一个节点的命令
    """
    with manage_state_update("process_info", "处理用户信息") as state_update:
        # 从消息中提取最后一条
        if not state.get("messages"):
            raise ValueError("无有效的用户输入消息")
        
        latest_message = state["messages"][-1]
        # 直接使用get_message_content提取参数
        params = get_message_content(latest_message)
        
        # 更新教案信息
        plan_info = PlanInfo(
            lesson_plan=params.get("lesson_plan", ""),
            personal_requirements=params.get("personal_requirements", ""),
            pdf_path=params.get("pdf_path", None),
            pdf_start_page=params.get("pdf_start_page", None),
            pdf_end_page=params.get("pdf_end_page", None),
            pdf_type="text"  # 默认为文本类型
        )
        
        # 验证至少有教案或PDF其中之一
        if not plan_info.lesson_plan and not plan_info.pdf_path:
            raise ValueError("请提供教案内容或PDF文件路径其中之一")
        
        # 处理PDF文件（如果有）
        if plan_info.pdf_path:
            try:
                # 处理PDF文件
                pdf_result = file_process_agent.process_pdf(
                    plan_info.pdf_path,
                    plan_info.pdf_start_page,
                    plan_info.pdf_end_page
                )
                
                # 更新教案信息
                plan_info.pdf_content = pdf_result.get("pdf_content", "")
                plan_info.pdf_type = pdf_result.get("pdf_type", "text")
                plan_info.pdf_path = pdf_result.get("pdf_path", plan_info.pdf_path)
                
                # 如果是视觉类型的PDF且有临时路径，更新临时路径
                if plan_info.pdf_type == "vision" and "pdf_temp_path" in pdf_result:
                    plan_info.pdf_temp_path = pdf_result.get("pdf_temp_path")
                    print(f"已更新PDF临时文件路径: {plan_info.pdf_temp_path}")
            except Exception as e:
                print(f"处理PDF文件失败: {str(e)}")
                # 即使PDF处理失败，也继续执行后续流程
        
        # 创建状态更新
        state_update["plan_info"] = plan_info
        state_update["router"] = Router(
            stage="process_info",
            status="处理用户信息成功"
        )
        
        # 根据PDF类型确定下一个节点
        if plan_info.pdf_path and plan_info.pdf_type == "vision":
            goto = "STR_00_generate_vision_plan_analysis"
        else:
            goto = "STR_00_generate_txt_plan_analysis"
            
        # 返回命令，包含状态更新和下一个节点
        return Command(
            update=state_update,
            goto=goto
        )

def generate_txt_plan_analysis(state: PlanAnalysisState) -> Dict[str, Any]:
    """生成文本教案分析
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("STR_00_generate_txt_plan_analysis", "生成文本教案分析") as state_update:
        # 确保教案信息存在
        if not state.get("plan_info"):
            raise ValueError("缺少教案信息，无法生成分析")
        
        # 调用智能体生成文本教案分析
        plan_analysis_content = plan_analysis_txt_generate_agent.generate_plan_analysis(
            state["plan_info"]
        )
        
        # 更新状态
        state_update["plan_analysis"] = PlanAnalysis(content=plan_analysis_content)
        state_update["final_analysis"] = plan_analysis_content
        state_update["router"] = Router(
            stage="FIN_ALL_generate_txt_plan_analysis",
            status="生成文本教案分析成功"
        )
    
    return state_update

def generate_vision_plan_analysis(state: PlanAnalysisState) -> Dict[str, Any]:
    """生成视觉教案分析
    
    Args:
        state: 当前状态
        
    Returns:
        Dict[str, Any]: 更新后的状态
    """
    with manage_state_update("STR_00_generate_vision_plan_analysis", "生成视觉教案分析") as state_update:
        # 确保教案信息存在
        if not state.get("plan_info"):
            raise ValueError("缺少教案信息，无法生成分析")
        
        # 调用智能体生成视觉教案分析
        plan_analysis_content = plan_analysis_vision_generate_agent.generate_plan_analysis(
            state["plan_info"]
        )
        
        # 更新状态
        state_update["plan_analysis"] = PlanAnalysis(content=plan_analysis_content)
        state_update["final_analysis"] = plan_analysis_content
        state_update["router"] = Router(
            stage="FIN_ALL_generate_vision_plan_analysis",
            status="生成视觉教案分析成功"
        )
    
    return state_update 
# 提示词系统详解

## 概述

Transcript Graph 的提示词系统是整个 AI 生成流程的核心，它定义了如何与各种 AI 模型进行交互，确保生成内容的质量和一致性。系统包含三个主要提示词，分别负责文本教学流程生成、视觉教学流程生成和教学逐字稿生成。

## 提示词架构

### 设计原则

1. **角色定位明确**：每个提示词都明确定义了 AI 的专业角色
2. **格式规范严格**：详细规定输出格式，确保结构化输出
3. **内容要求具体**：明确字数限制、质量标准和评价维度
4. **中国化特色**：符合中国教育体系和课堂教学特点

### 参数化设计

所有提示词都采用参数化设计，支持动态内容替换：

```python
prompt = TEMPLATE.format(
    course_info_formatted=course_info,
    process_title=title,
    # ... 其他参数
)
```

## 详细提示词分析

### 1. TEACHING_PROCESS_TXT_SYSTEM_PROMPT - 文本教学流程生成

#### 功能定位
负责基于文本课程信息生成结构化的教学流程大纲。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/prompts.py" mode="EXCERPT">
````python
TEACHING_PROCESS_TXT_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据课程信息，设计一份教学流程，其中使用"一、二、三、等"二级标题(##)标识每个环节

{course_info_formatted}

要求：
1. 格式要求：
- 严格使用"一、二、三、等"一级标题(#)标识每个环节
- 每个环节下使用数字序号（1、2、3、等）等二级标题(##），至少包含3个，4-5个左右更合适
- 每个环节的内容120-150字
- 总字数不超过600字
- 不要输出无关内容及任何解释说明，直接输出流程内容，从"# 一、"开始
````
</augment_code_snippet>

#### 参数说明

**输入参数：**
- `{course_info_formatted}`: 格式化后的课程信息，包含：
  - 课程基本信息（课程名称、年级、学科等）
  - 教学信息（教学内容、教学目标等）
  - 个性化要求（可选）
  - PDF 内容（如果有）

#### 格式要求详解

**1. 标题层级结构**
```markdown
# 一、课程导入
## 1. 创设情境
## 2. 激发兴趣
## 3. 引出主题

# 二、知识讲解
## 1. 概念介绍
## 2. 原理分析
## 3. 实例演示
```

**2. 内容规范**
- **字数控制**：每个环节 120-150 字，总计不超过 600 字
- **环节数量**：4-5 个教学环节
- **必备环节**：课程导入、课程总结
- **逻辑性**：环节间有清晰的递进关系

#### 质量检查维度

**1. 内容选取合理性**
- 符合教学重点内容
- 难度适中，循序渐进
- 知识点完整，逻辑清晰

**2. 时间分配科学性**
- 各环节时间比例合理
- 预留缓冲时间
- 节奏把控适当

**3. 教学设计合理性**
- 目标明确具体
- 方法灵活多样
- 师生互动充分
- 重难点突出

### 2. TEACHING_PROCESS_VISION_SYSTEM_PROMPT - 视觉教学流程生成

#### 功能定位
专门处理包含 PDF 图像的多模态输入，基于视觉内容生成教学流程。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/prompts.py" mode="EXCERPT">
````python
TEACHING_PROCESS_VISION_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据提供的课程信息和资料图像，设计一份教学流程，其中使用"一、二、三、等"二级标题(##)标识每个环节

{course_info_formatted}

要求：
1. 格式要求：
- 严格使用"一、二、三、等"一级标题(#)标识每个环节
- 每个环节下使用数字序号（1、2、3、等）等二级标题(##），至少包含3个，4-5个左右更合适
- 每个环节的内容100-120字
- 总字数不超过500字
- 不要输出无关内容及任何解释说明，直接输出流程内容，从"# 一、"开始
````
</augment_code_snippet>

#### 与文本版本的差异

**1. 字数调整**
- 每个环节：100-120 字（比文本版本少 20-30 字）
- 总字数：不超过 500 字（比文本版本少 100 字）

**2. 视觉特化要求**
- 充分利用图像中的信息
- 提取关键知识点
- 结合图像内容设计教学环节

**3. 多模态处理**
- 同时处理文本和图像信息
- 图像内容优先级更高
- 文本信息作为补充和上下文

#### 视觉内容处理策略

**1. 图像信息提取**
- 识别图表、公式、概念图
- 提取文字内容和关键信息
- 理解图像的教学意图

**2. 内容整合**
- 将图像信息与课程信息结合
- 确保教学流程的连贯性
- 突出图像中的重点内容

### 3. TRANSCRIPT_SECTION_SYSTEM_PROMPT - 教学逐字稿生成

#### 功能定位
为单个教学环节生成详细的教学逐字稿，是系统的核心内容生成组件。

<augment_code_snippet path="yunzhishi-agent-service/src/transcript_graph/prompts.py" mode="EXCERPT">
````python
TRANSCRIPT_SECTION_SYSTEM_PROMPT = """作为一名专业的教学逐字稿创作专家，请根据教学流程环节的内容，为该环节创作完整的教学逐字稿。

课程基本信息：
{course_basic_info}

教学流程全貌：
{teaching_process}

当前需要创作逐字稿的环节：
{process_title}

1. 格式要求：
   - 按照流程的编号、框架及格式，直接从"# {process_title}"开始
   - 必须以"老师："开始，表示教师讲话
   - 教师与学生对话时，学生的发言以"学生："标识
   - 教师的动作或说明放在括号中，如"(板书在黑板上)"
   - 重要内容可以使用引号强调
   - 每段对话独占一行
   - 每个教学环节内部结构应包含：引入、讲解、互动、小结
````
</augment_code_snippet>

#### 参数说明

**输入参数：**
- `{course_basic_info}`: 课程基本信息
- `{teaching_process}`: 完整的教学流程内容
- `{process_title}`: 当前环节的标题

#### 格式要求详解

**1. 对话格式规范**
```markdown
# 一、课程导入

老师：同学们，今天我们来学习一个非常有趣的话题...

学生：老师，这个概念我们之前学过吗？

老师：(走到黑板前) 很好的问题！让我们先回顾一下...

老师：请大家看这个例子，"重点内容需要特别注意"。
```

**2. 内部结构要求**
每个教学环节必须包含四个部分：
- **引入**：环节开始，承接上一环节
- **讲解**：核心内容阐述
- **互动**：师生问答和讨论
- **小结**：环节总结，过渡到下一环节

#### 内容要求详解

**1. 语言风格**
- 自然、亲切、生动
- 符合中国课堂教学语言风格
- 避免 AI 常见的生硬表达
- 避免过于完美、刻板的句式

**2. 互动设计**
- 体现教师与学生的真实互动
- 包含提问、回答和讨论
- 设计合理的学生反应
- 展现课堂的动态性

**3. 内容完整性**
- 涵盖环节中的所有教学内容
- 包含教师的提问、解释和总结
- 对重点内容进行强调
- 保持逻辑层次清晰

**4. 字数控制**
- 总字数：800-1200 字
- 确保内容充实但不冗长
- 平衡各部分的篇幅

## 提示词优化策略

### 1. 参数化设计

**优势：**
- 支持动态内容替换
- 提高提示词的复用性
- 便于维护和更新

**实现方式：**
```python
def format_prompt(template, **kwargs):
    return template.format(**kwargs)
```

### 2. 分层次要求

**结构层次：**
1. 角色定位
2. 输入信息
3. 格式要求
4. 内容要求
5. 质量检查

**好处：**
- 逐步引导 AI 理解任务
- 确保输出质量的多维度控制
- 便于问题定位和优化

### 3. 中国化适配

**教育特色：**
- 符合中国教育体系
- 体现中国课堂特点
- 使用中国教师语言风格

**文化适配：**
- 使用中文数字序号
- 符合中文表达习惯
- 体现师生关系特点

## 提示词测试和验证

### 1. 输出格式验证

**检查项目：**
- 标题层级是否正确
- 字数是否符合要求
- 格式是否规范

**验证方法：**
```python
def validate_format(output):
    # 检查标题格式
    # 统计字数
    # 验证结构
    pass
```

### 2. 内容质量评估

**评估维度：**
- 教学逻辑性
- 内容完整性
- 语言自然度
- 互动合理性

### 3. 一致性检查

**检查内容：**
- 多次生成结果的一致性
- 不同环节间的连贯性
- 与输入信息的匹配度

## 提示词维护和更新

### 1. 版本管理

**管理策略：**
- 记录每次修改的原因
- 保留历史版本用于回滚
- 测试新版本的效果

### 2. 性能监控

**监控指标：**
- 生成质量评分
- 用户满意度
- 处理时间

### 3. 持续优化

**优化方向：**
- 根据用户反馈调整
- 结合新的 AI 模型特性
- 适应教育需求变化

## 最佳实践

### 1. 提示词设计原则

- **明确性**：指令清晰，不产生歧义
- **完整性**：覆盖所有必要的要求
- **一致性**：保持风格和标准统一
- **可测试性**：便于验证和评估

### 2. 参数使用建议

- 使用描述性的参数名
- 确保参数值的格式正确
- 处理参数缺失的情况
- 验证参数的有效性

### 3. 错误处理

- 处理 AI 模型的异常输出
- 提供备用的处理策略
- 记录和分析失败案例
- 持续改进提示词设计

这个提示词系统为 Transcript Graph 提供了强大的 AI 交互能力，确保了生成内容的高质量和一致性，是整个系统成功的关键因素。

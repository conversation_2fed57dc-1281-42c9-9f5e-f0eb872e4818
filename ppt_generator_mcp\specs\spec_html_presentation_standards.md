# HTML 演示文稿 (PPT) 创建规范

## 1. 基础结构

### 1.1 文档结构
```html
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>演示文稿标题</title>
    <!-- CSS和JS引入 -->
    <style>
        /* 页面样式 */
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 幻灯片内容 -->
    </div>
</body>
</html>
```

### 1.2 尺寸规范
- 标准尺寸：宽度1280px，高度720px（16:9比例）
- 在CSS中固定尺寸：
```css
body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
}
```

## 2. 资源引用

### 2.1 必要库
```html
<!-- Tailwind CSS -->
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<!-- 或使用脚本方式 -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Font Awesome 图标 -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>

<!-- Tailwind Browser -->
<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

<!-- 数据可视化（按需引入） -->
<script src="https://d3js.org/d3.v7.min.js"></script>
```

### 2.2 字体设置
```css
/* Google Fonts 引入 */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Noto+Serif+SC:wght@700&family=Noto+Sans+SC:wght@400;500&display=swap');

/* 中文标题 */
.title {
    font-family: 'Noto Serif SC', serif;
    font-weight: bold;
}

/* 中文正文 */
.subtitle, .content {
    font-family: 'Noto Sans SC', sans-serif;
}

/* 备选方案 */
body {
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
}
```

## 3. 样式规范

### 3.1 背景设置
```css
/* 基础渐变背景 */
body {
    background: linear-gradient(to bottom right, #f9f4ff, #f0e6ff);
}

/* 带角度渐变 */
.slide {
    background: linear-gradient(135deg, #f9f2ff 0%, #f0e6fa 100%);
}

/* 图片背景 */
.slide-with-bg {
    background-image: url('path/to/image.jpg');
    background-size: cover;
    background-position: center;
}

/* 带覆盖层的图片背景 */
.gradient-overlay {
    background: linear-gradient(135deg, rgba(103, 58, 183, 0.85) 0%, rgba(63, 81, 181, 0.75) 100%);
    position: absolute;
    inset: 0;
}
```

### 3.2 内容容器
```css
.content-box {
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 25px;
}

/* 卡片式容器 */
.card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 8px 15px rgba(106, 48, 147, 0.1);
    transition: transform 0.3s ease;
    padding: 20px;
}

.card:hover {
    transform: translateY(-5px);
}
```

### 3.3 标题样式
```css
.title {
    color: #6a2c91;
    font-size: 36px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* 带下划线的标题 */
.title-underlined {
    position: relative;
}

.title-underlined::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background-color: #6a3093;
}
```

### 3.4 颜色方案
```css
:root {
    /* 主色 */
    --primary-color: #6a2c91;
    --primary-light: #9c6fb6;
    --primary-dark: #5a287d;
    
    /* 背景色 */
    --bg-light: #f9f4ff;
    --bg-medium: #f0e6ff;
    
    /* 强调色 */
    --accent-color: #ff7eb3;
    
    /* 文本色 */
    --text-dark: #333333;
    --text-light: #ffffff;
    --text-muted: #6b7280;
}

.text-primary { color: var(--primary-color); }
.bg-primary { background-color: var(--primary-color); }
```

## 4. 布局规范

### 4.1 页面布局
```html
<div class="slide-container p-10">
    <!-- 标题区域 -->
    <div class="mb-8">
        <h1 class="title">幻灯片标题</h1>
    </div>
    
    <!-- 内容区域 -->
    <div class="content flex">
        <!-- 左侧内容 -->
        <div class="w-1/2 pr-6">
            <!-- 内容块 -->
        </div>
        
        <!-- 右侧内容 -->
        <div class="w-1/2 pl-6">
            <!-- 内容块 -->
        </div>
    </div>
    
    <!-- 页码 -->
    <div class="absolute bottom-4 right-6 text-gray-500">
        1 / 18
    </div>
</div>
```

### 4.2 网格布局
```html
<div class="grid grid-cols-3 gap-8">
    <!-- 第一列 -->
    <div class="card">
        <!-- 内容 -->
    </div>
    
    <!-- 第二列 -->
    <div class="card">
        <!-- 内容 -->
    </div>
    
    <!-- 第三列 -->
    <div class="card">
        <!-- 内容 -->
    </div>
</div>
```

### 4.3 内容对齐
```css
/* 居中对齐 */
.center-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 垂直分布 */
.vertical-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
```

## 5. 内容元素规范

### 5.1 图标使用
```css
/* 基础图标样式 */
.icon {
    font-size: 24px;
    color: var(--primary-color);
}

/* 圆形图标容器 */
.feature-icon {
    background-color: #e9d5ff;
    color: #6a2c91;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 10px rgba(123, 67, 151, 0.3);
}
```

```html
<!-- 图标使用示例 -->
<div class="feature-icon">
    <i class="fas fa-paint-brush"></i>
</div>
```

### 5.2 图片处理
```css
/* 基础图片样式 */
.image {
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

/* 卡片图片 */
.card-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}
```

```html
<!-- 图片使用示例 -->
<img alt="描述文本" class="image" src="path/to/image.jpg"/>
```

### 5.3 列表样式
```css
/* 带图标的列表 */
.icon-list {
    list-style: none;
    padding: 0;
}

.icon-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
}

.icon-list i {
    color: var(--primary-color);
    margin-right: 10px;
    margin-top: 4px;
}
```

```html
<!-- 列表使用示例 -->
<ul class="space-y-3">
    <li class="flex items-start">
        <i class="fas fa-check-circle text-purple-600 mt-1 mr-2"></i>
        <span>列表项内容</span>
    </li>
</ul>
```

### 5.4 特性项目
```html
<!-- 特性项目示例 -->
<div class="feature-item">
    <div class="feature-icon">
        <i class="fas fa-paint-brush"></i>
    </div>
    <div class="feature-text">
        <h3>特性标题</h3>
        <p>特性描述文本，详细说明该特性的内容和优势。</p>
    </div>
</div>
```

### 5.5 引用样式
```html
<!-- 引用样式示例 -->
<div class="p-6 flex items-center justify-center">
    <div class="text-center">
        <i class="fas fa-quote-left text-purple-400 text-3xl mb-4"></i>
        <p class="italic text-lg mb-4">"引用文本内容"</p>
    </div>
</div>
```

### 5.6 装饰元素
```css
.decorative-element {
    position: absolute;
    opacity: 0.15;
    z-index: -1;
}
```

```html
<!-- 装饰元素示例 -->
<div class="decorative-element top-0 right-0">
    <i class="fas fa-heart text-pink-300 text-9xl"></i>
</div>
```

## 6. 数据可视化

### 6.1 基础图表设置
```javascript
// 设置图表尺寸和边距
const margin = {top: 20, right: 30, bottom: 40, left: 60};
const width = container.clientWidth - margin.left - margin.right;
const height = container.clientHeight - margin.top - margin.bottom;

// 创建SVG元素
const svg = d3.select("#chart-container")
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);
```

### 6.2 柱状图示例
```javascript
// 创建X轴比例尺
const x = d3.scaleBand()
    .range([0, width])
    .domain(data.map(d => d.category))
    .padding(0.3);

// 创建Y轴比例尺
const y = d3.scaleLinear()
    .range([height, 0])
    .domain([0, d3.max(data, d => d.value)]);

// 添加柱状图
svg.selectAll("bars")
    .data(data)
    .join("rect")
    .attr("x", d => x(d.category))
    .attr("y", d => y(d.value))
    .attr("width", x.bandwidth())
    .attr("height", d => height - y(d.value))
    .attr("fill", "#9c59b6")
    .attr("rx", 5)
    .attr("ry", 5);
```

## 7. 响应性和交互

### 7.1 悬停效果
```css
.hover-element {
    transition: all 0.3s ease;
}

.hover-element:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
```

### 7.2 强调效果
```css
.highlight {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 22px;
}
```

## 8. 最佳实践

### 8.1 文件组织
- 每张幻灯片使用单独的HTML文件
- 文件命名格式：`page_[序号].html`（例如：`page_1.html`, `page_2.html`）

### 8.2 代码规范
- 使用语义化HTML标签
- 保持CSS类名一致性
- 注释关键代码段
- 优化图片资源

### 8.3 性能考虑
- 使用CDN加载库
- 优化图片尺寸和格式
- 避免过度使用复杂动画

### 8.4 可访问性
- 为图片添加alt属性
- 使用语义化HTML结构
- 确保足够的颜色对比度
- 使用适当的字体大小

## 9. 常见页面类型模板

### 9.1 封面页
```html
<div class="slide-container bg-gradient-to-br from-purple-100 to-indigo-100">
    <!-- 背景图片 -->
    <div class="absolute inset-0">
        <img alt="背景图片" class="w-full h-full object-cover" src="path/to/image.jpg"/>
        <div class="gradient-overlay absolute inset-0 opacity-80"></div>
    </div>
    
    <!-- 内容 -->
    <div class="absolute inset-0 flex flex-col justify-center items-center text-white p-16">
        <div class="text-center mb-10">
            <h1 class="title text-5xl mb-6 leading-tight">演示文稿标题</h1>
            <p class="subtitle text-xl mb-8 max-w-3xl mx-auto leading-relaxed">
                副标题或简短描述
            </p>
        </div>
        
        <!-- 页脚信息 -->
        <div class="absolute bottom-12 flex items-center space-x-2 text-sm opacity-80">
            <i class="fas fa-calendar-alt mr-1"></i>
            <span>日期</span>
            <span class="mx-2">|</span>
            <i class="fas fa-user mr-1"></i>
            <span>演讲者</span>
        </div>
    </div>
</div>
```

### 9.2 数据页
```html
<div class="slide">
    <div class="title">数据标题</div>
    <div class="content">
        <div class="stats">
            <!-- 统计数据 -->
            <div class="stat-item">
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-text">
                    统计描述 <span class="highlight">958亿元</span>
                </div>
            </div>
            <!-- 更多统计项 -->
        </div>
        <div class="chart-container">
            <div class="chart-title">图表标题</div>
            <svg id="chart" width="100%" height="300"></svg>
        </div>
    </div>
</div>
```

### 9.3 特性对比页
```html
<div class="slide-container p-10">
    <h1 class="title">对比分析</h1>
    <div class="grid grid-cols-3 gap-8 h-4/5">
        <!-- 项目1 -->
        <div class="card p-5 flex flex-col">
            <h2 class="text-xl font-bold text-purple-800 mb-3">项目标题</h2>
            <img alt="项目图片" class="card-image mb-4" src="path/to/image.jpg"/>
            <div class="flex-grow">
                <p class="mb-3">项目描述</p>
                <!-- 项目详情 -->
            </div>
        </div>
        
        <!-- 项目2和3 -->
    </div>
</div>
```

### 9.4 分析页
```html
<div class="slide-container p-10">
    <div class="mb-8">
        <h1 class="title">分析标题</h1>
    </div>
    <div class="flex">
        <div class="w-2/3 pr-6">
            <div class="content-box p-6 h-full">
                <p class="text-lg mb-4">主要分析内容</p>
                <div class="grid grid-cols-2 gap-6 mt-8">
                    <!-- 特性项目 -->
                </div>
            </div>
        </div>
        <div class="w-1/3">
            <div class="content-box p-6 mb-6">
                <h3 class="text-xl font-bold text-purple-800 mb-4">补充分析</h3>
                <!-- 列表或其他内容 -->
            </div>
        </div>
    </div>
</div>
```

---

通过遵循以上规范，可以创建出风格统一、美观专业的HTML演示文稿。根据具体需求，可以对样式和布局进行适当调整，但建议保持整体风格的一致性。
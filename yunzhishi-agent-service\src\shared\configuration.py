"""定义教学计划生成系统的可配置参数。"""

from __future__ import annotations
from dataclasses import dataclass, field, fields
from typing import Annotated, Optional, Dict, Any, cast
from typing_extensions import TypedDict
from langchain_core.runnables import RunnableConfig
import os

# 定义配置模式
class ConfigSchema(TypedDict):
    """工作流配置模式"""
    txt_model_provider: Optional[str]
    vision_model_provider: Optional[str]
    text_model: Optional[str]
    vision_model: Optional[str]
    temp_dir: Optional[str]
    output_dir: Optional[str]
    oss_endpoint: Optional[str]
    oss_bucket: Optional[str]

@dataclass
class LessonPlanConfiguration:
    """应用配置类"""
    
    # 模型提供商配置
    txt_model_provider: str = field(default="zhipuai")  # zhipuai/siliconflow/dashscope
    vision_model_provider: str = field(default="zhipuai")  # zhipuai/siliconflow/dashscope
    text_model: str = field(default="zhipuai/glm-4-air-0111")
    vision_model: str = field(default="zhipuai/glm-4v-plus-0111")
    
    # 文件处理配置
    temp_dir: str = field(default="temp_files")
    output_dir: str = field(default="outputs")
    
    # Token统计API配置（从环境变量读取）
    
    # 当前课程信息
    current_course_name: Optional[str] = field(default=None)
    current_lesson_unitname: Optional[str] = field(default=None)
    
    # OSS配置
    oss_endpoint: str = field(default="oss-cn-shanghai.aliyuncs.com")
    oss_bucket: str = field(default="textbook-pdf2025")

    @classmethod
    def from_runnable_config(cls, config: RunnableConfig) -> "LessonPlanConfiguration":
        """从运行时配置创建配置对象"""
        if not isinstance(config, dict):
            config = cast(Dict[str, Any], config)
            
        # 获取configurable字段
        configurable = config.get("configurable", {})
        
        if not configurable:
            return cls()
            
        # 创建配置对象
        try:
            # 确保provider是小写的
            txt_model_provider = str(configurable.get("txt_model_provider", "zhipuai")).lower()
            vision_model_provider = str(configurable.get("vision_model_provider", "zhipuai")).lower()
            
            # 根据txt_model_provider设置默认的text_model
            default_text_model = (
                "THUDM/glm-4-9b-chat" if txt_model_provider == "siliconflow"
                else "deepseek-v3" if txt_model_provider == "dashscope"
                else "zhipuai/glm-4-air-0111"
            )
            text_model = str(configurable.get("text_model", default_text_model))
            
            # 根据vision_model_provider设置默认的vision_model
            default_vision_model = (
                "deepseek-vl2" if vision_model_provider == "siliconflow"
                else "qwen-omni-turbo" if vision_model_provider == "dashscope"
                else "zhipuai/glm-4v-plus-0111"
            )
            vision_model = str(configurable.get("vision_model", default_vision_model))
            
            config_obj = cls(
                txt_model_provider=txt_model_provider,
                vision_model_provider=vision_model_provider,
                text_model=text_model,
                vision_model=vision_model,
                temp_dir=str(configurable.get("temp_dir", "temp_files")),
                output_dir=str(configurable.get("output_dir", "outputs")),
                oss_endpoint=str(configurable.get("oss_endpoint", "oss-cn-shanghai.aliyuncs.com")),
                oss_bucket=str(configurable.get("oss_bucket", "textbook-pdf2025"))
            )
            return config_obj     
        except Exception as e:
            return cls()

    @property
    def txt_api_key(self) -> str:
        """获取文本模型API密钥"""
        if self.txt_model_provider == "zhipuai":
            key = os.getenv("ZHIPUAI_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 ZHIPUAI_API_KEY")
            return key.strip()
        elif self.txt_model_provider == "siliconflow":
            key = os.getenv("SILICONFLOW_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 SILICONFLOW_API_KEY") 
            return key.strip()
        elif self.txt_model_provider == "dashscope":
            key = os.getenv("DASHSCOPE_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 DASHSCOPE_API_KEY")
            return key.strip()
        else:
            raise ValueError(f"不支持的文本模型提供商: {self.txt_model_provider}")
            
    @property
    def vision_api_key(self) -> str:
        """获取视觉模型API密钥"""
        if self.vision_model_provider == "zhipuai":
            key = os.getenv("ZHIPUAI_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 ZHIPUAI_API_KEY")
            return key.strip()
        elif self.vision_model_provider == "siliconflow":
            key = os.getenv("SILICONFLOW_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 SILICONFLOW_API_KEY") 
            return key.strip()
        elif self.vision_model_provider == "dashscope":
            key = os.getenv("DASHSCOPE_API_KEY")
            if not key:
                raise ValueError("请设置环境变量 DASHSCOPE_API_KEY")
            return key.strip()
        else:
            raise ValueError(f"不支持的视觉模型提供商: {self.vision_model_provider}")
            
    @property
    def model_base_url(self) -> str:
        """获取模型API基础URL"""
        if self.txt_model_provider == "siliconflow":
            return "https://api.siliconflow.cn/v1"
        elif self.txt_model_provider == "dashscope":
            return "https://dashscope.aliyuncs.com/compatible-mode/v1"
        return ""
        
    @property
    def vision_model_base_url(self) -> str:
        """获取视觉模型API基础URL"""
        if self.vision_model_provider == "siliconflow":
            return "https://api.siliconflow.cn/v1"
        elif self.vision_model_provider == "dashscope":
            return "https://dashscope.aliyuncs.com/compatible-mode/v1"
        return ""
        
    def __post_init__(self):
        """配置验证"""
        if self.txt_model_provider not in ["zhipuai", "siliconflow", "dashscope"]:
            raise ValueError(f"txt_model_provider必须是zhipuai、siliconflow或dashscope")
            
        if self.vision_model_provider not in ["zhipuai", "siliconflow", "dashscope"]:
            raise ValueError(f"vision_model_provider必须是zhipuai、siliconflow或dashscope")
            
        if self.txt_model_provider == "zhipuai":
            if not self.text_model.startswith("zhipuai/"):
                raise ValueError("zhipuai模型名称必须以zhipuai/开头")
                
        # 验证视觉模型名称格式
        if self.vision_model_provider == "zhipuai":
            if not self.vision_model.startswith("zhipuai/"):
                raise ValueError("zhipuai视觉模型名称必须以zhipuai/开头")
            
    @property
    def oss_access_key_id(self) -> str:
        """获取OSS访问密钥ID"""
        oss_access_key_id = os.getenv("OSS_ACCESS_KEY_ID")
        if not oss_access_key_id:
            raise ValueError("未设置OSS_ACCESS_KEY_ID环境变量")
        return oss_access_key_id
        
    @property
    def oss_access_key_secret(self) -> str:
        """获取OSS访问密钥"""
        oss_access_key_secret = os.getenv("OSS_ACCESS_KEY_SECRET")
        if not oss_access_key_secret:
            raise ValueError("未设置OSS_ACCESS_KEY_SECRET环境变量")
        return oss_access_key_secret
    
    @property
    def token_api_domain(self) -> str:
        """获取Token统计API域名"""
        token_api_domain = os.getenv("TOKEN_API_DOMAIN", "http://localhost:8080")
        return token_api_domain.strip()

    def get_text_model_name(self) -> str:
        """获取实际的文本模型名称"""
        if self.txt_model_provider == "zhipuai":
            return self.text_model.split('/')[-1]
        elif self.txt_model_provider == "dashscope":
            # 如果是完整路径（如 dashscope/deepseek-v3），去掉前缀
            if '/' in self.text_model:
                return self.text_model.split('/')[-1]
            return self.text_model
        return self.text_model

def get_default_config() -> LessonPlanConfiguration:
    """获取默认配置"""
    return LessonPlanConfiguration()

def load_config(config_path: str) -> LessonPlanConfiguration:
    """从文件加载配置"""
    import json
    import os
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            
        config = LessonPlanConfiguration(**config_data)
        
        return config
    except json.JSONDecodeError as e:
        raise ValueError(f"配置文件格式错误: {str(e)}")
    except Exception as e:
        raise ValueError(f"加载配置文件时出错: {str(e)}")

def save_config(config: LessonPlanConfiguration, config_path: str) -> None:
    """保存配置到文件
    
    Args:
        config: 配置对象
        config_path: 配置文件保存路径
        
    Raises:
        ValueError: 保存配置文件时出错
    """
    import json
    import os
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 将配置对象转换为字典
        config_dict = {
            field.name: getattr(config, field.name)
            for field in fields(config)
        }
        
        # 保存到文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise ValueError(f"保存配置文件时出错: {str(e)}") 
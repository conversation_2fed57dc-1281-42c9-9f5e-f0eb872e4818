"""Grade Analysis Core Service Module

实现完整的成绩分析业务逻辑，参考 picturebook_generator_mcp 的服务架构和实时通信模式。
"""
import json
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from fastmcp import Context

from .config import GradeAnalysisConfig
from .logger import get_logger
from .models import (
    AnalysisRequest, AnalysisResult, ProgressUpdate, 
    ErrorInfo, AnalysisMetadata, ModelUsageInfo
)
from .ai_client import AnalystClient, ReporterClient
from .code_executor import SafeCodeExecutor


class GradeAnalyzer:
    """
    成绩分析主服务类，负责协调整个分析流程。
    参考 picturebook_generator_mcp 的 PictureBookGeneratorService 设计模式。
    """
    
    def __init__(self, config: GradeAnalysisConfig):
        """
        初始化成绩分析服务。

        Args:
            config (GradeAnalysisConfig): 应用配置实例。
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.analyst_client = AnalystClient(config)
        self.reporter_client = ReporterClient(config)
        self.code_executor = SafeCodeExecutor(config)
        self.logger.info("成绩分析服务已初始化")
    
    async def analyze_grade_data(
        self, 
        request: AnalysisRequest, 
        context: Context
    ) -> Dict[str, Any]:
        """
        执行完整的成绩分析流程，包括数据解析、代码生成、代码执行、报告生成，
        并通过 context 实时向客户端发送进度和用量更新。

        Args:
            request (AnalysisRequest): 来自用户请求的分析信息。
            context (Context): MCP上下文，用于与客户端实时通信。

        Returns:
            Dict[str, Any]: 一个包含最终结果的字典，成功时包含报告等信息，失败时包含错误信息。
        """
        
        async def send_progress(status: str, message: str, extra: Optional[Dict] = None):
            """便捷函数，用于通过 MCP 通道发送结构化的进度更新。"""
            if context:
                payload = {"status": status, "message": message}
                if extra:
                    payload.update(extra)
                # 将结构化数据作为JSON字符串通过info日志发送
                await context.info(json.dumps(payload))
            self.logger.info(f"[进度] {status}: {message}")
        
        async def send_usage(usage_info: ModelUsageInfo):
            """发送模型用量信息"""
            if context:
                await context.info(json.dumps({
                    "status": "model_usage",
                    "usage": usage_info.model_dump()
                }))
        
        # 初始化分析元数据
        metadata = AnalysisMetadata(
            analysis_id=request.analysis_id,
            start_time=datetime.now().isoformat(),
            model_usage=[]
        )
        
        try:
            # 1. 分析开始
            await send_progress(
                "analysis_started", 
                "成绩分析任务已开始",
                {"analysis_id": request.analysis_id}
            )
            
            # 2. 数据解析和验证
            await send_progress("data_parsing", "正在解析和验证数据...")
            
            # 简单的数据验证
            if not request.data_string.strip():
                raise ValueError("数据字符串为空")
            
            # 估算数据规模
            data_lines = request.data_string.strip().split('\n')
            estimated_rows = len([line for line in data_lines if line.strip() and '|' in line]) - 1  # 减去表头
            estimated_cols = len(data_lines[0].split('|')) - 2 if data_lines else 0  # 减去首尾空格
            
            metadata.data_rows = max(0, estimated_rows)
            metadata.data_columns = max(0, estimated_cols)
            
            await send_progress(
                "data_parsing_completed", 
                f"数据解析完成，预估 {metadata.data_rows} 行 {metadata.data_columns} 列",
                {"rows": metadata.data_rows, "columns": metadata.data_columns}
            )
            
            # 3. AI 代码生成
            await send_progress("code_generation", "AI 正在生成分析代码...")
            
            generated_code, analyst_usage = await self.analyst_client.generate_analysis_code(
                request.user_request, 
                request.data_string
            )
            
            metadata.model_usage.append(analyst_usage)
            metadata.code_generated = generated_code
            await send_usage(analyst_usage)
            
            if not generated_code.strip():
                raise ValueError("AI 未能生成有效的分析代码")
            
            await send_progress("code_generation_completed", "分析代码生成完毕")
            
            # 4. 安全代码执行
            await send_progress("code_execution", "正在安全执行分析代码...")
            
            execution_result = await self.code_executor.execute_code(
                generated_code, 
                request.data_string
            )
            
            if not execution_result.success:
                error_msg = f"代码执行失败: {execution_result.error}"
                await context.error(json.dumps({
                    "error_type": "code_execution_error",
                    "error_message": error_msg,
                    "error_details": execution_result.error,
                    "stage": "code_execution",
                    "retry_suggested": True,
                    "analysis_id": request.analysis_id
                }))
                raise ValueError(error_msg)
            
            # 解析执行结果
            try:
                analysis_results = json.loads(execution_result.output)
            except json.JSONDecodeError:
                analysis_results = {"raw_output": execution_result.output}
            
            await send_progress(
                "code_execution_completed", 
                f"代码执行完成，耗时 {execution_result.execution_time:.2f} 秒",
                {"execution_time": execution_result.execution_time}
            )
            
            # 5. AI 报告生成
            await send_progress("report_generation", "AI 正在生成分析报告...")
            
            analysis_report, reporter_usage = await self.reporter_client.generate_analysis_report(
                request.user_request, 
                analysis_results
            )
            
            metadata.model_usage.append(reporter_usage)
            await send_usage(reporter_usage)
            
            if not analysis_report.strip():
                raise ValueError("AI 未能生成有效的分析报告")
            
            await send_progress("report_generation_completed", "分析报告生成完毕")
            
            # 6. 保存结果文件
            output_files = await self._save_analysis_results(
                request, analysis_report, analysis_results, metadata
            )
            
            # 7. 完成分析
            metadata.end_time = datetime.now().isoformat()
            start_dt = datetime.fromisoformat(metadata.start_time)
            end_dt = datetime.fromisoformat(metadata.end_time)
            metadata.total_duration = (end_dt - start_dt).total_seconds()
            
            # 计算总用量
            metadata.total_tokens = sum(
                usage.input_tokens + usage.output_tokens 
                for usage in metadata.model_usage
            )
            metadata.estimated_cost = sum(
                usage.cost_estimate or 0 
                for usage in metadata.model_usage
            )
            
            success_message = f"成绩分析《{request.analysis_id}》完成！"
            await send_progress(
                "analysis_completed", 
                success_message,
                {
                    "total_duration": metadata.total_duration,
                    "total_tokens": metadata.total_tokens,
                    "estimated_cost": metadata.estimated_cost
                }
            )
            
            self.logger.info(success_message)
            
            # 返回最终结果
            return {
                "success": True,
                "analysis_id": request.analysis_id,
                "report": analysis_report,
                "metadata": metadata.model_dump(),
                "files": output_files
            }
            
        except Exception as e:
            error_message = f"分析过程中发生错误: {str(e)}"
            self.logger.exception(error_message)
            
            # 更新元数据
            metadata.end_time = datetime.now().isoformat()
            if metadata.start_time:
                start_dt = datetime.fromisoformat(metadata.start_time)
                end_dt = datetime.fromisoformat(metadata.end_time)
                metadata.total_duration = (end_dt - start_dt).total_seconds()
            
            if context:
                # 通过 MCP 协议发送结构化错误信息
                await context.error(json.dumps({
                    "error_type": "analysis_error",
                    "error_message": "分析过程中发生错误",
                    "error_details": str(e),
                    "stage": "analysis_execution",
                    "retry_suggested": True,
                    "analysis_id": request.analysis_id
                }))
            
            return {
                "success": False,
                "analysis_id": request.analysis_id,
                "message": "分析过程中发生错误",
                "error": str(e),
                "metadata": metadata.model_dump()
            }
    
    async def _save_analysis_results(
        self, 
        request: AnalysisRequest,
        report: str,
        results: Dict[str, Any],
        metadata: AnalysisMetadata
    ) -> List[str]:
        """
        保存分析结果到文件。
        
        Args:
            request: 分析请求
            report: 生成的报告
            results: 分析结果数据
            metadata: 分析元数据
            
        Returns:
            List[str]: 保存的文件路径列表
        """
        try:
            # 创建输出目录
            os.makedirs(self.config.output_dir, exist_ok=True)
            
            # 生成文件名前缀
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_prefix = f"analysis_{timestamp}_{request.analysis_id[:8]}"
            
            saved_files = []
            
            # 保存分析报告
            report_file = os.path.join(self.config.output_dir, f"{file_prefix}_report.md")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            saved_files.append(report_file)
            
            # 保存分析结果数据
            results_file = os.path.join(self.config.output_dir, f"{file_prefix}_results.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            saved_files.append(results_file)
            
            # 保存元数据
            metadata_file = os.path.join(self.config.output_dir, f"{file_prefix}_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata.model_dump(), f, ensure_ascii=False, indent=2)
            saved_files.append(metadata_file)
            
            self.logger.info(f"分析结果已保存到 {len(saved_files)} 个文件")
            return saved_files
            
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")
            return []

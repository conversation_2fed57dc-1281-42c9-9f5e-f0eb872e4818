# 项目一致性检查清单

## 概述

本文档用于确保 `transcript_generator_mcp` 项目与参考项目 `picturebook_generator_mcp` 在代码规范、文件结构、命名规范等方面保持高度一致。

## 1. 文件和目录结构一致性

### 1.1 根目录结构对比

**参考项目 (picturebook_generator_mcp):**
```
picturebook_generator_mcp/
├── README.md
├── pyproject.toml
├── main.py
├── doc/                    # 文档目录
├── src/
│   └── picturebook_generator/
├── tests/
└── outputs/
```

**当前项目 (transcript_generator_mcp):**
```
transcript_generator_mcp/
├── README.md
├── pyproject.toml
├── main.py
├── docs/                   # ❌ 应为 doc/
├── src/
│   └── transcript_generator/
├── tests/
└── outputs/
```

**检查项目:**
- [ ] 目录命名是否一致 (`doc/` vs `docs/`)
- [ ] 是否包含所有必要的根目录
- [ ] 目录结构层次是否一致

### 1.2 源码目录结构对比

**参考项目:**
```
src/picturebook_generator/
├── __init__.py
├── config.py
├── models.py
├── generators.py           # 核心业务逻辑
└── logger.py
```

**当前项目:**
```
src/transcript_generator/
├── __init__.py
├── config.py
├── models.py
├── services.py             # ❌ 应为 generators.py
├── logger.py
├── ai_client.py            # 额外文件
└── prompts.py              # 额外文件
```

**检查项目:**
- [ ] 核心业务文件命名是否一致
- [ ] 是否包含所有必要的模块文件
- [ ] 额外文件是否合理且必要

## 2. 代码命名规范一致性

### 2.1 类名命名规范

**参考项目模式:**
- `PictureBookGeneratorConfig`
- `PictureBookInfo`
- `StoryGenerationAgent`
- `ImageGenerationAgent`
- `PictureBookGeneratorService`

**当前项目模式:**
- `TranscriptGeneratorConfig`
- `CourseInfo`
- `TranscriptGeneratorService`

**检查项目:**
- [ ] 类名是否遵循 PascalCase
- [ ] 类名是否包含项目前缀
- [ ] 类名是否语义明确

### 2.2 方法名命名规范

**参考项目模式:**
- `generate_picture_book()`
- `generate_story()`
- `generate_images()`
- `send_progress()`

**检查项目:**
- [ ] 方法名是否遵循 snake_case
- [ ] 方法名是否语义明确
- [ ] 核心方法命名是否一致

### 2.3 变量和常量命名

**检查项目:**
- [ ] 变量名是否遵循 snake_case
- [ ] 常量名是否遵循 UPPER_SNAKE_CASE
- [ ] 私有方法是否以下划线开头

## 3. 日志系统一致性

### 3.1 日志记录器命名

**参考项目:**
```python
logger = logging.getLogger("PictureBookLogger")
```

**当前项目:**
```python
logger = logging.getLogger("TranscriptGeneratorLogger")
```

**检查项目:**
- [ ] 日志记录器命名是否一致
- [ ] 日志记录器是否在各模块中正确使用

### 3.2 日志格式规范

**参考项目格式:**
```
2025-07-21 14:26:11 - PictureBookGeneratorService - INFO - generators.py:431 - send_progress() - [进度] image_generation_started: 正在为第 1 页生成图片...
```

**当前项目格式:**
```
2025-07-20 16:59:06 - transcript_generator.services - INFO - services.py:115 - generate_teaching_flow() - 开始生成教学流程 - 课程: 健康检查测试课程
```

**检查项目:**
- [ ] 日志时间格式是否一致
- [ ] 日志级别使用是否合理
- [ ] 进度日志是否包含 `[进度]` 标识
- [ ] 日志消息格式是否一致

### 3.3 进度日志详细程度

**参考项目进度日志示例:**
- `[进度] directory_created: 已创建绘本工作目录`
- `[进度] story_generation_started: 正在生成故事剧本...`
- `[进度] image_generation_started: 正在为第 1 页生成图片...`
- `[进度] image_generation_completed: 第 1 页图片生成完毕`

**检查项目:**
- [ ] 是否包含所有关键步骤的进度日志
- [ ] 进度日志是否足够详细
- [ ] 进度日志格式是否统一

## 4. 配置管理一致性

### 4.1 pyproject.toml 结构

**检查项目:**
- [ ] 项目元数据是否完整
- [ ] 依赖版本是否合理
- [ ] 构建系统配置是否一致
- [ ] 代码格式化工具配置是否一致

### 4.2 环境变量命名

**参考项目模式:**
- `DOUBAO_API_KEY`
- `DOUBAO_BASE_URL`
- `DOUBAO_STORY_MODEL`
- `OUTPUT_DIR`

**检查项目:**
- [ ] 环境变量命名是否遵循 UPPER_SNAKE_CASE
- [ ] 环境变量前缀是否一致
- [ ] 是否包含所有必要的配置项

### 4.3 配置类结构

**检查项目:**
- [ ] 配置类是否继承 BaseModel
- [ ] 字段是否使用 Field 进行描述
- [ ] 是否包含 from_env 类方法
- [ ] 默认值设置是否合理

## 5. 数据模型一致性

### 5.1 Pydantic 模型结构

**检查项目:**
- [ ] 模型类是否继承 BaseModel
- [ ] 字段类型注解是否正确
- [ ] 字段描述是否完整
- [ ] 模型验证是否合理

### 5.2 字段命名规范

**检查项目:**
- [ ] 字段名是否遵循 snake_case
- [ ] 字段名是否语义明确
- [ ] 可选字段是否正确标记

## 6. 输出格式一致性

### 6.1 metadata.json 结构对比

**参考项目结构:**
```json
{
  "book_info": {
    "title": "...",
    "style": "...",
    "target_pages": 6,
    "theme": "...",
    "requirements": "..."
  },
  "generation_info": {
    "timestamp": "...",
    "story_length": 773,
    "actual_pages": 6
  },
  "story_script": "..."
}
```

**当前项目结构:**
```json
{
  "request_info": {
    "course_basic_info": "...",
    "teaching_info": "...",
    "personal_requirements": null
  },
  "generation_info": {
    "timestamp": "...",
    "sections_count": 6,
    "estimated_duration_minutes": 22,    // ❌ 不需要
    "generation_time_seconds": 43.8,    // ❌ 不需要
    "transcript_length": 5093
  },
  "transcript_content": "..."
}
```

**检查项目:**
- [ ] 顶级字段命名是否合理
- [ ] 是否包含不必要的字段
- [ ] 字段类型是否一致
- [ ] 数据结构是否清晰

### 6.2 输出目录结构

**检查项目:**
- [ ] 输出目录命名规则是否一致
- [ ] 时间戳格式是否一致
- [ ] 文件组织结构是否合理

## 7. 错误处理一致性

### 7.1 异常处理模式

**检查项目:**
- [ ] 是否使用统一的异常处理模式
- [ ] 错误日志记录是否完整
- [ ] 是否通过 ctx.error() 报告错误

### 7.2 错误消息格式

**检查项目:**
- [ ] 错误消息是否包含足够的上下文信息
- [ ] 错误类型分类是否合理
- [ ] 错误消息是否用户友好

## 8. 文档规范一致性

### 8.1 README.md 结构

**检查项目:**
- [ ] 项目描述是否清晰
- [ ] 安装说明是否完整
- [ ] 使用示例是否准确
- [ ] 技术架构说明是否详细

### 8.2 代码注释和文档字符串

**检查项目:**
- [ ] 类和方法是否包含完整的 docstring
- [ ] 注释风格是否一致
- [ ] 文档字符串格式是否遵循 Google 风格

## 检查执行建议

1. **逐项检查**: 按照清单逐项进行检查，标记完成状态
2. **优先级排序**: 优先处理影响功能和用户体验的不一致问题
3. **批量修改**: 对于命名规范等问题，可以批量进行修改
4. **测试验证**: 每次修改后进行测试，确保功能正常
5. **文档更新**: 修改完成后及时更新相关文档

## 9. 其他一致性检查项目

### 9.1 依赖管理一致性

**检查项目:**
- [ ] 是否使用相同的包管理工具
- [ ] 核心依赖版本是否兼容
- [ ] 开发依赖是否完整

### 9.2 测试结构一致性

**检查项目:**
- [ ] 测试目录结构是否一致
- [ ] 测试文件命名规范是否统一
- [ ] 测试覆盖率是否达标

### 9.3 MCP 协议实现一致性

**检查项目:**
- [ ] MCP 工具定义是否规范
- [ ] 参数类型和验证是否一致
- [ ] 返回值格式是否统一
- [ ] 错误处理机制是否一致

### 9.4 实时通信一致性

**检查项目:**
- [ ] `ctx.info()` 调用时机是否合理
- [ ] 进度消息格式是否统一
- [ ] 状态码定义是否一致
- [ ] 消息内容是否用户友好

### 9.5 性能和资源管理

**检查项目:**
- [ ] 内存使用是否合理
- [ ] 文件操作是否安全
- [ ] 异步处理是否正确
- [ ] 资源清理是否完整

## 检查完成标准

- [ ] 所有检查项目均已完成
- [ ] 项目结构与参考项目高度一致
- [ ] 代码规范完全符合标准
- [ ] 功能测试全部通过
- [ ] 文档更新完整
- [ ] MCP 协议实现规范
- [ ] 用户体验达到预期标准

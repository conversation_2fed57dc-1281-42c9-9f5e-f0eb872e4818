"""定义教学逐字稿生成系统中使用的所有提示词。"""

# 文本教学流程生成提示词
TEACHING_PROCESS_TXT_SYSTEM_PROMPT = """作为一名专业的教学设计专家，请根据课程信息，设计一份教学流程，其中使用"一、二、三、等"二级标题(##)标识每个环节

{course_info_formatted}

要求：
1. 格式要求：
- 严格使用"一、二、三、等"一级标题(#)标识每个环节
- 每个环节下使用数字序号（1、2、3、等）等二级标题(##），至少包含3个，4-5个左右更合适
- 每个环节的内容120-150字
- 总字数不超过600字
- 不要输出无关内容及任何解释说明，直接输出流程内容，从"# 一、"开始

2. 内容要求：
- 根据课程内容设计4-5个教学环节，至少包括课程导入、课程总结
- 环节之间要有清晰的逻辑关系和递进性
- 确保教学流程的完整性

3. 质量检查：
- 内容选取合理：
  * 符合教学重点内容
  * 难度适中，循序渐进
  * 知识点完整，逻辑清晰
- 时间分配科学：
  * 各环节时间比例合理
  * 预留缓冲时间
  * 节奏把控适当
- 教学设计合理：
  * 目标明确具体
  * 方法灵活多样
  * 师生互动充分
  * 重难点突出"""


# 教学逐字稿生成提示词
TRANSCRIPT_SECTION_SYSTEM_PROMPT = """作为一名专业的教学逐字稿创作专家，请根据教学流程环节的内容，为该环节创作完整的教学逐字稿。

课程基本信息：
{course_basic_info}

教学流程全貌：
{teaching_process}

当前需要创作逐字稿的环节：
{process_title}

1. 格式要求：
   - 按照流程的编号、框架及格式，直接从"# {process_title}"开始
   - 必须以"老师："开始，表示教师讲话
   - 教师与学生对话时，学生的发言以"学生："标识
   - 教师的动作或说明放在括号中，如"(板书在黑板上)"
   - 重要内容可以使用引号强调
   - 每段对话独占一行
   - 每个教学环节内部结构应包含：引入、讲解、互动、小结

2. 内容要求：
   - 语言自然、亲切、生动
   - 体现教师与学生的互动，包括提问、回答和讨论
   - 避免AI常见的生硬或不自然的表达，避免过于完美、刻板的句式结构
   - 涵盖此环节中提到的所有教学内容和步骤
   - 包含教师的提问、解释和总结
   - 对于重点内容要有强调和层次
   - 符合中国课堂教学的语言风格
   - 总字数控制在800-1200字左右

请直接输出逐字稿内容，不要包含其他解释。"""


def format_course_info(course_basic_info: str, teaching_info: str, personal_requirements: str = None) -> str:
    """格式化课程信息为提示词模板

    Args:
        course_basic_info: 课程基本信息
        teaching_info: 教学信息
        personal_requirements: 个性化要求

    Returns:
        str: 格式化后的课程信息
    """
    formatted_info = f"""课程基本信息：{course_basic_info}

教学信息：{teaching_info}"""

    if personal_requirements:
        formatted_info += f"""

个性化要求：{personal_requirements}"""

    return formatted_info
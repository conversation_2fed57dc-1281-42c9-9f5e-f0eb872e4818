# TeacherAssistantAgent 部署指南

本文档详细说明如何在阿里云 ECS 上部署 TeacherAssistantAgent 服务。

## 1. 环境准备

### 1.1 系统要求
- 操作系统：Ubuntu Server LTS（推荐 20.04 或更高版本）
- Python 版本：3.11 或更高
- 内存：建议 4GB 或更高
- 磁盘空间：建议 20GB 或更高

### 1.2 基础环境安装

```bash
# 更新系统
sudo apt-get update
sudo apt-get upgrade -y

# 安装必要的系统包
sudo apt-get install -y python3.11 python3.11-venv python3-pip git nginx supervisor

# 创建项目目录
sudo mkdir -p /opt/teacherassistant
sudo chown $(whoami):$(whoami) /opt/teacherassistant
cd /opt/teacherassistant
```

## 2. 项目部署

### 2.1 获取项目代码

```bash
# 克隆项目代码
git clone [项目仓库地址] .

# 创建并激活虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装项目依赖
pip install -e .
```

### 2.2 配置环境变量

```bash
# 创建环境变量文件
cp .env.example .env
```

编辑 `.env` 文件，配置以下必要的环境变量：

```ini
# API Keys
ZHIPUAI_API_KEY=your_zhipuai_key
OSS_ACCESS_KEY_ID=your_oss_key_id
OSS_ACCESS_KEY_SECRET=your_oss_key_secret

# 服务配置
LANGCHAIN_HOST=0.0.0.0
LANGCHAIN_BACKEND_HOST=0.0.0.0
LANGCHAIN_PORT=2024
LANGCHAIN_SERVE_ALLOW_CREDENTIALS=true
LANGCHAIN_SERVE_CORS_HEADERS=*
LANGCHAIN_SERVE_CORS_ORIGINS=*
```

## 3. 服务配置

### 3.1 Supervisor 配置

创建 Supervisor 配置文件：

```bash
sudo nano /etc/supervisor/conf.d/teacherassistant.conf
```

配置内容：

```ini
[group:teacherassistant]
programs=teacherassistant-2024,teacherassistant-2025,teacherassistant-2026,teacherassistant-2027,teacherassistant-2028

[program:teacherassistant-2024]
directory=/opt/teacherassistant
command=/opt/teacherassistant/venv/bin/langgraph dev --host 0.0.0.0 --port 2024
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/teacherassistant.2024.err.log
stdout_logfile=/var/log/teacherassistant.2024.out.log
environment=LANGCHAIN_HOST="0.0.0.0",LANGCHAIN_BACKEND_HOST="0.0.0.0",LANGCHAIN_PORT="2024",LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true",LANGCHAIN_SERVE_CORS_HEADERS="*",LANGCHAIN_SERVE_CORS_ORIGINS="*"

[program:teacherassistant-2025]
directory=/opt/teacherassistant
command=/opt/teacherassistant/venv/bin/langgraph dev --host 0.0.0.0 --port 2025
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/teacherassistant.2025.err.log
stdout_logfile=/var/log/teacherassistant.2025.out.log
environment=LANGCHAIN_HOST="0.0.0.0",LANGCHAIN_BACKEND_HOST="0.0.0.0",LANGCHAIN_PORT="2025",LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true",LANGCHAIN_SERVE_CORS_HEADERS="*",LANGCHAIN_SERVE_CORS_ORIGINS="*"

[program:teacherassistant-2026]
directory=/opt/teacherassistant
command=/opt/teacherassistant/venv/bin/langgraph dev --host 0.0.0.0 --port 2026
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/teacherassistant.2026.err.log
stdout_logfile=/var/log/teacherassistant.2026.out.log
environment=LANGCHAIN_HOST="0.0.0.0",LANGCHAIN_BACKEND_HOST="0.0.0.0",LANGCHAIN_PORT="2026",LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true",LANGCHAIN_SERVE_CORS_HEADERS="*",LANGCHAIN_SERVE_CORS_ORIGINS="*"

[program:teacherassistant-2027]
directory=/opt/teacherassistant
command=/opt/teacherassistant/venv/bin/langgraph dev --host 0.0.0.0 --port 2027
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/teacherassistant.2027.err.log
stdout_logfile=/var/log/teacherassistant.2027.out.log
environment=LANGCHAIN_HOST="0.0.0.0",LANGCHAIN_BACKEND_HOST="0.0.0.0",LANGCHAIN_PORT="2027",LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true",LANGCHAIN_SERVE_CORS_HEADERS="*",LANGCHAIN_SERVE_CORS_ORIGINS="*"

[program:teacherassistant-2028]
directory=/opt/teacherassistant
command=/opt/teacherassistant/venv/bin/langgraph dev --host 0.0.0.0 --port 2028
user=root
autostart=true
autorestart=true
stderr_logfile=/var/log/teacherassistant.2028.err.log
stdout_logfile=/var/log/teacherassistant.2028.out.log
environment=LANGCHAIN_HOST="0.0.0.0",LANGCHAIN_BACKEND_HOST="0.0.0.0",LANGCHAIN_PORT="2028",LANGCHAIN_SERVE_ALLOW_CREDENTIALS="true",LANGCHAIN_SERVE_CORS_HEADERS="*",LANGCHAIN_SERVE_CORS_ORIGINS="*"
```

### 3.2 Nginx 配置

创建 Nginx 配置文件：

```bash
sudo nano /etc/nginx/sites-available/teacherassistant
```

配置内容：

```nginx
upstream teacherassistant_backend {
    # 使用 ip_hash 确保同一客户端请求始终发送到同一后端服务器
    ip_hash;
    
    # 后端服务器列表
    server 127.0.0.1:2024;
    server 127.0.0.1:2025;
    server 127.0.0.1:2026;
    server 127.0.0.1:2027;
    server 127.0.0.1:2028;
}

server {
    listen 8024;
    server_name your_domain.com;  # 替换为您的域名或公网IP

    location / {
        proxy_pass http://teacherassistant_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
```

启用配置：

```bash
sudo ln -s /etc/nginx/sites-available/teacherassistant /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 4. 安全配置

### 4.1 防火墙配置

```bash
# UFW配置
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

### 4.2 阿里云安全组配置

在阿里云控制台中：
1. 进入 ECS 实例的安全组配置
2. 添加以下入方向规则：
   - 端口范围：8024/8024（HTTP API 端口）
   - 端口范围：443/443（HTTPS，如果需要）
   - 授权对象：0.0.0.0/0

### 4.3 SSL 证书（可选）

```bash
# 安装 Certbot
sudo apt-get install -y certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d your_domain.com
```

## 5. 服务管理

### 5.1 启动服务

```bash
# 重新加载 Supervisor 配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动所有服务
sudo supervisorctl start teacherassistant:*

# 查看所有服务状态
sudo supervisorctl status teacherassistant:*
```

### 5.2 查看服务状态

```bash
# 查看所有实例状态
sudo supervisorctl status teacherassistant:*

# 查看各个实例的日志
tail -f /var/log/teacherassistant.202[4-8].out.log
tail -f /var/log/teacherassistant.202[4-8].err.log

# 查看 Nginx 访问日志
tail -f /var/log/nginx/access.log
```

## 6. 测试部署

### 6.1 基础测试

```bash
# 测试服务健康状态
curl http://localhost:2024/health

# 测试公网访问（替换为您的域名或IP）
curl http://your_domain.com/health
```

### 6.2 功能测试

创建测试脚本 `test.py`：

```python
from langgraph_sdk import get_sync_client
import time

def test_service():
    # 使用统一的 8024 端口访问
    client = get_sync_client(url="http://localhost:8024")
    
    test_input = {
        "messages": [{
            "role": "human",
            "content": {
                "lesson_coursename": "语文",
                "lesson_name": "测试课程",
                "lesson_count": "1",
                "lesson_target": "五年级"
            }
        }]
    }
    
    # 测试多次请求以验证负载均衡
    for i in range(5):
        try:
            print(f"\n测试请求 {i+1}:")
            response = client.runs.create(
                None,
                "lessonplan",
                input=test_input
            )
            print(f"测试成功: {response}")
            time.sleep(1)  # 间隔1秒
        except Exception as e:
            print(f"测试失败: {e}")

if __name__ == "__main__":
    test_service()
```

执行测试：

```bash
python test.py
```

## 7. 监控和维护

### 7.1 日志管理

配置日志轮转：

```bash
sudo nano /etc/logrotate.d/teacherassistant
```

```
/var/log/teacherassistant.*.log {
    daily
    rotate 7
    compress
    missingok
    notifempty
    create 0640 root root
}
```

### 7.2 性能监控

```bash
# 安装监控工具
sudo apt-get install -y htop iftop

# 查看系统资源
htop

# 查看网络流量
sudo iftop
```

### 7.3 负载监控

```bash
# 监控各个端口的连接数
watch -n 1 'netstat -an | grep "202[4-8]" | grep ESTABLISHED | wc -l'

# 查看各个实例的资源使用情况
ps aux | grep langgraph

# 监控 Nginx 负载均衡状态
watch -n 1 'grep "proxy_pass" /var/log/nginx/access.log | tail -n 10'
```

## 8. 故障排除

常见问题及解决方案：

1. 服务无法启动
   ```bash
   # 检查日志
   sudo supervisorctl tail teacherassistant
   
   # 检查端口占用
   sudo lsof -i :2024
   ```

2. 无法访问服务
   ```bash
   # 检查 Nginx 配置
   sudo nginx -t
   
   # 检查 Nginx 日志
   sudo tail -f /var/log/nginx/error.log
   ```

3. 性能问题
   ```bash
   # 检查系统负载
   top
   
   # 检查内存使用
   free -m
   ```

## 9. 备份策略

### 9.1 配置文件备份

定期备份以下文件：
- `/opt/teacherassistant/.env`
- `/etc/supervisor/conf.d/teacherassistant.conf`
- `/etc/nginx/sites-available/teacherassistant`

### 9.2 数据备份

根据需要备份：
- 临时文件目录
- 输出文件目录
- 日志文件

## 10. 更新维护

### 10.1 代码更新

```bash
cd /opt/teacherassistant
git pull
source venv/bin/activate
pip install -e .
sudo supervisorctl restart teacherassistant
```

### 10.2 依赖更新

```bash
source venv/bin/activate
pip install --upgrade -e .
sudo supervisorctl restart teacherassistant
``` 
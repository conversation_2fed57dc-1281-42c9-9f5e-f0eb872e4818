#!/usr/bin/env python3
"""
Token统计API测试服务器
用于本地测试，接收并显示token消费统计数据
"""

from flask import Flask, request, jsonify
import json
from datetime import datetime
import os

app = Flask(__name__)

# 存储接收到的token统计数据
token_data = []

@app.route('/api/v1/token/consumption', methods=['POST'])
def receive_token_consumption():
    """接收token消费统计"""
    try:
        # 获取请求数据
        data = request.get_json()
        
        # 添加时间戳
        data['timestamp'] = datetime.now().isoformat()
        
        # 存储数据
        token_data.append(data)
        
        # 打印到控制台
        print("\n" + "="*60)
        print(f"🔔 收到Token统计数据 #{len(token_data)}")
        print("="*60)
        print(f"时间: {data['timestamp']}")
        print(f"用户ID: {data.get('user_id', 'N/A')}")
        print(f"会话ID: {data.get('session_id', 'N/A')}")
        print(f"模型名称: {data.get('llm_name', 'N/A')}")
        print(f"平台: {data.get('plat_form', 'N/A')}")
        print(f"输入Token: {data.get('input_token', 0)}")
        print(f"输出Token: {data.get('output_token', 0)}")
        print(f"总Token: {data.get('input_token', 0) + data.get('output_token', 0)}")
        print("="*60)
        
        # 保存到文件
        save_to_file(data)
        
        # 返回成功响应
        return jsonify({
            "status": "success", 
            "message": "Token consumption recorded",
            "received_count": len(token_data)
        }), 200
        
    except Exception as e:
        print(f"❌ 处理token统计数据时出错: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/v1/token/stats', methods=['GET'])
def get_token_stats():
    """获取token统计概览"""
    if not token_data:
        return jsonify({"message": "暂无token统计数据"}), 200
    
    # 计算统计信息
    total_records = len(token_data)
    total_input_tokens = sum(item.get('input_token', 0) for item in token_data)
    total_output_tokens = sum(item.get('output_token', 0) for item in token_data)
    total_tokens = total_input_tokens + total_output_tokens
    
    # 按模型分组统计
    model_stats = {}
    for item in token_data:
        model = item.get('llm_name', 'unknown')
        if model not in model_stats:
            model_stats[model] = {'count': 0, 'input_tokens': 0, 'output_tokens': 0}
        model_stats[model]['count'] += 1
        model_stats[model]['input_tokens'] += item.get('input_token', 0)
        model_stats[model]['output_tokens'] += item.get('output_token', 0)
    
    return jsonify({
        "total_records": total_records,
        "total_input_tokens": total_input_tokens,
        "total_output_tokens": total_output_tokens,
        "total_tokens": total_tokens,
        "model_stats": model_stats,
        "recent_data": token_data[-5:]  # 最近5条记录
    })

@app.route('/api/v1/token/clear', methods=['POST'])
def clear_token_data():
    """清空token统计数据"""
    global token_data
    count = len(token_data)
    token_data = []
    print(f"🗑️ 已清空 {count} 条token统计记录")
    return jsonify({"message": f"已清空 {count} 条记录"}), 200

@app.route('/', methods=['GET'])
def index():
    """主页，显示API使用说明"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Token统计API测试服务器</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
            .method { color: #28a745; font-weight: bold; }
            h1 { color: #333; }
        </style>
    </head>
    <body>
        <h1>🔍 Token统计API测试服务器</h1>
        <p>当前运行在: <strong>http://localhost:8080</strong></p>
        
        <h2>可用接口：</h2>
        
        <div class="endpoint">
            <span class="method">POST</span> /api/v1/token/consumption
            <br>接收token消费统计数据
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /api/v1/token/stats
            <br>查看token统计概览
        </div>
        
        <div class="endpoint">
            <span class="method">POST</span> /api/v1/token/clear
            <br>清空统计数据
        </div>
        
        <h2>使用方法：</h2>
        <ol>
            <li>启动此服务器</li>
            <li>在.env文件中设置环境变量: TOKEN_API_DOMAIN=http://localhost:8080</li>
            <li>运行教案生成，观察控制台输出</li>
            <li>访问 <a href="/api/v1/token/stats">/api/v1/token/stats</a> 查看统计</li>
        </ol>
        
        <p><strong>当前统计记录数：</strong> <span id="count">加载中...</span></p>
        
        <script>
            fetch('/api/v1/token/stats')
                .then(r => r.json())
                .then(data => {
                    document.getElementById('count').textContent = data.total_records || 0;
                })
                .catch(() => {
                    document.getElementById('count').textContent = '0';
                });
        </script>
    </body>
    </html>
    """
    return html

def save_to_file(data):
    """保存数据到文件"""
    try:
        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 保存到JSON文件
        with open('logs/token_consumption.jsonl', 'a', encoding='utf-8') as f:
            f.write(json.dumps(data, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"保存数据到文件时出错: {str(e)}")

if __name__ == '__main__':
    print("🚀 启动Token统计API测试服务器...")
    print("📍 地址: http://localhost:8080")
    print("📊 统计接口: http://localhost:8080/api/v1/token/stats")
    print("🗂️ 数据保存: logs/token_consumption.jsonl")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=8080, debug=True) 
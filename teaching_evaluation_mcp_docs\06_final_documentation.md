# 教学评估MCP服务器 - 完整实施计划文档

## 1. 项目概述

### 1.1 项目简介
`teaching_evaluation_mcp` 是一个基于 FastMCP 框架构建的教学分析服务，专门用于分析课堂教学转录文本并生成结构化的教学评估报告。该项目集成了阿里云 DashScope 的 DeepSeek-V3 模型，采用五维评价框架对课堂教学进行全面、深入的专业分析。

### 1.2 核心特性
- **智能分析**：基于 DeepSeek-V3 大模型的深度教学分析
- **五维评价**：覆盖学生学习、教师教学、课程性质、课堂文化、社会情感五个维度
- **实时反馈**：通过 MCP 协议提供实时分析进度和状态更新
- **结构化输出**：生成标准化的 Markdown 格式分析报告和 JSON 元数据
- **多学科适配**：自动识别学科特点并进行针对性分析
- **高质量保证**：完整的测试体系和质量控制机制

### 1.3 技术架构
- **框架**：FastMCP (Model Context Protocol)
- **AI模型**：阿里云 DashScope DeepSeek-V3
- **数据验证**：Pydantic
- **异步处理**：Python asyncio
- **日志系统**：Python logging with rotation
- **测试框架**：pytest + coverage

## 2. 文档结构总览

本实施计划包含以下核心文档：

### 2.1 架构设计文档
**文件**: `01_architecture_design.md`
**内容概要**:
- 系统整体架构设计
- 组件关系和数据流
- 接口规范定义
- 错误处理策略
- 部署架构考虑

**关键设计决策**:
- 采用 FastMCP 框架确保与 AI 客户端的高效通信
- 使用 Pydantic 进行数据验证和序列化
- 实现异步处理提升性能
- 设计模块化架构便于维护和扩展

### 2.2 核心模块设计文档
**文件**: `02_core_modules_design.md`
**内容概要**:
- 配置管理模块 (`config.py`)
- 数据模型定义 (`models.py`)
- 日志系统实现 (`logger.py`)
- AI客户端集成策略
- 提示词管理框架

**关键实现**:
```python
class TeachingEvaluationConfig(BaseModel):
    ai_api_key: str
    ai_provider: str = "dashscope"
    ai_model_name: str = "deepseek-v3"
    output_dir: str = "outputs"
    log_level: str = "INFO"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
```

### 2.3 提示词工程文档
**文件**: `03_prompt_engineering.md`
**内容概要**:
- 原始提示词问题分析和修复
- 完整的提示词模板设计
- 提示词管理系统实现
- 优化策略和扩展性考虑

**核心成果**:
- 修复了原始 `prompt.md` 的格式问题
- 设计了结构化的提示词模板
- 实现了 `PromptManager` 类用于动态提示词管理
- 支持自定义要求和多学科适配

### 2.4 开发任务分解文档
**文件**: `04_development_breakdown.md`
**内容概要**:
- 5个开发阶段的详细任务分解
- 每个任务的具体目标、验收标准和时间估算
- 风险控制和应对策略
- 交付物清单和后续维护计划

**开发阶段**:
1. **基础框架搭建** (2-3天)
2. **AI集成与提示词管理** (2-3天)
3. **核心业务逻辑** (3-4天)
4. **MCP服务器实现** (2天)
5. **测试与优化** (2-3天)

### 2.5 质量保证策略文档
**文件**: `05_quality_assurance.md`
**内容概要**:
- 代码质量保证机制
- 全面的测试策略 (单元测试、集成测试、端到端测试)
- 性能保证和监控
- 安全性检查和用户体验验证
- 质量度量和报告系统

**质量标准**:
- 代码覆盖率：核心模块 ≥ 90%，整体 ≥ 85%
- 性能指标：单次分析 < 60秒，内存使用 < 512MB
- 安全性：通过安全扫描，无高危漏洞
- 可用性：99.5% 正常运行时间

## 3. 关键技术实现

### 3.1 MCP服务器实现
```python
# main.py 核心结构
from fastmcp import FastMCP, Context
from src.teaching_evaluation.analysis_service import TeachingAnalysisService
from src.teaching_evaluation.models import TeachingAnalysisRequest

app = FastMCP("Teaching Evaluation MCP Server")

@app.tool()
async def analyze_teaching_transcript(
    transcript: str,
    custom_requirements: str = "",
    output_dir: str = "outputs",
    ctx: Context = None
) -> dict:
    """分析课堂教学转录文本"""
    service = TeachingAnalysisService()
    
    # 实时进度报告
    await ctx.info({"status": "started", "message": "开始分析课堂教学转录"})
    
    try:
        result = await service.analyze_transcript(
            transcript=transcript,
            custom_requirements=custom_requirements,
            output_dir=output_dir,
            progress_callback=lambda update: ctx.info(update)
        )
        
        await ctx.info({"status": "completed", "message": "分析完成"})
        return result.model_dump()
        
    except Exception as e:
        await ctx.error({
            "type": "analysis_error",
            "message": f"分析过程中发生错误: {str(e)}",
            "details": str(e)
        })
        raise
```

### 3.2 AI服务集成
```python
# ai_service.py 核心实现
class AIService:
    def __init__(self, config: TeachingEvaluationConfig):
        self.config = config
        self.client = DashScopeClient(config.ai_api_key)
        self.prompt_manager = PromptManager()
    
    async def analyze_transcript(
        self, 
        transcript: str, 
        custom_requirements: str = ""
    ) -> Tuple[str, ModelUsageInfo]:
        """分析教学转录"""
        # 构建提示词
        prompt = self.prompt_manager.build_analysis_prompt(
            transcript=transcript,
            custom_requirements=custom_requirements
        )
        
        # 调用AI模型
        response = await self.client.chat_completion(
            model=self.config.ai_model_name,
            messages=[{"role": "user", "content": prompt}]
        )
        
        # 提取结果和用量信息
        content = response["choices"][0]["message"]["content"]
        usage = ModelUsageInfo(
            provider="dashscope",
            model=self.config.ai_model_name,
            input_tokens=response["usage"]["prompt_tokens"],
            output_tokens=response["usage"]["completion_tokens"],
            total_tokens=response["usage"]["total_tokens"]
        )
        
        return content, usage
```

### 3.3 实时进度报告
```python
# analysis_service.py 进度报告实现
async def analyze_teaching_transcript(
    self,
    transcript: str,
    output_dir: str,
    progress_callback: Optional[Callable] = None
) -> TeachingAnalysisResponse:
    """执行教学分析"""
    
    async def report_progress(status: str, message: str, **kwargs):
        if progress_callback:
            await progress_callback({
                "status": status,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                **kwargs
            })
    
    try:
        # 1. 开始分析
        await report_progress("started", "开始分析课堂教学转录")
        
        # 2. 预处理
        await report_progress("preprocessing", "正在预处理转录文本")
        processed_transcript = self._preprocess_transcript(transcript)
        
        # 3. AI分析
        await report_progress("ai_analysis", "正在调用AI模型进行分析")
        analysis_content, usage_info = await self.ai_service.analyze_transcript(
            transcript=processed_transcript
        )
        
        # 4. 用量报告
        await report_progress("model_usage", "AI模型用量统计", usage=usage_info.model_dump())
        
        # 5. 生成输出
        await report_progress("generating_output", "正在生成分析报告")
        output_files = await self._generate_output_files(
            analysis_content, usage_info, output_dir
        )
        
        # 6. 完成
        await report_progress("completed", "分析完成", output_files=output_files)
        
        return TeachingAnalysisResponse(
            success=True,
            report_content=analysis_content,
            output_files=output_files,
            usage_info=usage_info
        )
        
    except Exception as e:
        await report_progress("error", f"分析失败: {str(e)}")
        raise
```

## 4. 项目结构

```
teaching_evaluation_mcp/
├── README.md                           # 项目说明文档
├── pyproject.toml                      # 项目配置和依赖
├── .env.example                        # 环境变量示例
├── .gitignore                          # Git忽略文件
├── main.py                             # MCP服务器入口
├── src/
│   └── teaching_evaluation/
│       ├── __init__.py                 # 模块初始化
│       ├── config.py                   # 配置管理
│       ├── models.py                   # 数据模型
│       ├── logger.py                   # 日志系统
│       ├── ai_client.py                # AI客户端
│       ├── ai_service.py               # AI服务封装
│       ├── prompt_manager.py           # 提示词管理
│       ├── analysis_service.py         # 核心分析服务
│       ├── file_handler.py             # 文件处理
│       ├── output_formatter.py         # 输出格式化
│       ├── exceptions.py               # 自定义异常
│       ├── security.py                 # 安全验证
│       └── performance_monitor.py      # 性能监控
├── tests/                              # 测试目录
│   ├── __init__.py
│   ├── conftest.py                     # 测试配置
│   ├── fixtures/                       # 测试数据
│   ├── test_models.py                  # 模型测试
│   ├── test_ai_service.py              # AI服务测试
│   ├── test_analysis_service.py        # 分析服务测试
│   ├── test_integration.py             # 集成测试
│   ├── test_performance.py             # 性能测试
│   └── test_mcp_server.py              # MCP服务器测试
├── docs/                               # 文档目录
│   ├── 01_architecture_design.md       # 架构设计
│   ├── 02_core_modules_design.md       # 核心模块设计
│   ├── 03_prompt_engineering.md        # 提示词工程
│   ├── 04_development_breakdown.md     # 开发任务分解
│   ├── 05_quality_assurance.md         # 质量保证策略
│   ├── 06_final_documentation.md       # 最终文档总结
│   ├── input_example.md                # 输入示例
│   ├── output_example.md               # 输出示例
│   ├── dashscope_integration_guide.md  # DashScope集成指南
│   └── prompt_fixed.md                 # 修复后的提示词
├── scripts/                            # 脚本目录
│   ├── run_tests.sh                    # 测试运行脚本
│   ├── pre_commit_check.sh             # 提交前检查
│   ├── collect_metrics.py              # 指标收集
│   └── generate_quality_report.py      # 质量报告生成
├── examples/                           # 示例目录
│   ├── basic_usage.py                  # 基础使用示例
│   └── advanced_usage.py               # 高级使用示例
└── outputs/                            # 输出目录
    └── .gitkeep
```

## 5. 实施时间线

### 第1周：基础框架搭建
- **Day 1-2**: 项目结构创建、依赖配置、日志系统
- **Day 3**: 基础数据模型定义和验证

### 第2周：AI集成与核心逻辑
- **Day 1-2**: DashScope客户端集成、提示词管理
- **Day 3-4**: 核心分析服务实现

### 第3周：MCP服务器与测试
- **Day 1-2**: MCP服务器实现、接口调试
- **Day 3-5**: 测试编写、质量保证、文档完善

## 6. 质量保证措施

### 6.1 自动化测试
- **单元测试覆盖率**: ≥ 85%
- **集成测试**: 完整流程验证
- **性能测试**: 响应时间和资源使用
- **安全测试**: 漏洞扫描和输入验证

### 6.2 代码质量
- **代码规范**: PEP 8 + black + ruff
- **类型检查**: mypy 静态类型检查
- **复杂度控制**: radon 复杂度分析
- **文档完整性**: 100% docstring 覆盖

### 6.3 持续集成
- **GitHub Actions**: 自动化测试和部署
- **代码审查**: Pull Request 必须审查
- **质量门禁**: 测试通过才能合并
- **自动化报告**: 质量指标自动收集

## 7. 风险管控

### 7.1 技术风险
- **AI API限流**: 实现重试机制和降级策略
- **性能瓶颈**: 异步处理和资源优化
- **数据安全**: 敏感信息加密和访问控制

### 7.2 项目风险
- **进度延期**: 敏捷开发和优先级管理
- **质量问题**: 严格的测试和审查流程
- **需求变更**: 模块化设计和接口抽象

## 8. 成功标准

### 8.1 功能标准
- [ ] 支持多种格式的课堂转录文本输入
- [ ] 生成符合五维评价框架的完整分析报告
- [ ] 实现实时进度反馈和错误处理
- [ ] 输出结构化的Markdown报告和JSON元数据

### 8.2 性能标准
- [ ] 单次分析响应时间 < 60秒
- [ ] 内存使用峰值 < 512MB
- [ ] 支持并发处理多个分析请求
- [ ] 99.5% 服务可用性

### 8.3 质量标准
- [ ] 测试覆盖率 ≥ 85%
- [ ] 通过所有安全扫描
- [ ] 代码质量评分 ≥ 8.0/10
- [ ] 用户满意度 ≥ 90%

## 9. 后续发展规划

### 9.1 短期优化 (1-3个月)
- 性能优化和bug修复
- 用户反馈收集和改进
- 文档完善和示例补充

### 9.2 中期扩展 (3-6个月)
- 支持更多文件格式和输入方式
- 增加可视化报告功能
- 实现批量处理和历史记录

### 9.3 长期发展 (6个月以上)
- 多语言支持和国际化
- Web界面和协作功能
- 与教育管理系统集成

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护团队**: Teaching Evaluation MCP Development Team

通过以上完整的实施计划，`teaching_evaluation_mcp` 项目将能够为教育工作者提供专业、高效、可靠的课堂教学分析服务，助力教学质量的持续改进和提升。

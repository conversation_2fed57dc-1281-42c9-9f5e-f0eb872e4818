# 核心模块详细设计

## 1. 配置管理模块 (config.py)

### 1.1 设计目标
- 集中管理所有配置项
- 支持环境变量和默认值
- 类型安全的配置验证
- 敏感信息保护

### 1.2 完整实现
```python
"""课堂教学分析配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()

class TeachingEvaluationConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- AI服务配置 ---
    ai_api_key: str = Field(description="AI服务API密钥")
    ai_provider: str = Field(
        default="dashscope",
        description="AI服务提供商（固定为dashscope）"
    )
    ai_model_name: str = Field(
        default="deepseek-v3",
        description="AI模型名称"
    )
    ai_timeout: int = Field(
        default=120,
        description="AI服务请求超时时间（秒）"
    )
    ai_max_retries: int = Field(
        default=3,
        description="AI服务请求最大重试次数"
    )
    
    # --- 分析参数配置 ---
    max_tokens: int = Field(default=8000, description="最大token数量")
    temperature: float = Field(default=0.7, description="生成温度")
    
    # --- 输入限制配置 ---
    max_transcript_length: int = Field(
        default=50000, 
        description="转录文本最大长度（字符数）"
    )
    
    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的分析报告文件存放的根目录")
    
    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @validator('ai_timeout')
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError('超时时间必须大于0')
        return v
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0 <= v <= 2:
            raise ValueError('温度值必须在0-2之间')
        return v
    
    @classmethod
    def from_env(cls) -> "TeachingEvaluationConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如AI_API_KEY）。

        Returns:
            TeachingEvaluationConfig: 一个包含所有最终配置值的实例。
        """
        ai_api_key = os.getenv("AI_API_KEY")
        if not ai_api_key:
            raise ValueError("请设置环境变量 AI_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上两级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            ai_api_key=ai_api_key,
            ai_provider=os.getenv("AI_PROVIDER", "dashscope"),
            ai_model_name=os.getenv("AI_MODEL_NAME", "deepseek-v3"),
            ai_timeout=int(os.getenv("AI_TIMEOUT", "120")),
            ai_max_retries=int(os.getenv("AI_MAX_RETRIES", "3")),
            max_tokens=int(os.getenv("MAX_TOKENS", "8000")),
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_transcript_length=int(os.getenv("MAX_TRANSCRIPT_LENGTH", "50000")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        )
```

## 2. 数据模型模块 (models.py)

### 2.1 设计目标
- 类型安全的数据结构
- 输入输出验证
- 序列化支持
- 扩展性考虑

### 2.2 完整实现
```python
"""课堂教学分析数据模型"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
import uuid

class TeachingTranscript(BaseModel):
    """课堂教学转录文本"""
    content: str = Field(description="转录文本内容")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="转录文本的元数据信息"
    )
    
    @validator('content')
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError('转录文本内容不能为空')
        if len(v) > 50000:
            raise ValueError('转录文本长度不能超过50000字符')
        return v.strip()

class AnalysisRequest(BaseModel):
    """分析请求"""
    transcript: str = Field(description="课堂教学转录文本")
    analysis_id: Optional[str] = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="分析任务唯一标识符"
    )
    custom_requirements: Optional[str] = Field(
        default="", 
        description="用户自定义分析要求"
    )
    
    @validator('transcript')
    def validate_transcript(cls, v):
        if not v or not v.strip():
            raise ValueError('转录文本不能为空')
        return v.strip()

class FiveDimensionAnalysis(BaseModel):
    """五维分析结果"""
    student_learning: Dict[str, str] = Field(description="学生学习维度分析")
    teacher_teaching: Dict[str, str] = Field(description="教师教学维度分析")
    curriculum_nature: Dict[str, str] = Field(description="课程性质维度分析")
    classroom_culture: Dict[str, str] = Field(description="课堂文化维度分析")
    social_emotion: Dict[str, str] = Field(description="社会情感维度分析")

class AnalysisReport(BaseModel):
    """分析报告"""
    analysis_id: str = Field(description="分析任务唯一标识符")
    overall_evaluation: str = Field(description="总体评价")
    five_dimensions: FiveDimensionAnalysis = Field(description="五维分析结果")
    summary_and_suggestions: str = Field(description="总结与建议")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    word_count: int = Field(description="报告字数")
    analysis_duration: Optional[float] = Field(description="分析耗时（秒）")

class ModelUsageInfo(BaseModel):
    """模型用量信息"""
    vendor: str = Field(description="提供模型的厂商，例如：dashscope, doubao")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")

class ProgressUpdate(BaseModel):
    """进度更新消息"""
    status: str = Field(description="当前状态")
    message: str = Field(description="状态描述")
    progress: Optional[float] = Field(description="进度百分比 (0-100)")
    details: Optional[Dict[str, Any]] = Field(description="详细信息")

class ErrorInfo(BaseModel):
    """错误信息"""
    error_type: str = Field(description="错误类型")
    error_message: str = Field(description="错误消息")
    error_details: Optional[str] = Field(description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误发生时间")

class AnalysisMetadata(BaseModel):
    """分析元数据"""
    analysis_id: str = Field(description="分析任务ID")
    transcript_length: int = Field(description="转录文本长度")
    report_word_count: int = Field(description="报告字数")
    analysis_duration: float = Field(description="分析耗时")
    model_usage: ModelUsageInfo = Field(description="模型用量信息")
    created_at: datetime = Field(description="创建时间")
    config_snapshot: Dict[str, Any] = Field(description="配置快照")
```

## 3. 日志系统模块 (logger.py)

### 3.1 设计目标
- 继承 `picturebook_generator_mcp` 的日志系统设计
- 适配教学分析场景的命名
- 保持完全一致的功能特性

### 3.2 实现策略
```python
"""教学分析日志配置模块

基于 picturebook_generator_mcp 的日志系统设计，
适配教学分析场景的命名规范。
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional

class TeachingEvaluationLogger:
    """
    教学分析日志管理类，基于 PictureBookLogger 的设计模式。
    
    提供统一的、可配置的日志记录功能，支持文件输出、控制台输出和不同日志级别。
    使用单例模式确保全局日志配置只被设置一次。
    """
    
    _loggers = {}
    _initialized = False
    
    @classmethod
    def setup_logging(
        cls,
        log_dir: str = "logs",
        log_level: str = "INFO",
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        console_output: bool = True
    ) -> None:
        """设置全局日志系统"""
        if cls._initialized:
            return
            
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置根日志级别
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - '
                '%(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加文件处理器 - 详细日志
        log_filename = os.path.join(
            log_dir, 
            f"teaching_evaluation_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # 添加错误日志文件处理器
        error_log_filename = os.path.join(
            log_dir,
            f"teaching_evaluation_error_{datetime.now().strftime('%Y%m%d')}.log"
        )
        error_file_handler = logging.handlers.RotatingFileHandler(
            filename=error_log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_file_handler)
        
        # 添加控制台处理器（如果启用）
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            console_handler.setFormatter(simple_formatter)
            root_logger.addHandler(console_handler)
        
        cls._initialized = True
        
        # 记录初始化信息
        logger = cls.get_logger("TeachingEvaluationLogger")
        logger.info("教学分析日志系统初始化完成")
        logger.info(f"日志目录: {log_dir}")
        logger.info(f"日志级别: {log_level}")
        logger.info(f"控制台输出: {console_output}")
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """获取指定名称的日志器实例"""
        if name not in cls._loggers:
            logger = logging.getLogger(name)
            cls._loggers[name] = logger
        
        return cls._loggers[name]

def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取日志器的便捷函数"""
    if name is None:
        import inspect
        frame = inspect.currentframe()
        if frame and frame.f_back:
            name = frame.f_back.f_globals.get('__name__', 'unknown')
        else:
            name = 'unknown'
    
    return TeachingEvaluationLogger.get_logger(name)

# 初始化默认日志配置
def init_default_logging() -> None:
    """在模块首次加载时，执行一次默认的日志初始化"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    log_dir = os.path.join(project_root, "logs")
    
    log_level = os.getenv("LOG_LEVEL", "INFO")
    console_output = os.getenv("LOG_CONSOLE", "true").lower() == "true"
    
    TeachingEvaluationLogger.setup_logging(
        log_dir=log_dir,
        log_level=log_level,
        console_output=console_output
    )

# 在模块加载时初始化日志
if not TeachingEvaluationLogger._initialized:
    init_default_logging()
```

## 4. AI客户端模块 (ai_client.py)

### 4.1 设计目标
- 复用 `transcript_generator_mcp` 的 DashScope 客户端
- 适配教学分析的特定需求
- 保持完整的错误处理和用量统计

### 4.2 实现策略
直接复用并适配现有的 DashScope 客户端实现，主要修改：
- 更新模块导入路径
- 调整日志记录器名称
- 根据分析任务调整默认参数

## 5. 提示词管理模块 (prompt_manager.py)

### 5.1 设计目标（简化版）
- 使用统一的提示词模板
- 简化实现逻辑，移除学科识别
- 专注于核心教学评价功能

### 5.2 实现框架
```python
"""教学分析提示词管理模块（简化版）"""
from typing import Optional
from pathlib import Path
from .logger import get_logger

class PromptManager:
    """提示词管理器（基于prompt_fixed.md设计，支持评分功能）"""

    def __init__(self, templates_dir: str = "templates"):
        self.logger = get_logger(__name__)
        self.templates_dir = Path(templates_dir)
        self.base_template = self._load_base_template()

    def build_analysis_prompt(self, transcript: str,
                            custom_requirements: str = "") -> str:
        """构建统一的教学分析提示词（包含评分要求）"""
        prompt = self.base_template.format(
            transcript=transcript,
            custom_requirements=custom_requirements if custom_requirements else ""
        )
        return prompt

    def _load_base_template(self) -> str:
        """加载基础提示词模板"""
        template_path = self.templates_dir / "base_template.txt"
        if template_path.exists():
            return template_path.read_text(encoding='utf-8')

        # 基于prompt_fixed.md的完整模板（增加评分功能）
        return """
# 课堂教学分析专家提示词

## 角色定义
你是一位顶级的教育评估专家和教学分析顾问，拥有超过20年的课堂观察和教师发展指导经验，你的专业知识覆盖了从人文社科到自然科学的多个小学学科领域。你尤其擅长从课堂的细微互动中，洞察其背后深层的教育学和心理学逻辑，并能结合不同学科的特质进行精准分析。你的分析报告以其深刻的洞察力、严谨的逻辑、丰富的细节和专业的术语而著称。

## 核心任务
你的任务是根据下面提供的【课堂教学转录文本】，撰写一份全面、深入、专业的《课堂教学分析报告》，并提供五维度评分数据用于生成雷达图。

## 评价框架与要求
报告必须严格遵循以下五维评价框架，覆盖全部25个评价视角。报告总字数必须超过2000字。

### 五维评分要求（用于雷达图生成）
在完成定性分析的基础上，必须为以下五个维度提供量化评分（1-10分制）：
1. **学生学习**：综合评估学生在准备、倾听、互动、自主、达成五个方面的表现
2. **教师教学**：综合评估教师在环节、呈示、对话、指导、机智五个方面的表现
3. **课程性质**：综合评估课程在目标、内容、实施、评价、资源五个方面的表现
4. **课堂文化**：综合评估课堂在思考、民主、创新、关爱、特质五个方面的表现
5. **社会情感**：综合评估学生在情绪力、共情力、合作力、责任力、创造力五个方面的表现

评分标准：
- 9-10分：优秀，该维度表现突出，值得推广
- 7-8分：良好，该维度表现较好，有亮点
- 5-6分：合格，该维度表现一般，符合基本要求
- 3-4分：待改进，该维度存在明显不足
- 1-2分：需要重点改进，该维度表现较差

### 报告结构

#### 1. 一、总体评价（约200-300字）
对整堂课进行一个高度概括、观点鲜明的总体评价，点出其核心亮点与主要待改进之处。

#### 2. 二、五维评估
这是报告的主体，必须对以下五个维度及其所有子项进行逐一分析。

**对于每一个子项（共25个），你的分析都必须遵循以下模式：**
1. **观点陈述**：首先明确给出你对该子项的评价
2. **证据列举**：然后，【必须】从【课堂教学转录文本】中引用1到2个具体的课堂情景、师生对话或行为作为核心证据
3. **深度分析**：最后，对该证据进行深入的教育学分析

每个子项的分析论述部分（观点+证据+分析）应不少于80字。

##### 五维框架详情：

**（一）学生学习**
- 准备：学生课前知识、物品或心理准备情况
- 倾听：学生是否能有效倾听教师指导和同伴发言
- 互动：学生参与课堂问答、讨论和活动的频率与质量
- 自主：学生在没有教师直接指令下，自主探索、思考和操作的表现
- 达成：学生对本节课核心知识与技能目标的掌握程度

**（二）教师教学**
- 环节：教学流程的设计是否清晰、完整、逻辑连贯
- 呈示：教师呈现教学内容的方式是否清晰、有效、富有吸引力
- 对话：师生、生生对话的质量，教师是否能有效引导和启发
- 指导：教师在学生活动中的巡视、指导和支持是否及时、有效
- 机智：教师处理课堂意外情况或学生独特想法的应变能力

**（三）课程性质**
- 目标：本课的教学目标是否明确、恰当，并得到有效落实
- 内容：教学内容的选择是否贴近学生生活，具有科学性、趣味性和探究空间
- 实施：教学计划的实际执行情况，节奏和时间把控是否合理
- 评价：教师在课堂中运用的评价方式是否多样、有效，能否激励学生
- 资源：对教具、文本、多媒体等教学资源的利用是否充分、得当

**（四）课堂文化**
- 思考：课堂是否鼓励并引发了学生的深度思考和批判性思维
- 民主：课堂氛围是否民主、平等，学生能否自由安全地表达不同意见
- 创新：课堂是否鼓励学生提出创新的想法或解决方法
- 关爱：师生关系是否融洽，教师是否关注到学生的情感需求
- 特质：课堂是否体现了良好的教学特质和专业性

**（五）社会情感**
- 情绪力：学生在课堂上整体的情绪状态是积极投入还是消极被动
- 共情力：学生是否表现出理解、尊重和体谅他人的能力
- 合作力：学生在同桌或小组活动中展现出的合作意愿和能力
- 责任力：学生对自己学习任务的负责态度
- 创造力：学生在解决问题过程中展现出的想象力和创造性思维

#### 3. 三、总结与建议（约200-300字）
在五维分析的基础上，对整堂课进行最终总结，并针对待改进之处，提出2-3条具体、可操作的教学建议。

## 输出格式要求
请严格按照以下格式输出分析报告：

```markdown
# 《课堂教学分析报告》

## 一、总体评价
[总体评价内容]

## 二、五维评估
[详细的定性分析内容，包含25个子项的完整分析]

## 三、总结与建议
[总结与建议内容]

## 四、五维度评分（用于雷达图生成）
```json
{{
  "学生学习": 8.5,
  "教师教学": 7.8,
  "课程性质": 8.2,
  "课堂文化": 7.5,
  "社会情感": 8.0
}}
```
```

---

【课堂教学转录文本】
{transcript}

{custom_requirements}
"""

    def extract_scores_from_response(self, response: str) -> dict:
        """从AI响应中提取五维度评分数据"""
        import json
        import re

        # 查找JSON格式的评分数据
        json_pattern = r'```json\s*(\{[^}]*\})\s*```'
        match = re.search(json_pattern, response, re.DOTALL)

        if match:
            try:
                scores_data = json.loads(match.group(1))
                return scores_data
            except json.JSONDecodeError:
                self.logger.warning("无法解析评分JSON数据，使用默认评分")

        # 如果JSON解析失败，返回默认评分
        return {
            "学生学习": 7.0,
            "教师教学": 7.0,
            "课程性质": 7.0,
            "课堂文化": 7.0,
            "社会情感": 7.0
        }
```

## 6. 雷达图生成模块 (chart_generator.py)

### 6.1 设计目标
- 基于Plotly生成专业的教学评价雷达图
- 支持从JSON格式中提取五维度评分数据
- 提供HTML和PNG两种输出格式
- 参考雷达图_Plotly参考1.md的最佳实践
- 支持数据标准化和可视化优化

### 6.2 实现框架
```python
"""雷达图生成模块（基于雷达图_Plotly参考1.md优化）"""
import json
import re
import plotly.graph_objects as go
import plotly.io as pio
from pathlib import Path
from typing import Dict, Tuple, Optional, List
from .logger import get_logger

class RadarChartGenerator:
    """基于Plotly的教学评价雷达图生成器"""

    def __init__(self, output_dir: str = "outputs"):
        self.logger = get_logger(__name__)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 五个维度的标准名称（按教学评价逻辑排序）
        self.dimensions = [
            "学生学习",
            "教师教学",
            "课程性质",
            "课堂文化",
            "社会情感"
        ]

        # 设置Plotly的默认配置
        pio.kaleido.scope.mathjax = None  # 避免MathJax相关问题

    def extract_scores_from_json(self, analysis_text: str) -> Dict[str, float]:
        """从AI分析结果中提取JSON格式的五维度评分"""
        # 优先查找JSON格式的评分数据
        json_pattern = r'```json\s*(\{[^}]*\})\s*```'
        match = re.search(json_pattern, analysis_text, re.DOTALL)

        if match:
            try:
                scores_data = json.loads(match.group(1))
                self.logger.info("成功从JSON格式中提取评分数据")
                return scores_data
            except json.JSONDecodeError as e:
                self.logger.warning(f"JSON解析失败: {e}")

        # 如果JSON解析失败，尝试其他格式
        return self._extract_scores_fallback(analysis_text)

    def _extract_scores_fallback(self, analysis_text: str) -> Dict[str, float]:
        """备用的评分提取方法"""
        scores = {}

        # 使用正则表达式提取评分
        for dimension in self.dimensions:
            # 尝试多种格式
            patterns = [
                rf"【{dimension}：(\d+(?:\.\d+)?)分】",
                rf"{dimension}[：:]\s*(\d+(?:\.\d+)?)分",
                rf"{dimension}.*?(\d+(?:\.\d+)?)分"
            ]

            found = False
            for pattern in patterns:
                match = re.search(pattern, analysis_text)
                if match:
                    scores[dimension] = float(match.group(1))
                    found = True
                    break

            if not found:
                self.logger.warning(f"未能从分析文本中提取{dimension}的评分，使用默认值")
                scores[dimension] = 7.0  # 使用中等偏上的默认分数

        return scores

    def generate_radar_chart(self, scores: Dict[str, float],
                           title: str = "课堂教学评价雷达图",
                           timestamp: str = None) -> Tuple[str, str]:
        """生成专业的教学评价雷达图（基于参考文档优化）"""

        if timestamp is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 验证和标准化数据
        validated_scores = self._validate_and_normalize_scores(scores)

        # 准备数据（确保维度顺序一致）
        categories = [dim for dim in self.dimensions if dim in validated_scores]
        values = [validated_scores[dim] for dim in categories]

        # 闭合多边形（参考文档要求）
        values += values[:1]
        categories += categories[:1]

        # 创建雷达图
        fig = go.Figure()

        # 添加主要数据轨迹
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='教学评价',
            line=dict(color='rgb(0, 123, 255)', width=3),
            fillcolor='rgba(0, 123, 255, 0.25)',
            marker=dict(size=8, color='rgb(0, 123, 255)')
        ))

        # 添加参考线（平均水平线）
        avg_score = 7.0  # 良好水平参考线
        fig.add_trace(go.Scatterpolar(
            r=[avg_score] * len(categories),
            theta=categories,
            mode='lines',
            line=dict(color='rgba(255, 0, 0, 0.6)', dash='dash', width=2),
            name=f'参考水平({avg_score}分)',
            showlegend=True
        ))

        # 设置专业的布局（参考文档样式）
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 10],
                    tickmode='linear',
                    tick0=0,
                    dtick=2,
                    tickfont=dict(size=10),
                    gridcolor='rgba(128, 128, 128, 0.3)'
                ),
                angularaxis=dict(
                    tickfont=dict(size=12, family="Microsoft YaHei, Arial, sans-serif"),
                    rotation=90,  # 从顶部开始
                    direction='clockwise'  # 顺时针方向
                )
            ),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=-0.1,
                xanchor="center",
                x=0.5
            ),
            title=dict(
                text=title,
                x=0.5,
                y=0.95,
                font=dict(size=18, family="Microsoft YaHei, Arial, sans-serif", color='rgb(50, 50, 50)')
            ),
            font=dict(size=12, family="Microsoft YaHei, Arial, sans-serif"),
            width=700,
            height=600,
            margin=dict(t=80, b=80, l=80, r=80),
            paper_bgcolor='white',
            plot_bgcolor='white'
        )

        # 保存HTML文件
        html_filename = f"radar_chart_{timestamp}.html"
        html_path = self.output_dir / html_filename
        fig.write_html(
            str(html_path),
            config={'displayModeBar': True, 'displaylogo': False}
        )

        # 保存PNG文件
        png_filename = f"radar_chart_{timestamp}.png"
        png_path = self.output_dir / png_filename
        try:
            fig.write_image(str(png_path), width=700, height=600, scale=2)
        except Exception as e:
            self.logger.warning(f"PNG保存失败: {e}，请确保已安装kaleido")
            png_path = None

        self.logger.info(f"雷达图已生成: HTML={html_path}, PNG={png_path}")

        return str(html_path), str(png_path) if png_path else None

    def _validate_and_normalize_scores(self, scores: Dict[str, float]) -> Dict[str, float]:
        """验证和标准化评分数据"""
        validated = {}

        for dimension in self.dimensions:
            if dimension in scores:
                score = scores[dimension]
                # 确保分数在合理范围内
                if 0 <= score <= 10:
                    validated[dimension] = score
                else:
                    self.logger.warning(f"维度{dimension}的评分{score}超出范围，调整为7.0")
                    validated[dimension] = 7.0
            else:
                self.logger.warning(f"缺少维度{dimension}的评分，使用默认值7.0")
                validated[dimension] = 7.0

        return validated

    def validate_scores(self, scores: Dict[str, float]) -> bool:
        """验证评分数据的有效性"""
        if not scores:
            self.logger.error("评分数据为空")
            return False

        # 检查是否包含所有必需的维度
        missing_dimensions = set(self.dimensions) - set(scores.keys())
        if missing_dimensions:
            self.logger.warning(f"缺少维度: {missing_dimensions}")

        # 检查评分范围
        for dimension, score in scores.items():
            if not isinstance(score, (int, float)):
                self.logger.error(f"维度{dimension}的评分{score}不是数值类型")
                return False
            if not (0 <= score <= 10):
                self.logger.error(f"维度{dimension}的评分{score}超出范围[0,10]")
                return False

        return True

    def generate_chart_from_analysis(self, analysis_text: str,
                                   title: str = "课堂教学评价雷达图",
                                   timestamp: str = None) -> Tuple[str, Optional[str]]:
        """从分析文本直接生成雷达图的便捷方法"""
        # 提取评分数据
        scores = self.extract_scores_from_json(analysis_text)

        # 验证数据
        if not self.validate_scores(scores):
            self.logger.error("评分数据验证失败，无法生成雷达图")
            return None, None

        # 生成图表
        return self.generate_radar_chart(scores, title, timestamp)
```

## 7. 教学分析服务模块 (analyzer.py)

### 6.1 设计目标
- 核心业务逻辑实现
- 完整的分析流程管理
- 实时进度反馈
- 错误处理和恢复

### 6.2 实现框架
```python
"""教学分析服务模块"""
import json
import os
from datetime import datetime
from typing import Tuple, Optional
from fastmcp import Context

from .config import TeachingEvaluationConfig
from .models import AnalysisRequest, AnalysisReport, ModelUsageInfo, ProgressUpdate
from .ai_client import DashScopeClient
from .prompt_manager import PromptManager
from .chart_generator import RadarChartGenerator
from .logger import get_logger

class TeachingAnalyzer:
    """教学分析服务（集成雷达图生成功能）"""

    def __init__(self, config: TeachingEvaluationConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.ai_client = DashScopeClient(config)
        self.prompt_manager = PromptManager()
        self.chart_generator = RadarChartGenerator(config.output_dir)
    
    async def analyze_teaching(
        self, 
        request: AnalysisRequest, 
        ctx: Context
    ) -> Tuple[AnalysisReport, ModelUsageInfo]:
        """执行教学分析的完整流程"""
        
        start_time = datetime.now()
        
        try:
            # 1. 发送开始状态
            await ctx.info(ProgressUpdate(
                status="started",
                message="开始分析课堂教学转录文本",
                progress=0
            ).model_dump())
            
            # 2. 构建提示词
            await ctx.info(ProgressUpdate(
                status="preparing",
                message="构建分析提示词",
                progress=10
            ).model_dump())
            
            prompt = self.prompt_manager.build_analysis_prompt(
                request.transcript, 
                request.custom_requirements
            )
            
            # 3. 调用AI模型
            await ctx.info(ProgressUpdate(
                status="analyzing",
                message="正在调用AI模型进行分析",
                progress=20
            ).model_dump())
            
            ai_response, usage_info = await self.ai_client.generate_text(
                prompt=prompt,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            # 4. 解析结果
            await ctx.info(ProgressUpdate(
                status="parsing",
                message="解析分析结果",
                progress=60
            ).model_dump())

            report = self._parse_ai_response(ai_response, request.analysis_id)

            # 5. 生成雷达图
            await ctx.info(ProgressUpdate(
                status="generating_chart",
                message="生成五维度评价雷达图",
                progress=75
            ).model_dump())

            html_path, png_path = await self._generate_radar_chart(
                ai_response, request.analysis_id, ctx
            )

            # 6. 保存文件
            await ctx.info(ProgressUpdate(
                status="saving",
                message="保存分析报告和雷达图",
                progress=90
            ).model_dump())

            await self._save_analysis_results(report, usage_info, html_path, png_path)
            
            # 6. 完成
            duration = (datetime.now() - start_time).total_seconds()
            report.analysis_duration = duration
            
            await ctx.info(ProgressUpdate(
                status="completed",
                message=f"分析完成，耗时 {duration:.2f} 秒",
                progress=100
            ).model_dump())
            
            return report, usage_info
            
        except Exception as e:
            self.logger.exception(f"教学分析失败: {str(e)}")
            await ctx.error({
                "type": "analysis_error",
                "message": "教学分析过程中发生错误",
                "details": str(e)
            })
            raise
    
    def _parse_ai_response(self, response: str, analysis_id: str) -> AnalysisReport:
        """解析AI响应为结构化报告"""
        # 实现AI响应解析逻辑
        pass

    async def _generate_radar_chart(
        self,
        ai_response: str,
        analysis_id: str,
        ctx: Context
    ) -> Tuple[Optional[str], Optional[str]]:
        """生成雷达图并返回文件路径"""
        try:
            # 从AI响应中提取评分数据
            scores = self.prompt_manager.extract_scores_from_response(ai_response)

            # 验证评分数据
            if not self.chart_generator.validate_scores(scores):
                await ctx.info(ProgressUpdate(
                    status="chart_warning",
                    message="评分数据不完整，使用默认评分生成雷达图"
                ).model_dump())

            # 生成雷达图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            title = f"课堂教学评价雷达图 - {analysis_id}"

            html_path, png_path = self.chart_generator.generate_radar_chart(
                scores, title, timestamp
            )

            await ctx.info(ProgressUpdate(
                status="chart_generated",
                message=f"雷达图生成成功: {html_path}",
                details={"html_path": html_path, "png_path": png_path}
            ).model_dump())

            return html_path, png_path

        except Exception as e:
            self.logger.error(f"雷达图生成失败: {e}")
            await ctx.info(ProgressUpdate(
                status="chart_error",
                message=f"雷达图生成失败: {str(e)}"
            ).model_dump())
            return None, None

    async def _save_analysis_results(
        self,
        report: AnalysisReport,
        usage_info: ModelUsageInfo,
        html_path: Optional[str] = None,
        png_path: Optional[str] = None
    ) -> None:
        """保存分析结果到文件（包含雷达图路径）"""
        # 实现文件保存逻辑，包含雷达图文件路径信息
        pass
```

## 7. 模块集成策略

### 7.1 依赖关系
```
main.py
├── config.py (配置管理)
├── logger.py (日志系统)
├── models.py (数据模型)
├── ai_client.py (AI客户端)
├── prompt_manager.py (提示词管理)
└── analyzer.py (分析服务)
```

### 7.2 初始化顺序
1. 加载配置 (config.py)
2. 初始化日志 (logger.py)
3. 创建AI客户端 (ai_client.py)
4. 初始化分析服务 (analyzer.py)
5. 注册MCP工具 (main.py)

### 7.3 错误传播
- 配置错误：立即终止启动
- AI服务错误：通过MCP上下文报告
- 文件系统错误：记录日志并报告
- 解析错误：提供详细错误信息
